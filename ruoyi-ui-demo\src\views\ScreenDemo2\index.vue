<template>
  <div>
    <div class="ibg" v-if="(settings.dashboard_display.indexOf('2D') >= 0)"
         :style="{backgroundImage:'url('+settings.dashboard_bg_img+')'}"></div>

    <div class="co2View co2_container">
      <ScreenTop @changeMenu="changeMenu" />

      <transition mode="out-in">
        <component :is="viewType" class="co2_main" >
          <slot />
        </component>
      </transition>
    </div>

  </div>
</template>

<script>
import ScreenTop from "@/views/components/cO2View/components/ScreenTop";

import EnergyView from "./components/energy"; // 能耗首页
import DeviceView from "./components/device"; // 设备首页
import DemandView from "./components/demand"; // 需求测响应 虚拟电厂
import DevopsView from "./components/devops"; // 运维首页
import EnergySaveView from "./components/energySave"; // 节能首页
import SecurityView from "./components/security"; // 安全首页
import ComprehensiveSituation from "./components/comprehensiveSituation"; // 安全首页
export default {
  mixins: [],
  components: {
    ScreenTop,

    EnergyView,
    DeviceView,
    DemandView,
    EnergySaveView,
    SecurityView,
    DevopsView,
    ComprehensiveSituation

  },
  props: {
  },
  data () {
    return {
      viewType: null,
      settings: this.gf.projectSettings(),
    }
  },
  created() {
  },
  methods: {
    changeMenu(view) {
      this.viewType = view;
    }
  }
}
</script>
