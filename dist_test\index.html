<!DOCTYPE html><html lang=""><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link rel="icon" href="./favicon.ico"><script src="./js/config.js"></script><script src="./js/jquery-3.3.1.min.js"></script><script src="./js/UE4.js"></script><style>@font-face {
        font-family: "Source Han Sans SC";
        src: url("./font/SourceHanSans-Regular.ttc");
      }
      @font-face {
        font-family: "YouSheBiaoTiHei";
        src: url("./font/YouSheBiaoTiHei-2.ttf");
      }
      @font-face {
        font-family: "DINCond";
        src: url("./font/DINCond-Regular.otf");
      }

      @font-face {
        font-family: "FZZongYi-M05S";
        src: url("./font/FZZongYi-M05S.ttf");
      }
      @font-face {
        font-family: "pangmenzhendao";
        src: url("./font/PangMenZhengDaoBiaoTiTi-1.ttf");
      }
      @font-face {
        font-family: "PangMenZhengDao";
        src: url("./font/paomenzhengdao.ttf");
      }
      @font-face {
        font-family: "DOUYU";
        src: url("./font/douyu.otf");
      }
      @font-face {
        font-family: "DingTalk JinBuTi";
        src: url("./font/DingTalkJinBuTi.ttf");
      }</style><title id="title"></title><link href="./css/app.bd3415c7.css" rel="preload" as="style"><link href="./css/chunk-vendors.45e38c6d.css" rel="preload" as="style"><link href="./js/app.12865c23.js" rel="preload" as="script"><link href="./js/chunk-vendors.bb995edc.js" rel="preload" as="script"><link href="./css/chunk-vendors.45e38c6d.css" rel="stylesheet"><link href="./css/app.bd3415c7.css" rel="stylesheet"></head><body><noscript><strong>We're sorry but doesn't work properly without JavaScript enabled. Please enable it to continue.</strong></noscript><div id="app"></div><script src="lib/echarts.min.js"></script><script src="./js/hls.min.js"></script><script>var _hmt = _hmt || [];
      (function () {
        var hm = document.createElement("script");
        hm.src = "https://hm.baidu.com/hm.js?46274a4877030f8a0b194f5eb94ceefd";
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(hm, s);
      })();
      document.getElementById("title").innerHTML = captions;
      window.onload = function () {
        // 禁用文本选择
        document.addEventListener("selectstart", function (e) {
          e.preventDefault();
        });

        // 禁用复制
        document.addEventListener("copy", function (e) {
          e.preventDefault();
        });
      };</script><script src="./js/chunk-vendors.bb995edc.js"></script><script src="./js/app.12865c23.js"></script></body></html>