<template>
  <div class="prototype" style="height: 100%;">
    <h3 v-if="showTitle">
      <span v-if="devices.length > 0" v-text="devices[0].name + ' -- '"></span>
      <span v-if="scence.id > 0" v-text="scence.name"></span>
    </h3>

    <div id="senceOuter" style="clear:both; padding:10px; min-height: 740px; height: calc(100% - 20px); position: relative; z-index: 999;"
        v-if="newScence.id > 0" ref="senceParent">

        <div class="mb10" v-if="isEdit">
          <el-select v-model="addType" size="mini" class="mr10">
            <el-option label="静态文字" value="vtext-1"/>
            <el-option label="静态图片" value="vdecoration-1"/>
            <el-option label="静态图文" value="vdecoration-2"/>
            <el-option label="静态图标(设备)" value="vdecoration-3"/>
            <el-option label="静态图文(设备)" value="vdecoration-4"/>
            <el-option label="动态图标(设备)" value="vicon-1"/>
            <el-option label="动态文字(数据，需翻译)" value="vtext-2"/>
            <el-option label="动态文字(数据，不需翻译)" value="vtext-3"/>
            <el-option label="下拉框" value="vselect-1"/>
            <el-option label="输入框" value="vinput-1"/>
            <el-option label="按钮" value="vbutton-1"/>
            <el-option label="开关按钮" value="vswitch-1"/>
            <el-option label="iframe" value="viframe-1"/>
            <el-option label="动态文字(用能统计，不需翻译)" value="vtext-4"/>
            <el-option label="链接按钮(iframe弹框或页面跳转)" value="vbutton-2"/>
            <el-option label="图表(支持多图表展示，sql数据)" value="vsqlCharts-1"/>
            <el-option label="能耗图表(支持配置多个设备id)" value="venergyCharts-1"/>
          </el-select>
          <el-button size="mini" @click="addJsonItem">添加</el-button>
          <el-button size="mini" @click="saveJson">保存</el-button>
          <el-button size="mini" @click="quitEditJson">退出编辑</el-button>
          <el-button size="mini" @click="showAllNode">{{nodeBgShow ? '隐藏':'显示'}}元素背景</el-button>
        </div>
        <div class="sence" ref="sence" v-if="isEdit" key="isEdit_true">
          <div
            v-bind:is="o.type"
            v-for="(o, ind) in newScence.otherData.objects"
            :data="o"
            :ind="ind"
            :edit="isEdit"
            v-drag="true"
            @updatePosition="updatePosition"
            @dblclick.native="editJsonItem(o,ind)"
            style="cursor: move;"
            :key="`isEdit_true_${nodeBgShow}_${ind}`"
            :emptyClass="nodeBgShow ? 'empty' : ''"
          ></div>
        </div>
      <Base3DContainer
        style="position: absolute; z-index: 99;"
        v-if="params"
        :title="null"
        :params="params"
        :width="params.width"
        :height="params.height"
      />

        <div class="sence" ref="sence" v-if="!isEdit" key="isEdit_false">
          <div
            v-bind:is="o.type"
            v-for="(o, ind) in newScence.otherData.objects"
            :data="o"
            :ind="ind"
            @refreshData="refreshScence"
            @updatePosition="updatePosition"
            @handleClick="handleClick"
            @mousedown="mousedownDisabled"
            @mousemove="mousemoveDisabled"
          ></div>
        </div>

    </div>

    <!-- 弹框 设备详情  -->
    <DeviceDialogSmall
      v-if="activeDevice.id > 0 && activeDeviceSummaryShow && activeDevice.dialogType != 'mult'"
      :width="activeDevice.type.includes('camera') ? '980px' : '650px' "
      :activeDevice="activeDevice"
      :protoTypeDisplay="protoTypeDisplay"
      @handleDeviceViewClose="handleDeviceViewClose"
      @refreshList="getResourceDeviceList"
      @handleBatchStrategySend="handleBatchStrategySend"
      @handleItemDataLock="handleItemDataLock"
      @handleItemDataUnlock="handleItemDataUnlock"
    />

    <el-dialog
      class="deviceMapDialog"
      :modal="false"
      width="650px"
      :close-on-click-modal="false"
      :visible.sync="addJsonItemShow"
      :before-close="cancelJsonItem"
      append-to-body
      v-dialogDrag="true">
      <el-form :model="jsonItemData" label-width="120px">
        <!-- <el-form-item ref="jsonItemForm" label="name" prop="name">
          <el-input v-model="jsonItemData.name"/>
        </el-form-item> -->
        <el-form-item v-if="['vtext-1','vtext-2','vtext-3','vtext-4'].includes(addType)" label="size" prop="size">
          <el-input v-model="jsonItemData.size"/>
        </el-form-item>
        <el-form-item v-if="['vtext-1','vtext-2','vtext-3','vtext-4'].includes(addType)" label="weight" prop="weight">
          <el-input v-model="jsonItemData.weight"/>
        </el-form-item>
        <el-form-item v-if="['vtext-1','vtext-2','vtext-3','vtext-4'].includes(addType)" label="color" prop="color">
          <el-input v-model="jsonItemData.color"/>
        </el-form-item>
        <el-form-item label="zIndex" prop="zIndex">
          <el-input v-model="jsonItemData.zIndex"/>
        </el-form-item>
        <el-form-item v-if="['vtext-1'].includes(addType)" label="data" prop="data">
          <el-input v-model="jsonItemData.data"/>
        </el-form-item>
        <el-form-item v-if="['vdecoration-1','vdecoration-2','vdecoration-3','vdecoration-4','vicon-1','viframe-1','vswitch-1','vbutton-2','vsqlCharts-1', 'venergyCharts-1'].includes(addType)" label="iconSize" prop="iconSize">
          <el-input v-model="jsonItemData.iconSize"/>
        </el-form-item>
        <el-form-item v-if="['vbutton-2'].includes(addType)" label="全屏展示" prop="isFullScreen">
          <el-checkbox v-model="jsonItemData.isFullScreen">是</el-checkbox><span class="ps">（注：勾选后iconSize将无效）</span>
        </el-form-item>
        <el-form-item v-if="['vdecoration-1','vdecoration-2','vdecoration-3','vdecoration-4','vicon-1','viframe-1','vbutton-2'].includes(addType)" label="url" prop="url">
          <el-input v-model="jsonItemData.url"/>
        </el-form-item>
        <el-form-item v-if="['vicon-1','vtext-2','vtext-3','vselect-1','vinput-1','vbutton-1','vswitch-1'].includes(addType)" label="dataId" prop="dataId">
          <el-input v-model="jsonItemData.dataId"/>
        </el-form-item>
        <el-form-item v-if="['vicon-1','vtext-2','vtext-3','vselect-1','vinput-1','vbutton-1','vswitch-1'].includes(addType)" label="dataName" prop="dataName">
          <el-input v-model="jsonItemData.dataName"/>
        </el-form-item>
        <el-form-item v-if="['vbutton-1','vbutton-2'].includes(addType)" label="按钮文字" prop="textName">
          <el-input v-model="jsonItemData.textName"/>
        </el-form-item>
        <el-form-item v-if="['vbutton-1','vbutton-2'].includes(addType)" label="按钮类型" prop="btnType">
          <el-select v-model="jsonItemData.btnType" style="width: 100%">
            <el-option label="primary" value="primary"/>
            <el-option label="success" value="success"/>
            <el-option label="info" value="info"/>
            <el-option label="warning" value="warning"/>
            <el-option label="danger" value="danger"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="['vbutton-1','vbutton-2'].includes(addType)" label="朴素按钮" prop="isPlain">
          <el-checkbox v-model="jsonItemData.isPlain">是</el-checkbox>
        </el-form-item>
        <el-form-item v-if="addType == 'vbutton-1'" label="下发值" prop="val">
          <el-input v-model="jsonItemData.val"/>
        </el-form-item>
        <el-form-item v-if="['vdecoration-3','vdecoration-4'].includes(addType)" label="deviceId" prop="deviceId">
          <el-input v-model="jsonItemData.deviceId"/>
        </el-form-item>
        <el-form-item v-if="['vdecoration-2','vdecoration-3','vdecoration-4'].includes(addType)" label="deviceName" prop="deviceName">
          <el-input v-model="jsonItemData.deviceName"/>
        </el-form-item>
        <el-form-item v-if="['vicon-1','vswitch-1'].includes(addType)" label="urls" prop="urls">
          <el-input type="textarea" v-model="jsonItemData.urls"/>
        </el-form-item>
        <el-form-item v-if="['vdecoration-2','vdecoration-3','vdecoration-4','vbutton-1','vbutton-2'].includes(addType)" label="textStyle" prop="textStyle">
          <el-input type="textarea" v-model="jsonItemData.textStyle"/>
        </el-form-item>
        <el-form-item v-if="['vtext-4','venergyCharts-1'].includes(addType)" label="电表id" prop="eDeviceIds">
          <el-input v-model="jsonItemData.eDeviceIds" placeholder="支持多个id拼接，以逗号分隔"/>
        </el-form-item>
        <el-form-item v-if="['vtext-4'].includes(addType)" label="用能时间" prop="eDisplayType">
          <el-select v-model="jsonItemData.eDisplayType" placeholder="请选择用能时间" style="width: 100%">
            <el-option
              v-for="item in energySummaryValMap"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="['vtext-4'].includes(addType)" label="单位" prop="unit">
          <el-input v-model="jsonItemData.unit" placeholder="请输入单位"/>
        </el-form-item>
        <el-form-item v-if="['vbutton-2'].includes(addType)" label="展示形式" prop="viewWay">
          <el-select v-model="jsonItemData.viewWay" placeholder="请选择展示形式" style="width: 100%">
            <el-option
              v-for="item in viewWayMap"
              :key="item.code"
              :label="item.name"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="['vsqlCharts-1'].includes(addType)" label="图表参数" prop="chartsOptions">
          <el-input class="mt10" type="textarea" v-model="jsonItemData.chartsOptions"/>
          <div style="text-align: right;">
            <el-dropdown size="mini" split-button type="primary" @command="chartsTypeMapHandle">
              选择图表类型
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-for="item in chartsTypeMap" :command="item.params" :key="item.code">{{item.name}}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-form-item>
        <el-form-item label="位置" prop="position">
          <el-input v-model="jsonItemData.position"/>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button type="warning" v-if="addJsonType == 'edit'" @click="copyJsonItem">复 制</el-button>
        <el-button type="danger" v-if="addJsonType == 'edit'" @click="deleteJsonItem">删 除</el-button>
        <el-button @click="cancelJsonItem">取 消</el-button>
        <el-button type="primary" @click="saveJsonItem">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import vinput from "./components/input";
import vicon from "./components/icon";
import vtext from "./components/text";
import vselect from "./components/select";
import vdecoration from "./components/decoration";
import viframe from "./components/iframe";
import vbutton from "./components/button";
import vswitch from "./components/switch";
import vsqlCharts from "./components/sqlCharts";
import venergyCharts from "./components/energyCharts";
import Base3DContainer from "@/views/components/cO2View/components/common/Base3DContainer.vue";

import DisplayByCard from "../Device/DisplayByCard";
import DisplayBaseFunc from "../Device/DisplayBaseFunc";

import DeviceDialogSmall from "@/views/device/monitor/components/Device/DeviceDialogSmall";
import EnergyDialog from "@/views/device/monitor/components/Energy/components/energyDialog"

import {getResource, updateResource} from "@/api/base/resource";
import { resourceDevice, listResourceDevice, } from "@/api/device/apis";
import { deviceSummaryData } from "@/api/energy/apis"
export default {
  name: 'prototype',
  props: {
    deviceId: {
      type: [Number, String],
      default: null,
    },
    resourceId: {
      type: [Number, String],
      default: null,
    },
    showTitle: {
      type: Boolean,
      default: true,
    },
    event: {
      type: Boolean,
      default: true,
      note: "是否出现控制操作内容",
    },
    sliderShow: {
      type: Boolean,
      default: false,
      note: "滑块",
    },
    summaryType: {
      type: String,
      default: "device",
      note: "显示类型",
    },
    defaultScale: {
      type: Number,
      default: 1,
      note: "比例大小",
    },
    autoResize: {
      type: Boolean,
      default: false,
      note: "自动缩放开关",
    },
    "refreshSpan": {
      default: null,
      desc: "定时刷新数据间隔, 单位秒"
    },
    "isEdit": {
      default: false,
      desc: "是否编辑模式"
    },
    refreshFlag: {
      type: Boolean,
      default: false
    }
  },
  components: {
    vinput,
    vicon,
    vtext,
    vselect,
    vdecoration,
    viframe,
    Base3DContainer,
    vbutton,
    vswitch,
    DeviceDialogSmall,
    vsqlCharts,
    venergyCharts,
  },
  mixins: [ DisplayBaseFunc ], // 继承父模块
  data () {
    return {
      //buildingId: this.gf.getBuildingId(), //建筑ID

      // deviceId: this.$route.query.deviceId || 0,
      // resourceId: this.$route.query.resourceId || 0,

      // 设备详情
      activeDeviceInd: -1,
      activeDevice: {},
      activeDeviceSummaryShow: false, // 设备弹框
      activeDeviceDetailShow: false,

      activeEnergySummaryShow: false, // 能耗弹框

      // datas: {},
      devices: [],
      deviceCopy: [],
      deviceSummary: {},
      scence: {},
      newScence: {},

      protoTypeDisplay: '',

      params: null,

      addType: 'vtext-1',
      addJsonItemShow: false,
      addJsonType: 'new',
      //添加的json项数据
      jsonItemData: {
        name: '',
        type: '',
        position: '{"top": "0px", "left": "0px"}',
        zIndex: 100,
        size: '14px',
        weight: 'bold',
        color: '#fff',
        data:'{"dVal":"","dDataUnit":""}',
        iconSize:'{"width":"80px","height":"80px"}',
        url: '',
        deviceId: null,
        dataId: null,
        dataName: '',
        deviceName: '',
        val: null,
        textName: '',
        btnType: 'primary',
        isPlain: false,
        textStyle: '{"fontSize":"12px","weight":"bold","color":"#fff","width":"60px","text-align": "center"}',
        urls: '{"0":"/images/IdcsGif/fan/fanLeftFalse.gif","1":"/images/IdcsGif/fan/fanLeftTrue.gif"}',
        viewWay: '',
        chartsOptions: '',
        isFullScreen: false
      },
      energySummaryValMap: [
        {
          name: "本日",
          code: "today"
        },
        {
          name: "本月",
          code: "curMonth"
        },
        {
          name: "本年",
          code: "curYear"
        },
        {
          name: "昨日",
          code: "yesterday"
        },
        {
          name: "上月",
          code: "lastMonth"
        },
        {
          name: "去年",
          code: "lastYear"
        },
        {
          name: "去年本月",
          code: "lastYearCurMonth"
        },
        {
          name: "累计",
          code: "total"
        },
      ],
      nodeBgShow: false,
      viewWayMap: [
        {
          name: "iframe弹框",
          code: "iframe"
        },
        {
          name: "跳转页面",
          code: "href"
        },
      ],
      chartsTypeMap: [
        {
          name: "柱状图",
          code: "bar",
          params: {
            options: {
              xAxis: {
                type: 'category',
                data: []
              },
              yAxis: {
                type: 'value'
              },
              series: [
                {
                  data: [],
                  type: 'bar'
                }
              ],
              tooltip: {
                trigger: 'axis'
              },
              title: {
                text: '柱状图标题',
                subtext: '柱状图副标题',
                left: 'center'
              },
              grid: {
                top: 60,
                left: 10,
                right: 10,
                bottom: 10,
                containLabel : true
              },
              legend: {
                type: 'scroll',
                orient: 'horizontal',
                top: 28
              },
            },
            sql: [],
            tabName: "标签名",
            type: "bar"
          }
        },
        {
          name: "折线图",
          code: "line",
          params: {
            options: {
              xAxis: {
                type: 'category',
                data: []
              },
              yAxis: {
                type: 'value'
              },
              series: [
                {
                  data: [],
                  type: 'line'
                }
              ],
              tooltip: {
                trigger: 'axis'
              },
              title: {
                text: '折线图标题',
                subtext: '折线图副标题',
                left: 'center'
              },
              grid: {
                top: 60,
                left: 10,
                right: 10,
                bottom: 10,
                containLabel : true
              },
              legend: {
                type: 'scroll',
                orient: 'horizontal',
                top: 28
              },
            },
            sql: [],
            tabName: "标签名",
            type: "line"
          }
        },
        {
          name: "饼图",
          code: "pie",
          params: {
            options: {
              tooltip: {
                trigger: 'item'
              },
              legend: {
                type: 'scroll',
                orient: 'horizontal',
                top: 26
              },
              series: [
                {
                  name: '',
                  type: 'pie',
                  data: [],
                  center: ['50%', '55%'],
                  radius: ["60%", "75%"],
                  label: {
                    show: false
                  }
                }
              ],
              xAxis: {
                show: false
              },
              title: {
                text: '饼图标题',
                subtext: '饼图副标题',
                left: 'center'
              }
            },
            sql: [],
            tabName: "标签名",
            type: "pie"
          }
        },
      ],
    };
  },
  computed: {
    // 筛选设备
    filterDevice() {
      return this.deviceList;
    }
  },
  created () {
    window.vue = this;
    this.$bus.$on("sceneObjectOnMouseOver", (data) => {
      console.log("sceneObjectOnMouseOver", data);
    });
    this.$bus.$on("sceneObjectOnClick", (data) => {
      console.log("sceneObjectOnClick", data);
      let device = this.devices.map((d, i) => {
        if(d.id == data.extraData.deviceCode) {
          this.handleDeviceShow(d, i);
        }
      });
    });
    // 绑定隐藏物体事件
    this.$bus.$on("sceneOnLoad", () => {
      if(this.newScence.otherData.hideObjs) {
        this.$bus.$emit("hideObjects", this.newScence.otherData.hideObjs);
      }
      // 更新场景内数据
      this.update3dScene();
    });
  },
  updated() {
    console.log("updated")
  },
  watch: {
    refreshSpan: {
      immediate: true,
      handler(newValue,oldValue){
        console.log("===========> refreshSpan ", this.refreshSpan);
        this.updateAutoRefresh();
      }
    },
    autoResize: {
      handler(newValue,oldValue){
        if(newValue) {
          this.resizeResourceScale();
        } else {
          this.initResourceScale();
        }
      }
    },
    isEdit: {
      handler(newValue,oldValue){
        this.drawScence();
      }
    },
    refreshFlag: {
      handler () {
        this.update3dScene();
      },
      immediate: true
    }
  },
  mounted() {
    console.log("mounted", this.$data);
    // 绑定窗口改变大小事件
    this.getDatas();
    this.iframeListener();
  },
  destroyed() {
    this.$bus.$off("sceneObjectOnMouseOver");
    this.$bus.$off("sceneObjectOnClick");
    this.$bus.$off("sceneOnLoad");
    // 销毁对象
    this.activeDevice = {};
    this.activeDeviceInd = -1;
    this.params = null;
  },
  methods: {
    // 重写函数
    async getResourceDeviceList() {
      await this.getDeviceDetail();
      this.drawScence();
      console.log("Prototype map getResourceDeviceList", this.devices);
      const iframes = document.querySelectorAll('iframe');
      console.log(iframes, 'iframe');
      // 遍历每个 iframe，并监听 onload
      iframes.forEach((iframe) => {
        const sendMessage = () => {
          if (iframe.contentWindow) {
            try {
              iframe.contentWindow.postMessage(
                { type: "activeDevice", data: this.activeDevice },
                "*"
              );
            } catch (err) {
              console.error("postMessage failed:", err);
            }
          }
        };

        // 如果 iframe 已加载，直接发送
        if (iframe.contentDocument?.readyState === 'complete') {
          sendMessage();
        }
        // 否则监听 onload
        else {
          iframe.onload = sendMessage;
        }
      });
    },

    // 关闭弹框
    handleDeviceSummaryViewClose() {
      this.activeDevice = {};
      this.activeDeviceInd = -1;
      this.activeDeviceSummaryShow = false;
    },

    // 设备详情
    async getDeviceDetail() {
      this.loading = true;
      this.devices = [];
      let response = null
      if(!this.deviceId) {
        response = await listResourceDevice({resourceId: this.resourceId});
        this.loading = false;
        console.log(response);
        for(let i=0; i< response.data.length; i++) {
          let d = response.data[i];
          let opts = await this.gf.getDeviceListDisplayConfig(d.type);
          this.devices.push({
            ...d,
            ...{
              showImages: opts.showImages,
              hasLock: opts.hasLock,
              hasEnergy: opts.hasEnergy,
              protoTypeDisplay: opts.protoTypeDisplay,
            },
          });
        }
        this.deviceSummary = this.summaryDevice(this.devices);
      } else {
        response = await resourceDevice({resourceId: this.resourceId, deviceId: this.deviceId});
        this.loading = false;
        console.log(response);
        let d = response.data;
        if(d) {
          let opts = await this.gf.getDeviceListDisplayConfig(d.type);
          this.devices.push({
            ...d,
            ...{
              showImages: opts.showImages,
              hasLock: opts.hasLock,
              hasEnergy: opts.hasEnergy,
              protoTypeDisplay: opts.protoTypeDisplay,
            },
          });
        }
        this.summaryDevice(this.devices);
        this.activeDeviceInd = 0
      }
      // 如果有选中，则也一起刷新
      if(this.activeDeviceInd >= 0) {
        this.activeDevice = this.devices[this.activeDeviceInd];
        this.item = this.activeDevice
      }
      // 有iframe时，postMessage
      this.postIframeMessage(this.devices)
    },
    // 图列表
    async getDatas() {
      this.loading = true;
      await this.gf.getDicts("d_device_list_display"); // 缓存所有设备显示选项，给下面调用提供数据
      if(!this.resourceId) {
        return;
      }
      let response = await getResource(this.resourceId);
      this.scence = response.data;
      // 尝试读取json数据
      if(this.scence.otherData != null && this.scence.otherData != "") {
        let od = this.scence.otherData;
        try {
          od = JSON.parse(this.scence.otherData);
          // 如果一次不行，在parse一次
          if(typeof od == "string") {
            od = JSON.parse(od);
          }
          console.log(this.scence);
        } catch(ex) {
          // pass
          console.error("------ getResource error", ex);
        }
        this.scence.otherData = od;
      }
      this.newScence = JSON.parse(JSON.stringify(this.scence));
      console.log(this.newScence);
      await this.getDeviceDetail();
      this.loading = false;
      // 绘制图
      this.drawScence();
    },
    // 设备数据和原始场景图数据合并, 生成原理图需要的数据
    async mergeData(deviceDetails, scence) {
      let newScence = JSON.parse(JSON.stringify(scence));
      deviceDetails.map( deviceDetail => {
        let deviceDatas = deviceDetail.deviceDataBase.concat(deviceDetail.deviceDataEnv);
        if(deviceDatas.length > 0 && scence.id > 0) {
          for(var i=0; i<newScence.otherData.objects.length; i++) {
            let obj = newScence.otherData.objects[i];
            // 非装饰图片, 全部绑定数据
            if(obj.type != "decoration") {
              // 绑定设备
              if(obj.hasOwnProperty("deviceId") && obj.deviceId == deviceDetail.id) {
                obj.deviceName = deviceDetail.name;
                obj.deviceDetail = deviceDetail;
              }
              // 绑定数据
              deviceDatas.map( d => {
                // 绑定设备点数据 如果有重名的，需要单独配置数据点 dataId 信息
                if(obj.hasOwnProperty("dataId") && obj.dataId) {
                  if(d.dmName == obj.dataName && d.dId == obj.dataId) {
                    // 默认为值+单位
                    d.valStr = d.dVal + ' ' + d.dDataUnit;
                    // drOtherData 转 map
                    if(d.dOtherData.trim() != "") {
                      d.dValMap = this.gf.fmtDataMap(d.dOtherData);
                      d.valStr = d.dValMap[d.dVal] || "";
                    }
                    obj.data = d;
                    obj.device = deviceDetail;
                  }
                } else {
                  // 普通情况，单个设备的数据名可作为唯一性判断
                  if(d.dmName == obj.dataName) {
                    // 默认为值+单位
                    d.valStr = d.dVal + ' ' + d.dDataUnit;
                    // drOtherData 转 map
                    if(d.dOtherData.trim() != "") {
                      d.dValMap = this.gf.fmtDataMap(d.dOtherData);
                      d.valStr = d.dValMap[d.dVal] || "";
                    }
                    obj.data = d;
                    obj.device = deviceDetail;
                  }
                }
              });
            }
          }
        }
      });
      //vtext-4  单独从接口获取数据
      const promiseArr = [];
      newScence.otherData.objects.forEach((item,index) => {
          if(item.eDeviceIds){
            let pormiseItem = deviceSummaryData({
              deviceIds: item.eDeviceIds,
            }).then(res => {
              return {
                data: res.data[0],
                index
              }
            })
            promiseArr.push(pormiseItem);
          }
      })
      if(promiseArr.length > 0){
        await Promise.all(promiseArr).then(res => {
          res.forEach(item => {
            let dt = newScence.otherData.objects[item.index];
            dt.data = {
              dVal: item.data[dt.eDisplayType],
              dDataUnit: dt.unit
            };
          })
        })
        console.log("newScence", newScence);
        return newScence;
      } else {
        console.log("newScence", newScence);
        return newScence;
      }
    },

    // 绘制原理图
    async drawScence() {
      console.log("sp3d drawScence")
      // 复制一份data数据
      // this.deviceCopy = JSON.parse(JSON.stringify(this.devices));
      let obj = await this.mergeData(this.devices, this.scence);
      this.newScence = obj;

      var $dom = this.$refs.sence.parentElement;
      var domWidth = $dom.offsetWidth - 20;
      var domHeight = $dom.offsetHeight;

      this.params = {
        width: domWidth + "px",
        height: domHeight + "px",
        modelPath: this.newScence.val,
        hideObjs: this.newScence.otherData.hideObjs || [],
      }
      // 更新3d场景模型样式
      // setTimeout(() => {
      //   this.update3dScene();
      // }, 1500);
    },

    update3dScene() {
      console.log("update3dScene", this.devices);
      // 更新设备状态
      let deviceStatus = {
        "type": "deviceStatus",
        "data": {}
      };
      // 更新面板
      let deviceData = {
        "type": "deviceData",
        "data": {},
      };
      // 设备动画
      let deviceAnimation = {
        "type": "deviceAnimation",
        "data": {},
      };
      // 设备细节属性显示状态
      let deviceProps = {
        "type": "deviceProps",
        "data": {},
      };

      // 单个设备组态图 和 多个设备的系统图，绑定的参数有区别
      this.devices.map(d => {
        deviceStatus.data[d.id] = d.status;   // 系统图用
        deviceStatus.data.deviceId = d.status;   // 编码写成 deviceId 通用设备运行状态
        deviceData.data[d.id] = {};
        deviceData.data.deviceId = {};       // 原理图用, 编码写成 deviceId 通用
        d.deviceDataBase.map(dd => {
          deviceData.data[d.id][dd.dmName] = dd.valStr; // 系统图用
          deviceData.data.deviceId[dd.dmName] = dd.valStr;  // 原理图用

          // 尝试转换成动画/属性输出
          let act = null;
          if (dd.dmNote) {
            try {
              let f = new Function(dd.dmNote);
              act = f()(dd.dVal);
            } catch (e) {
              // pass
            }
            // 多设备系统图用
            deviceAnimation.data[d.id] = {
              "name": dd.dmName,
              "action": act
            }
            deviceProps.data[d.id] = {
              "name": dd.dmName,
              "action": act
            }
            // 设备原理图用
            deviceAnimation.data[dd.dmName] = {
              "name": dd.dmName,
              "action": act
            }
            deviceProps.data[dd.dmName] = {}
            deviceProps.data[dd.dmName][dd.dmName] = act
          }
        });
      });

      console.log("update3dScene", "deviceStatus", deviceStatus);
      this.$bus.$emit('updateScene', JSON.stringify(deviceStatus));
      console.log("update3dScene", "deviceData", deviceData);
      this.$bus.$emit('updateScene', JSON.stringify(deviceData));
      console.log("update3dScene", "deviceProps", deviceProps);
      this.$bus.$emit('updateScene', JSON.stringify(deviceProps));
      console.log("update3dScene", "deviceAnimation", deviceAnimation);
      this.$bus.$emit('updateScene', JSON.stringify(deviceAnimation));
    },

    // 刷新场景
    refreshScence() {
      this.newScence.otherData.objects = [];
      this.activeDeviceInd = -1;
      this.activeDevice = {};
      this.activeDeviceSummaryShow = false;
      this.activeDeviceDetailShow = false;
      this.activeEnergySummaryShow = false;
      this.getDatas();
    },

    handleClick(pos) {
      console.log("handleClick show detail", pos);
      // 默认设备详情
      if(this.scence.otherData.objects[pos.ind].hasOwnProperty("deviceId") && this.scence.otherData.objects[pos.ind]["deviceId"] > 0) {
        this.devices.map( (d, ind) => {
          if(d.id == this.scence.otherData.objects[pos.ind]["deviceId"]) {
            // 判断是打开原型图还是显示详情弹框
            // linkType: chart / detail
            if(this.scence.otherData.objects[pos.ind]["linkType"] == "chart" && d.resourceId) {
              const { href } = this.$router.resolve({
                path: `/deviceMgt/device/prototype`,
                query: {
                  deviceId:d.id,
                  resourceId:d.resourceId,
                }
              })
              window.open(href, 'prototypePage');
            } else {
              this.activeDeviceInd = ind;
              this.activeDevice = d;
              this.activeDeviceSummaryShow = true;
            }
          }
        });
      } else if(this.scence.otherData.objects[pos.ind].hasOwnProperty("params")
        && !this.gf.isEmptyObject(this.scence.otherData.objects[pos.ind]["params"])) {
        // 用能详情
        this.activeDeviceInd = pos.ind;
        this.activeDevice = this.scence.otherData.objects[pos.ind];
        this.activeEnergySummaryShow = true;
      }
    },

    // 查看设备 (覆盖mixins)
    handleDeviceShow(item, ind) {
      this.activeDeviceInd = ind;
      this.activeDevice = item;
      this.activeDeviceSummaryShow = true;
      this.acticeName = item.name;
      //this.getDeviceChartDatas();  // 重新获取总用时能耗
    },
    handleDeviceViewClose() {
      this.activeDeviceInd = -1;
      this.activeDevice = {};
      this.activeDeviceSummaryShow = false;
      this.acticeName = "总";
      //this.getDeviceChartDatas();  // 重新获取总用时能耗
    },
    handleEnergyViewClose() {
      this.activeDeviceInd = -1;
      this.activeDevice = {};
      this.activeEnergySummaryShow = false;
      this.acticeName = "总";
      //this.getDeviceChartDatas();  // 重新获取总用时能耗
    },
    // 查看设备详情
    handleDeviceDetailShow(item, ind) {
      console.log(arguments);
      this.activeDeviceInd = ind;
      this.activeDevice = item;
      this.activeDeviceDetailShow = true;  // 显示弹框
      this.activeDeviceSummaryShow = false;
    },
    // 关闭查看详情
    handleDeviceDetailClose() {
      this.activeDeviceInd = -1;
      this.activeDevice = {};
      this.activeDeviceDetailShow = false; // 关闭弹框
    },
    showAllNode() {
      this.nodeBgShow = !this.nodeBgShow;
      this.drawScence()
    }
  },
}
</script>

<style scoped lang="scss">
.sence > * { position: absolute;  }
::v-deep .deviceMapDialog .el-dialog__footer{
  background: rgba(53, 84, 121, 0.3);
}
.empty {
  min-width: 40px;
  min-height: 10px;
  background-color: #ffffff30;
}
.ps {
  color: #ffffffbe;
  font-size: 12px;
}
</style>

