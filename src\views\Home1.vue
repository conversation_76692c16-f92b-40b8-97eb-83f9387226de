<template>
  <div v-show="shouall">
    <FitScreen :width="1920" :height="1080">
      <!-- <iframe src="http://139.196.200.244:8060/#/login?autoLogin=1&username=admin&password=lanxing121%21" frameborder="0"
        v-show="false"></iframe> -->
      <iframe id="myIframe" :src="`${iframeUrl}/#/login?autoLogin=1&username=admin&password=lanxing121%21`"
        frameborder="0" v-show="false">
      </iframe>
      <transition name="expand" mode="out-in">
        <iframe class="sbiframe" id="sbiframe" v-show="issbiframe"
          src="https://qiye.3dzhanting.cn/share-project.html?ids=hMZ6Lw4fiu2iHLp5PIYD3A==" frameborder="0">
        </iframe>
      </transition>
      <img class="close" @click="closesbiframe" v-if="issbiframe" src="../assets/close.png" alt="">
      <div class="container">
        <div class="bg"></div>
        <component v-if="true" @seedsb="seedsb" :is="componentTag" ref="childComponent" :sblist="sblist"
          :sbtitle="sbtitle"></component>
        <!-- 
        <iframe id="ifram" ref="mainIframe" class="iframe" name="mainIframe" src="https://3d.dddtask.cn/enginner-xufeng/bacheng3D/index.html"
          frameborder="0"></iframe> -->
        <!-- 头部内容 -->
        <div class="head">
          <p class="title">{{ captions }}</p>
        </div>
        <div class="now-time">

          <img class="img" src="../assets/image/log.png" alt="" />
          <span>{{ timeStr }}</span>
          <!--<span>2023-03-19 15:45:25</span>-->
        </div>
        <div class="select" v-if="false">
          <div class="el-select" placeholder="selectvalue" @click="toggleContent">
            <!-- 可以添加一个指示器，显示当前状态，比如箭头或加号/减号 -->
            <span class="pp">{{ selectvalue }}{{ selectvalue1 }}</span>
            <span class="sp" v-if="showlist">▲</span>
            <span class="sp" v-else>▼</span>
          </div>
          <transition name="expand1" @before-enter="beforeEnter" @enter="enter" @before-leave="beforeLeave"
            @leave="leave">
            <div class="content" v-show="showlist">
              <div v-for="(item, index) in floorlist" :key="index">
                <div class="butn" @click="sendlou(item.name)">
                  {{ item.name }}
                </div>
                <div class="btnbtn" v-for="(item1, index1) in floorlist[index].floor" :key="index1">
                  <div class="btn1" @click="sendTofloor(item1.name)">
                    {{ item1.num }}
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>

        <div class="xuhua" v-if="true" @click="sendxs()">
          {{ modelname }}
        </div>
        <div class="floorcd" v-if="true">
          <!-- <p class="tit">楼栋</p> -->
          <div @click="selectbuild(item, index1)" :class="floorindex1 == index1 ? 'flist1' : 'flist'"
            v-for="(item, index1) in fllist" :key="index1" v-show="!hideBuildings || item.name === '整体建筑'">{{
              item.name }}</div>

        </div>
        <div class="floorcd1" v-if="showfloor">
          <!-- <p class="tit">{{ fllist[floorindex].name }}</p> -->
          <!-- <img class="return" @click="returnbuild()" src="../assets/image/return.png" alt=""> -->
          <div class="flist" @click="selectfloor(item, index1, fllist[floorindex].name, fllist[floorindex].list.length)"
            v-for="(item, index1) in fllist[floorindex].list" :key="index1"
            :class="floorindex2 == index1 ? 'flist1' : 'flist'">{{ item }}</div>

        </div>
        <div class="groups" v-if="false">
          <el-radio-group v-model="isCollapse" style="margin-bottom: 20px">
          </el-radio-group>
          <el-menu default-active="2" class="el-menu-vertical-demo" :collapse="isCollapse" background-color="#12355D"
            text-color="#00e5ff" active-text-color="#00e5ff" @open="handleOpen" @close="handleClose"
            :unique-opened="true">
            <el-sub-menu v-for="(building, buildingIndex) in setlist" :key="buildingIndex"
              :index="`${buildingIndex + 1}`">
              <template #title>
                <span class="subitem">{{ building.name }}</span>
              </template>
              <el-menu-item-group>
                <el-menu-item v-for="(floor, floorIndex) in building.child" :key="floorIndex"
                  @click="loucxuanz(floorIndex, building.name + floor.title)"
                  :index="`${buildingIndex + 1}-${floorIndex + 1}`">
                  {{ floor.title }}
                </el-menu-item>
              </el-menu-item-group>
            </el-sub-menu>
            <!-- <el-sub-menu index="2">
              <template #title>
                <span class="subitem">2楼</span>
              </template>
              <el-menu-item-group>
                <el-menu-item index="2-1">B1</el-menu-item>
                <el-menu-item index="2-2">B2</el-menu-item>
              </el-menu-item-group>
            </el-sub-menu>
            <el-sub-menu index="3">
              <template #title>
                <span class="subitem">3楼</span>
              </template>
              <el-menu-item-group>
                <el-menu-item index="3-1">B1</el-menu-item>
                <el-menu-item index="3-2">B2</el-menu-item>
              </el-menu-item-group>
            </el-sub-menu> -->
          </el-menu>
        </div>
        <div class="btt">
          <button class="btt1" @click="changetqlist">
            <img class="ttqq" src="../assets/img/tq.png" alt="" />
          </button>
          <button class="btt1" @click="seedfw()">
            <img src="../assets/image/flooricon.png" alt="" />
          </button>

          <button class="btt1">
            <img class="imgg" src="../assets/image/huihua.png" alt="" />
          </button>
          <button class="btt1">
            <img class="imgg" src="../assets/image/tuichu.png" alt="" />
          </button>
        </div>
        <!-- 底部菜单 -->
        <div class="bot">
          <div class="bot1" v-for="(item, index) in botlist" :key="index" @mouseleave="closeBot"
            @mouseenter="openBot(index, $event)" @click="selectBot(index, item)">
            <div :class="selectedIndex == index ? 'activeimg' : 'img'">
              <img :src="require(`../assets/image/b${selectedIndex == index ? '' : 'a'
                }${index + 1}.png`)
                " alt="" />
            </div>
            <p :class="selectedIndex == index ? 'p2' : 'p1'">{{ item.name }}</p>
          </div>
        </div>
        <div class="fuwei" @click="seed('fuwei')" v-if="false">
          <img src="../assets/image/fuwei.png" alt="" />
          <p>复位</p>
        </div>

        <div class="caidan" v-show="fwshow" v-if="false">
          <img src="../assets/image/home.png" @click="show4 = !show4" class="cdimg" alt="" />
          <el-collapse-transition>
            <div class="cd" v-show="show4">
              <img src="../assets/image/home1.png" alt="" @click="seed('fuwei')" />
              <img src="../assets/image/home2.png" @click="changela()" alt="" />
            </div>
          </el-collapse-transition>
        </div>
        <div class="caidanlist" v-show="false">
          <div class="caidanimg1" @click="show3 = !show3">
            <img class="caidanicon1" src="../assets/image/caidanicon1.png" alt="" />
            <div class="caifonsiez">B3栋</div>
            <img class="caixiangxia" src="../assets/image/caixiangxia.png" alt="" />
          </div>
          <el-collapse-transition>
            <div v-show="show3">
              <div class="list" v-for="(item, index) in loulist" :key="index" :class="{ active: xuanzindex === index }"
                @click="loucxuanz(index, '空调箱b3栋' + item.title)">
                {{ item.title }}
              </div>
            </div>

          </el-collapse-transition>
        </div>
      </div>
      <!-- 中间内容 -->
      <el-collapse-transition>
        <div class="boxtap boxt" @mouseenter="cancelClose" @mouseleave="closeBot1" v-show="fenyeTag === 'component0'">
          <div class="tapli" v-for="(item, index) in ['漫游管理', '空间管理', '大屏显示']" :key="item"
            :style="{ color: index === resIndex0 ? '#14fcd5' : '' }" @click="resIndex0 = index, setoptions(item)">
            {{ item }}
          </div>
        </div>
      </el-collapse-transition>
      <el-collapse-transition>
        <div class="boxtap1 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1" v-show="fenyeTag === 'component1'">
          <div class="tapli" v-for="(item, index) in items" :style="{ color: index === resIndex1 ? '#14fcd5' : '' }"
            :key="index" @click="(resIndex1 = index), seedchild(index)">
            {{ item.title }}
          </div>
        </div>
      </el-collapse-transition>
      <el-collapse-transition>
        <div class="boxtap2 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1" v-show="fenyeTag === 'component2'">
          <div class="tapli" v-for="(item, index) in [
            '用电概览',
            '分项用电',
            '分区用电',
            '用电报表',
            '用电拓扑图',
            '用水概览',
            '分项用水',
            '分区用水',
            '用水报表',
            '用水拓扑图',
            '异常用能报警',
          ]" :style="{ color: index === resIndex2 ? '#14fcd5' : '' }" :key="item"
            @click="resIndex2 = index, seednengyuan(index)">
            {{ item }}
          </div>
        </div>
      </el-collapse-transition>
      <el-collapse-transition>
        <div class="boxtap3 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1" v-show="fenyeTag === 'component3'">
          <div class="tapli" v-for="(item, index) in [
            '停车场管理',
            '无线对讲',
            '电子巡更',
            '入侵报警',
            '门禁管理',
            '视频监控',
          ]" :style="{ color: index === resIndex3 ? '#14fcd5' : '' }" :key="item"
            @click="resIndex3 = index, seedanquan(index)">
            {{ item }}
          </div>
        </div>
      </el-collapse-transition>
      <el-collapse-transition>
        <div class="boxtap4 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1" v-show="fenyeTag === 'component4'">
          <div class="tapli" v-for="(item, index) in [
            '资产档案',
            '报警管理',
            '维修管理',
            '保养管理',
            '巡检管理',
            '排班管理',
            '资料管理',

          ]" :style="{ color: index === resIndex4 ? '#14fcd5' : '' }" :key="item"
            @click="resIndex4 = index, seedyunwei(index)">
            {{ item }}
          </div>
        </div>
      </el-collapse-transition>

      <!-- <div class="boxtap5" v-if="fenyeTag === 'component4'">
        <div class="tapli" v-for="item in ['停车场管理']" :key="item">
          {{ item }}
        </div>
      </div> -->
      <!-- <div class="boxtap6" v-if="fenyeTag === 'component5'">
        <div class="tapli" v-for="item in ['楼栋招商管理']" :key="item">
          {{ item }}
        </div>
      </div> -->
      <div class="content1" v-if="showtq">
        <div class="xuanzeqi">
          <p class="pp">时间与现实同步</p>
          <input class="switch-btn switch-btn-animbg" v-model="isChecked" @change="handleCheckboxChange" type="checkbox"
            checked />
        </div>
        <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
          <div class="tianqi" v-for="(item, index) in tqlist" :key="index" @click="tqchange1(index, item.time)">
            <img class="img" :src="require(`../assets/img/${tq1 == index ? 'tianqi1' : 'tianqi'
              }/tianqi${index + 1}.png`)
              " alt="" />
            <p class="time">{{ item.time }}</p>
          </div>
        </div>
        <div class="xuanzeqi">
          <p class="pp">天气设置</p>
        </div>
        <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
          <div class="tianqi" v-for="(item, index) in tqlist1" :key="index" @click="tqchange(index, item.time)">
            <img class="img" :src="require(`../assets/img/${tq2 == index ? 'tianqi1' : 'tianqi'
              }/tianqi${index + 5}.png`)
              " alt="" />
            <p class="time">{{ item.time }}</p>
          </div>
        </div>
        <!-- <label><input class="switch-btn switch-btn-animbg" type="checkbox" checked> 默认选中</label> -->
      </div>
    </FitScreen>
  </div>
</template>
<script>
import Title from "@/components/common/Title.vue";
import Title1 from "@/components/common/Title1.vue";
import component1 from "@/views/index.vue";
import component2 from "@/views/device.vue";
// import component13 from "@/views/peidian.vue";
import component3 from "@/views/nengyuan.vue";
import component4 from "@/views/anquan.vue";
import component5 from "@/views/yunwei.vue";
// import component5 from "@/views/moduan.vue";
// import component7 from "@/views/zhenkong.vue";
// import component8 from "@/views/centerAc.vue";
// import component9 from "@/views/feipai.vue";
// import component10 from "@/views/reshui.vue";
// import component12 from "@/views/zilaishui.vue";
// import component2 from "@/views/dianbiao.vue";
// import component6 from "@/views/kongya.vue";

// import component21 from "@/views/nenghao.vue";
import axios from "axios";
import { log } from '../../../ruoyi-ui-demo/src/components/byEditor/js/utils/mqtt';
export default {
  components: {
    component3,
    component2,
    component1,
    // component2,
    // component3,
    component4,
    component5,
    // component6,
    // component8,
    // component9,
    // component10,
    // component12,
    // component13,
    // component3,
    // component11,
    // component7,
    Title,
    Title1,

  },

  data() {
    return {
      modelname: '虚化',
      alldeviceList: [

      ],
      issbiframe: false,
      hideBuildings: false, // 控制是否隐藏其他楼栋
      iframeLoaded: false,
      iframeUrl,
      tqlist: [
        {
          time: "7:00",
        },
        {
          time: "12:00",
        },
        {
          time: "17:00",
        },
        {
          time: "22:00",
        },
      ],
      isChecked: true,
      tqlist1: [
        {
          time: "晴朗",
        },
        {
          time: "多云",
        },
        {
          time: "下雨",
        },
        {
          time: "下雪",
        },
      ],
      showtq: false,
      weather: '晴朗',
      weatherstatus: '开',
      dsweather: '',

      tq1: 0,
      tq2: 0,
      shouall: false,
      items: [
        {
          title: "机房动环",
          type: "shiDuChuanGanQi",
        },
        {
          title: "电梯管理",
          type: "DT",
        },
        {
          title: "照明系统",
          type: "ZM1",
        },
        {
          title: "电力监控",
          type: "DLJKXT",
        },
        // {
        //   title: "冷热源",
        //   type:'',
        // },
        {
          title: "给排水",
          type: "JPSGL",
        },
        {
          title: "通风机",
          type: "SPFGL",
        },
        {
          title: "新风空调",
          type: "XFKTGL  ",
        },
      ],
      loading: true,
      setlou: false,
      lablevalue: false,
      fenyeTag: "",
      isseed: false,//是否发送数据
      loulist: [
        {
          title: "整体",
        },
        {
          title: "13F",
        },
        {
          title: "12F",
        },
        {
          title: "11F",
        },
        {
          title: "10F",
        },
        {
          title: "9F",
        },
        {
          title: "8F",
        },
        {
          title: "7F",
        },
        {
          title: "6F",
        },
        {
          title: "5F",
        },
        {
          title: "4F",
        },
        {
          title: "3F",
        },
        {
          title: "2F",
        },
        {
          title: "1F",
        },
      ],
      setlist: [
        {
          name: 'B2栋',
          child: [{
            title: '整体'
          }, {
            title: '顶楼'
          }, {
            title: '4F'
          }, {
            title: '3F'
          }, {
            title: '2F'
          }, {
            title: '1F'
          }]
        },
        {
          name: "A1栋",
          child: [
            {
              title: "整体",
            },
            {
              title: "13F",
            },
            {
              title: "12F",
            },
            {
              title: "11F",
            },
            {
              title: "10F",
            },
            {
              title: "9F",
            },
            {
              title: "8F",
            },
            {
              title: "7F",
            },
            {
              title: "6F",
            },
            {
              title: "5F",
            },
            {
              title: "4F",
            },
            {
              title: "3F",
            },
            {
              title: "2F",
            },
            {
              title: "1F",
            },
          ],
        },
      ],
      show4: false,
      xuanzindex: "",
      show3: true,
      fwshow: true,
      timeStr: "",
      weather: "晴朗",
      isExpanded: true, // 控制bot容器展开/收起
      fwshow1: false,
      lastClickedTitle: "",
      showlist: false,
      showdata: true,
      componentTag: "component1",
      iframe,
      selectedIndex: 0,
      isButton2Active: false,
      captions,
      selectvalue: "整体场景",
      selectvalue1: "",
      floorlist: [
        {
          name: "整体场景",
          floor: [
            { num: "一期", name: "一期" },
            { num: "二期", name: "二期" },
            { num: "三期", name: "三期" },
            { num: "邻里中心", name: "邻里中心" },
          ],
        },

        {
          name: "B1栋",
          floor: [
            { num: "1F", name: "B1栋1F" },
            { num: "2F", name: "B1栋2F" },
            { num: "3F", name: "B1栋3F" },
            { num: "4F", name: "B1栋4F" },
            { num: "顶层", name: "B1栋顶层" },
          ],
        },
        {
          name: "B2栋",
          floor: [
            { num: "1F", name: "B2栋1F" },
            { num: "2F", name: "B2栋21F" },
            { num: "3F", name: "B2栋3F" },
            { num: "4F", name: "B2栋4F" },
            { num: "顶层", name: "B2栋顶层" },
          ],
        },
        {
          name: "B3栋",
          floor: [
            { num: "1F", name: "B3栋1F" },
            { num: "2F", name: "B3栋2F" },
            { num: "3F", name: "B3栋3F" },
            { num: "4F", name: "B3栋4F" },
            { num: "顶层", name: "B3栋顶层" },
          ],
        },
        {
          name: "B4栋",
          floor: [
            { num: "1F", name: "B4栋1F" },
            { num: "2F", name: "B4栋2F" },
            { num: "3F", name: "B4栋3F" },
            { num: "4F", name: "B4栋4F" },
            { num: "顶层", name: "B4栋顶层" },
          ],
        },
        {
          name: "W1栋",
          floor: [
            { num: "1F", name: "W1栋1F" },
            { num: "2F", name: "W1栋2F" },
            { num: "3F", name: "W1栋3F" },
            { num: "4F", name: "W1栋4F" },
          ],
        },
        {
          name: "W2栋",
          floor: [
            { num: "1F", name: "W2栋1F" },
            { num: "2F", name: "W2栋2F" },
            { num: "3F", name: "W2栋3F" },
            { num: "4F", name: "W2栋4F" },
          ],
        },
      ],
      showfloor: false,
      floorindex: 1,
      floorindex1: 0,
      floorindex2: 0,
      flist: ['整体建筑', 'A1栋', 'A2栋', 'A3栋', 'A4栋', 'A5栋', 'B1栋', 'B2栋', 'B3栋', 'B4栋', 'B5栋', 'B6栋', 'A6地下室', 'B7地下室',],
      fllist: [
        {
          name: '整体建筑',
          list: ['']
        },
        {
          name: 'A1栋',
          list: ['整体', '楼顶', '12F', '11F', '10F', '9F', '8F', '7F', '6F', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'A2栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'A3栋',
          list: ['整体', '楼顶', '12F', '11F', '10F', '9F', '8F', '7F', '6F', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'A4栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'A5栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'B1栋',
          list: ['整体', '楼顶', '14F', '13F', '12F', '11F', '10F', '9F', '8F', '7F', '6F', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'B2栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'B3栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'B4栋',
          list: ['整体', '楼顶', '15F', '14F', '13F', '12F', '11F', '10F', '9F', '8F', '7F', '6F', '5F', '4F', '3F', '2F', '1F']
        },
        {
          name: 'B5栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {

          name: 'B6栋',
          list: ['整体', '楼顶', '5F', '4F', '3F', '2F', '1F']
        },
        {

          name: 'A6地下室',
          list: ['-2F', '-1F']
        },
        {

          name: 'B7地下室',
          list: ['-2F', '-1F']
        }
      ],

      options: [
        {
          value: "整体场景",
          label: "整体场景",
        },
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],

      botlist: [
        {
          name: "综合态势",
        },
        {
          name: "设备管理",
        },
        {
          name: "用能管理",
        },
        {
          name: "安全管理",
        },

        {
          name: "运维管理",
        },
        {
          name: "运营招商",
        },
      ],
      opt: '',
      clickCounter: 0, // 添加一个计数器
      closeTimeout: null,  // 用于延迟关闭的计时器
      buildId: '',
      floorId: '',
      getname: '无', //查询点位的name字段
      sblist: [],
      sbtitle: [],
    };

  },

  created() {
    setInterval(() => {
      this.formatDate();
    }, 1000);
    setTimeout(() => {
      this.shouall = true
    }, 5000);
    // this.shouall = true
  },
  mounted() {
    // // 添加全局点击事件监听器
    // setTimeout(() => {
    //   document.addEventListener('click', this.handleOutsideClick);
    // }, 2000);

    setInterval(() => {
      const iframe = document.getElementById('myIframe');
      if (iframe) {
        iframe.src = iframe.src;
      }
    }, 60000);
    var that = this;
    window.addEventListener("message", function (event) {
      console.log(event, 1569);
      //event.data获取传过来的数据
      if (event.data.type == "function") {
        console.log(event.data, '登录成功');
        let name = event.data.name;
        let param = event.data.param;
        // //console.log(param.name, "sssssssssssssssssssssssss");

        if (that.shownum === "none") {
          if (param.name == "cxfb2881_2" || param.name == "cxfb1880_1") {
            that.shownum = "block";
          }
        }
      } else if (event.data.type == "finished") {
        that.loading = false;
        that.iframeLoaded = true;
      }
      else if (event.data.type == "autoLogin") {
        console.log(event.data, '登录成功');
        // that.componentTag = 'component1'
        that.iframeLoaded = true;
      }
    });
  },
  beforeDestroy() { },
  methods: {
    closesbiframe() {

      this.issbiframe = false
    },
    returnbuild() {
      // this.showfloor = false
    },
    selectbuild(item, index) {

      this.buildId = item.name
      if (this.modelname == '写实') {
        this.modelname = '虚化'
        this.sendToUE4('xieshi');
      }
      // 如果选择的是“整体建筑”，切换 hideBuildings 状态
      if (item.name === '整体建筑') {
        this.hideBuildings = !this.hideBuildings; // 切换显示和隐藏
        this.buildId = ''
        this.floorId = ''
      } else {
        this.hideBuildings = false; // 点击其他楼栋时始终显示所有楼栋
        this.showfloor = true; // 显示楼层部分
      }

      console.log(this.buildId, '当前楼栋');
      let name = item.name  //楼层
      // if (item.name == 'A6地下室') {
      //   name = 'A6地下室-2F';
      //   this.floorId = '-2F'
      // } else if (item.name == 'B7地下室') {
      //   name = 'B7地下室-2F';
      //   this.floorId = '-2F'
      // }
      if (this.isseed) {
        this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, this.buildId, this.floorId)
      }
      this.sendToUE4(this.lastClickedTitle);
      if (this.lastClickedTitle.includes('-2F') || this.lastClickedTitle.includes('-1F')) {
        setTimeout(() => {
          this.sendToUE4(name);
        }, 1500);
      } else {
        this.sendToUE4(name);
      }

      this.lastClickedTitle = name;
      if (index) {
        // this.showfloor = !this.showfloor;
        this.floorindex = index
      } else {
        this.showfloor = false
      }
      this.floorindex1 = index
    },
    async selectfloor(item, index, name, length) {
      if (this.modelname == '写实') {
        this.modelname = '虚化'
        this.sendToUE4('xieshi');
      }
      if (index == 1) {
        item = length - 1 + 'F'
      } else if (index == 0) {
        item = ''
      }
      console.log(item, index, name);
      if (index == 0 && name == 'A6地下室' || index == 0 && name == 'B7地下室') {
        item = '-2F'
      } else if (index == 1 && name == 'A6地下室' || index == 1 && name == 'B7地下室') {
        item = '-1F'
      }
      this.floorId = item
      console.log(this.floorId, '当前楼层');
      this.floorindex2 = index
      await this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, this.buildId, this.floorId)

      this.sendToUE4(this.lastClickedTitle);
      // setTimeout(() => {
      //   this.sendToUE4(name + item);
      // }, 1000);
      if (this.lastClickedTitle.includes('-2F') || this.lastClickedTitle.includes('-1F')) {
        setTimeout(() => {
          this.sendToUE4(name + item);
        }, 1000);
        //this.sendToUE4(name + item);
      } else {
        this.sendToUE4(name + item);
      }
      this.lastClickedTitle = name + item;
    },

    seedsb(item) {
      // this.sendToUE4(this.lastClickedTitle);
      // if (this.lastClickedTitle.includes('-2F') || this.lastClickedTitle.includes('-1F')) {
      //   setTimeout(() => {
      //     this.sendToUE4(item);
      //   }, 1000);
      // } else {
      //   this.sendToUE4(item);
      // }
      // this.lastClickedTitle = item;
    },
    setoptions(name) {
      if (name == '漫游管理') {
        this.componentTag = ''
      }
      this.sendToUE4(this.opt);
      this.sendToUE4(name);
      this.opt = name
    },
    handleCheckboxChange: function () {
      // 复选框状态发生改变时触发的方法getlist
      if (this.isChecked) {
        console.log("复选框被选中");
        // this.dsweather = setInterval(() => {
        //   this.getweather()
        // }, 180000);
        this.sendToUE4("开");
      } else {
        console.log("复选框未被选中");
        // clearInterval(this.dsweather)
        // this.dsweather = ''
        this.sendToUE4("关");
      }
    },
    tqchange(index, name) {
      this.tq2 = index
      this.sendToUE4(name)
    },
    tqchange1(index, name) {
      this.tq1 = index;
      if (name == "7:00") {
        this.sendToUE4("早上");
      } else if (name == "12:00") {
        this.sendToUE4("中午");
      } else if (name == "17:00") {
        this.sendToUE4("旁晚");
      } else if (name == "22:00") {
        this.sendToUE4("晚上");
      }
    },
    changetqlist() {
      this.showtq = !this.showtq
    },
    seedchild(index) {
      // this.sendToUE4('fuwei');
      console.log(index, '设备');
      if (index == 4) {
        this.sbtitle = '给排水设备列表'
        this.getname = '消防水泵,一用一备,生活水泵（变频）,生活水泵变频'
        this.isseed = true
      } else if (index == 3) {
        this.sbtitle = '电力监控设备列表'
        this.getname = '电力监控'
        this.isseed = true
      } else if (index == 2) {
        this.sbtitle = '照明系统设备列表'
        this.getname = '照明'
        this.isseed = true
      } else if (index == 1) {
        this.sbtitle = '电梯系统设备列表'
        this.getname = '直梯'
        this.isseed = true
      }
      else if (index == 0) {
        // this.sbtitle = '无线对讲设备列表'
        // this.getname = '无线对讲'
      }
      else if (index == 5) {
        this.sbtitle = '通风机设备列表'
        this.getname = '隔油装置,排油烟风机直启,平时风机,平时兼消防风机,通用双速风机,消防风机'
        this.isseed = true
      } else if (index == 6) {
        this.sbtitle = '新风空调设备列表'
        this.getname = '室内机,多联式空调室外机'
        this.isseed = true
      }
      if (this.isseed) {
        this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, this.buildId, this.floorId)
      }


      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.shebei) {
          this.$refs.childComponent.shebei(index);
        }
      });
    },
    seednengyuan(index) {
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.nengyuan) {
          this.$refs.childComponent.nengyuan(index);
        }
      });
    },
    sendxs() {
      if (this.modelname == '虚化') {
        this.sendToUE4('xuhua');
        this.modelname = '写实'
      } else {
        this.modelname = '虚化'
        this.sendToUE4('xieshi');
      }
    },

    seedanquan(index) {
      console.log(index, '安全');
      if (index == 5) {
        this.sbtitle = '视频监控设备列表'
        this.getname = '人脸半球型摄像机,人脸枪型摄像机,电梯半球,高清半球型摄像机,高清枪型摄像机'
        this.isseed = true
      } else if (index == 4) {
        this.sbtitle = '门禁管理设备列表'
        this.getname = 'IC卡,人脸+IC卡'
        this.isseed = true
      } else if (index == 3) {
        this.sbtitle = '入侵报警设备列表'
        this.getname = '被动红外探测器,紧急按钮'
        this.isseed = true
      } else if (index == 2) {
        this.sbtitle = '电子巡更设备列表'
        this.getname = '电子巡更'
        this.isseed = true
      }
      else if (index == 1) {
        this.sbtitle = '无线对讲设备列表'
        this.getname = '无线对讲'
        this.isseed = true
      }
      this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, this.buildId, this.floorId)
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.anquan) {
          this.$refs.childComponent.anquan(index);
        }
      });
    },
    seedyunwei(index) {
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.yunwei) {
          this.$refs.childComponent.yunwei(index);
        }
      });
    },
    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    changela() {
      this.lablevalue = !this.lablevalue;
      this.seed1(this.lablevalue);
    },
    loucxuanz(index, data) {
      console.log(index, data, '楼层数据');
      this.xuanzindex = index;
      this.seed(data);
      this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4(data);
      this.lastClickedTitle = data;
      // this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, 'A1#', '1F')
      if (this.isseed) {
        this.fetchProjectSet(1, '6_UvetrB_EW6Bv-DNTg6-Q==', 0, '', '')
      }

    },
    sendTofloor(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UE收到的");
    },
    sendlou(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    toggleContent() {
      this.showlist = !this.showlist;
    },
    beforeEnter(el) {
      el.style.height = "0"; // 设置初始高度
    },
    enter(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 应用过渡样式
        el.style.height = "255px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 再次确认过渡样式
        el.style.height = "0"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    toggleContent1() {
      this.fwshow1 = !this.fwshow1;
    },
    beforeEnter1(el) {
      el.style.height = "20px"; // 设置初始高度
    },
    enter1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 应用过渡样式
        el.style.height = "102px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave1(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 再次确认过渡样式
        el.style.height = "20px"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    handleChange(value) {
      this.seed(value);
      console.log(value);
    },
    toggleBot() {
      this.isExpanded = !this.isExpanded;
      console.log(this.isExpanded);
    },
    //https://rest{api.amap.com/v3/weather/weatherInfo?city=320200&key=1c046ae4b42c14be43fb7966539e744e
    getweather() {
      const apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
      const cityCode = "320200"; // 你的城市代码
      const apiKey = "1c046ae4b42c14be43fb7966539e744e";
      const params = {
        city: cityCode,
        key: apiKey,
      };
      axios
        .get(apiUrl, { params })
        .then((response) => {
          // 请求成功处理
          console.log("响应数据:", response.data.lives[0]);
          const weatherCondition = response.data.lives[0].weather;
          if (weatherCondition.includes("晴")) {
            this.weather = "晴朗";
          } else if (weatherCondition.includes("雨")) {
            this.weather = "下雨";
          } else if (weatherCondition.includes("雪")) {
            this.weather = "下雪";
          } else {
            this.weather = "阴天";
          }
          console.log(this.weather);
          this.sendToUE4(this.weather);
        })
        .catch((error) => {
          // 请求失败处理
          console.error("请求失败:", error);
        });
    },
    seed(item) {
      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 1, data: item },
      //   },
      //   "*"
      // );
    },
    seed1(item) {
      console.log(item);

      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "function",
          name: "exec3d",
          param: { type: 2, data: item },
        },
        "*"
      );
    },
    slideLeft() {
      const container = this.$refs.bot1Container;
      const scrollAmount = -900; // 调整滑动距离，负值表示向左滑动
      container.scrollLeft += scrollAmount;
    },
    slideRight() {
      const container = this.$refs.bot1Container;
      const scrollAmount = 900; // 调整滑动距离，正值表示向右滑动
      container.scrollLeft += scrollAmount;
    },

    // 新增的关闭方法
    closeBot() {
      // console.log('closeBot');
      // 延迟关闭菜单，给用户时间点击菜单项
      this.closeTimeout = setTimeout(() => {
        // this.selectedIndex = null;
        this.fenyeTag = null;
      }, 100); // 延迟300ms关闭
    },
    closeBot1() {
      // console.log('closeBot111111');
      // 延迟关闭菜单，给用户时间点击菜单项
      this.closeTimeout = setTimeout(() => {
        // this.selectedIndex = null;
        this.fenyeTag = null;
      }, 100); // 延迟300ms关闭
    },
    openBot(index) {
      if (this.selectedIndex === index) {



        clearTimeout(this.closeTimeout); // 清除关闭菜单的计时器
        // this.selectedIndex = index;
        this.clickCounter++;
        // console.log(parseInt(index + 1), "1231232");
        // 根据计数器的值设置 this.fenyeTag


        this.fenyeTag = "component" + index;
      }

    },
    cancelClose() {
      clearTimeout(this.closeTimeout); // 鼠标移入菜单时取消关闭
    },
    seedfw() {
      if (this.modelname == '写实') {
        this.modelname = '虚化'
        this.sendToUE4('xieshi');
      }
      this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4('fuwei');

      this.lastClickedTitle = ''
    },

    selectBot(index, value) {
      if (this.modelname == '写实') {
        this.modelname = '虚化'
        this.sendToUE4('xieshi');
      }
      this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4('fuwei');
      // this.sendToUE4("漫游");.
      this.floorindex1 = 0
      this.showfloor = false
      this.opt = ''
      this.lastClickedTitle = ''
      this.clickCounter++;
      if (index == 4) {
        this.setlou = true;
      } else {
        this.setlou = false;
      }
      if (index == 2) {
        // this.fetchProjectSet(1, 'qfoWkYs0IL-mp1iJOOWGzw==', '', 0, '研发', '4F')
      }
      console.log(value);
      this.seed(value);
      this.selectedIndex = index;
      this.openBot(index)
      console.log(parseInt(index + 1), "1231232");

      this.componentTag = "component" + parseInt(index + 1);

this.getname='无'
      //操作菜单
      if (index == 2) {
        this.seednengyuan(19);
      } else if (index == 3) {
        this.seedanquan(9);
      } else if (index == 4) {
        this.seedyunwei(19);
      } else if (index == 1) {
        this.seedchild(9, 'close');
      }
  
      this.isseed = false

    },

    async fetchProjectSet(type, projectId, parkId, buildId, floorId) {

      console.log(this.getname, type, projectId, parkId, buildId, floorId);
      //从接口拿数据
      try {
        const response = await axios.get('https://api-dh3d-test.3dzhanting.cn:8080/projectSet/all', {
          params: {
            type: type,
            projectId: projectId,
            deviceId: "",  // 如果不需要 deviceId，可以将其删除或保留为空字符串
            parkId: parkId,
            buildId: buildId,
            floorId: floorId,
            name: this.getname,
            roomId: ''
          },
        });
        this.sblist = response.data.data
        console.log('Response data:', response.data);
        this.sendToUE41('shebei', response.data.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching project set:', error);
        throw error;  // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);


    },

    formatDate() {
      let now = new Date();
      let year = now.getFullYear();
      let month = (now.getMonth() + 1).toString().padStart(2, "0");
      let date = now.getDate().toString().padStart(2, "0");
      let hh = now.getHours().toString().padStart(2, "0");
      let mm = now.getMinutes().toString().padStart(2, "0");
      let ss = now.getSeconds().toString().padStart(2, "0");
      this.timeStr = `${year}-${month}-${date}  ${hh}:${mm}:${ss}`;
    },
  },
};
</script>
 
<style lang="less" scoped>
.sbiframe {
  position: fixed;
  top: 14%;
  width: 62%;
  height: 71%;
  left: 19%;
  z-index: 9999;
  border-radius: 1%;
}

.close {
  position: fixed;
  z-index: 10000;
  top: 14.3%;
  right: 19.2%;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.switch-btn {
  cursor: pointer;
  width: 37.2px;
  height: 18.8px;
  position: relative;
  border: 1px solid #dfdfdf;
  background-color: #fdfdfd;
  box-shadow: #dfdfdf 0 0 0 0 inset;
  border-radius: 15px;
  background-clip: content-box;
  display: inline-block;
  -webkit-appearance: none;
  user-select: none;
  outline: none;
}

.switch-btn:before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 0.0125rem 0.0375rem rgba(0, 0, 0, 0.4);
}

.switch-btn:checked {
  border-color: #56b0d4;
  box-shadow: #56b0d4 0 0 0 0.2rem inset;
  background-color: #56b0d4;
}

.switch-btn:checked:before {
  left: 18px;
}

.switch-btn.switch-btn-animbg {
  transition: background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:before {
  transition: left 0.3s;
}

.switch-btn.switch-btn-animbg:checked {
  box-shadow: #dfdfdf 0 0 0 0 inset;
  background-color: #56b0d4;
  transition: border-color 0.4s, background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:checked:before {
  transition: left 0.3s;
}

.content1 {
  /* 设置滚动条的样式 */
  padding-bottom: 10px;
  background: url("../assets/img/tqbg.png");
  background-size: 100% 100%;
  position: fixed;
  top: 52px;
  right: 24.4px;
  width: 282px;
  height: 255px;

  z-index: 99999999999999999;

  .xuanzeqi {
    height: 54px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .pp {
      margin-left: 10px;
      margin-right: 10px;
      font-size: 20px;
      color: #fff;
    }
  }

  .tianjiandtime {
    cursor: pointer;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    text-align: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime2 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0.5;
    pointer-events: none;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #686868;
        margin-top: 10px;
      }
    }
  }
}

.red-text {
  color: red;
}


.boxt {
  position: fixed;
  bottom: 121px;
  width: 128px;
  z-index: 100;
  background: url("../assets/image/boxtapbj2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 18px;

  .tapli {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 17px;
    color: #e1f6ff;
    cursor: pointer;

    // 103px x 26px 宽高
    width: 103px;
    height: 26px;
    background: url("../assets/image/hangneiico.png");
    background-size: 100% 100%;
  }
}

.boxtap {

  left: 561px;

}

.boxtap1 {
  left: 691px;
}

.boxtap2 {
  left: 825px;

}

.boxtap3 {

  left: 958px;


}

.boxtap4 {

  left: 1086px
}

.boxtap5 {

  left: 1039px;


}

.boxtap6 {

  left: 1221px;


}

.iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 0;
}

.caidan {
  position: fixed;
  z-index: 12;
  top: 5.4%;
  left: 24.6%;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .cdimg {
    position: absolute;
    top: 10px;
    z-index: 10;
  }

  .cd {
    position: absolute;
    top: 10px;
    z-index: 1;
    padding-top: 22.5px;
    border-radius: 5px;
    width: 100px;
    height: 100px;
    background: #1a284d;
    background-size: 100% 100%;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      pointer-events: auto;
      margin-top: 10px;
    }
  }
}

.fuwei {
  position: fixed;
  z-index: 2;
  top: 69px;
  left: 424px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 57px;
    height: 57px;
  }

  p {
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 18px;
    color: rgba(30, 227, 253, 0.64);
    background: linear-gradient(0deg,
        rgba(255, 255, 255, 0.98) 1.8798828125%,
        rgba(51, 156, 237, 0.64) 35.7177734375%);
    -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}

.container {
  // display: flex;
  // flex-wrap: wrap;
  // justify-content: space-between;
  // align-items: stretch;
  // height: 100%;
  // background: url('../assets/bgg.png');
  // background-size: 100% 100%;

  .bg {
    position: fixed;
    height: 1080px;
    width: 1920px;
    // background: url("../assets/image/all.png");
    background-size: 100% 100%;
    z-index: 1;
    pointer-events: none;
  }

  .bot {
    bottom: 20px;
    left: 559px;
    position: fixed;
    display: flex;
    flex-direction: row;
    justify-content: center;
    z-index: 2;


    .bot1 {
      // margin-left: 15px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .activeimg {

        margin-left: 22px;
        margin-right: 22px;
        width: 88px;
        height: 78px;
        // background: url('../assets/img/bot-a.png');
        background-size: 100% 100%;
        padding-top: 0.25rem;
        opacity: 1.85;

        img {
          width: 88px;
          height: 78px;
        }
      }

      .img {

        opacity: 0.85;
        margin-left: 22px;
        margin-right: 22px;
        width: 88px;
        height: 78px;
        // background: url('../assets/img/bot.png');
        background-size: 100% 100%;
        padding-top: 0.25rem;

        img {
          width: 88px;
          height: 78px;
        }

      }

      .p1 {
        margin-top: 11px;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 18px;
        color: #aee2e5;
        opacity: 0.55;
        background: linear-gradient(180deg,
            #455E7C 1.8798828125%,
            #455E7C 35.7177734375%);
        // border-radius: 5px;
      }

      .p2 {
        margin-top: 11px;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 20px;
        color: #7dffd9;
        background: linear-gradient(180deg,
            #455E7C 1.8798828125%,
            #455E7C 35.7177734375%);
      }
    }
  }

  // /* 定义动画效果 */
  // .expand-enter-active,
  // .expand-leave-active {
  //   transition: height 0.3s ease;
  //   height: 255px;
  // }

  // .expand-enter,
  // .expand-leave-to

  // /* 初始和结束状态 */
  //   {
  //   height: 0;
  // }

  .select {
    text-align: center;
    z-index: 999;
    position: fixed;
    top: 8px;
    right: 210.864px;
    z-index: 2;

    .el-select {
      width: 142.8px;
      height: 26.9px;
      background: url("../assets/image/select.png");
      background-size: 100% 100%;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      font-size: 13px;
      // color: #FEFEFE;
      text-align: center;

      cursor: pointer;
      line-height: 26px;
      z-index: 20;

      .sp {
        font-size: 15px;
        color: #e4f3ff;
        position: absolute;
        right: 10px;
        z-index: 999;
      }

      .pp {
        font-family: Adobe Heiti Std;
        font-weight: normal;
        font-size: 14px;
        color: #fefefe;
        // line-height: 113px;
      }
    }

    /* 定义动画 */

    .content::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .warnlist::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    .warnlist::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .content::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    .content {
      overflow: hidden; // 隐藏过渡中的溢出内容
      /* 设置滚动条的样式 */
      padding-bottom: 10px;
      overflow-y: auto;
      background: rgb(19, 44, 75);
      background-size: 100% 100%;
      position: fixed;
      top: 40px;
      right: 210px;
      width: 145px;
      height: 255px;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      z-index: 999;
      border: 0.5px solid rgb(19, 44, 75);
      border-radius: 8px;

      .butn {
        background: url("../assets/image/lou.png");
        background-size: 100% 100%;
        width: 70px;
        height: 21px;
        text-align: center;
        line-height: 21px;
        font-size: 12px;
        font-family: HYQiHei;
        font-weight: normal;
        color: #ffffff;
        margin-left: 10px;
        margin-top: 11px;
        // margin-right: 80px;
      }

      .btnbtn {
        margin-top: 10px;
        float: left;

        .btn1:hover {
          // color: aqua;
          background-color: aqua;
          // background-color: rgb(119, 119, 119);
        }

        .btn1 {
          border: none;
          // float: left;
          margin-left: 11px;
          width: 51px;
          height: 18px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
          background: rgba(87, 174, 235, 0.18);
          font-size: 12px;
          border-radius: 2px;
        }
      }
    }

    img {
      position: absolute;
      right: 20px;
      top: 4.2px;
      cursor: pointer;
    }
  }

  .btt {
    z-index: 999;
    cursor: pointer;
    position: fixed;
    top: 8.8px;
    right: 20px;
    // width: 160px;
    height: 36px;
    display: flex;

    .btt1 {
      margin-left: 24px;
      flex: 1;
      align-items: center;
      justify-content: center;
      display: flex;
      flex-direction: column;
      background: transparent;
      border: none;
      cursor: pointer;

      img {
        margin-top: -5px;
        // width: 38.8px;
        // height: 38.8px;
      }

      .imgg {
        width: 26px;
        height: 26px;
      }

      p {
        margin-top: 5px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 300;
        color: #6896b3;
      }
    }
  }

  .ttqq {
    width: 26px;
    height: 26px;
  }

  .collapsed {
    height: 0 !important;
    // visibility: hidden;
    /* 当collapsed类被应用时，设置高度为0 */
  }

  // .bot {
  //   bottom: 0;
  //   left: 0.5%;
  //   position: fixed;
  //   pointer-events: none;
  //   z-index: 2;
  //   width: 99%;
  //   background: url("../assets/image/homebot.png");
  //   background-size: 100% 100%;
  //   height: 93.6px;
  //   transition: height 0.3s ease-out;
  //   /* 调整动画持续时间和缓动函数 */
  //   // overflow: hidden;

  //   /* 避免内容在动画过程中溢出 */
  //   .opt {
  //     width: 35px;
  //     height: 25px;
  //     position: fixed;
  //     bottom: 50px;
  //     left: 0;
  //     right: 0;
  //     margin: auto;
  //     cursor: pointer;
  //     pointer-events: auto;
  //     transition: transform 0.3s ease-out;
  //   }

  //   .opt-up {
  //     transform: translateY(0px);
  //     /* 当容器展开时，图标向上移动 */
  //   }

  //   .opt-down {
  //     transform: translateY(30px);
  //     /* 当容器收起时，图标向下移动 */
  //   }

  //   .bot1-container {
  //     margin-top: 40px;
  //     // margin-left: .25rem;

  //     // width: 100vw;
  //     // overflow-x: auto;
  //     display: flex;
  //     pointer-events: auto;

  //     // overflow: hidden;
  //     .bot1 {
  //       margin-left: 0.2px;
  //       margin-right: 13.8px;
  //       cursor: pointer;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         padding-left: 10px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         padding-left: 10px;
  //         // padding-top: 6px;
  //       }

  //       .icon {
  //         //  width: 23.4px;
  //         // height: 20.2px;
  //         margin-left: 5px;
  //         width: 19%;
  //         height: 55%;
  //       }

  //       .p1 {
  //         width: 94.2px;
  //         text-align: center;
  //         margin-left: -5.2px;

  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         width: 94.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }

  //     .bot2 {
  //       margin-left: 13.8px;
  //       margin-right: 13.8px;
  //       cursor: pointer;
  //       position: absolute;
  //       z-index: 20;
  //       top: -25%;
  //       left: 41.7%;
  //       right: 0;
  //       margin: auto;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         // padding-left: 10px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145.2px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         // padding-left: 10px;
  //         // padding-top: 6px;
  //       }

  //       .p1 {
  //         width: 145.2px;
  //         text-align: center;
  //         margin-left: 1.8px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         text-align: center;
  //         width: 145.2px;
  //         margin-left: 1.8px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }

  //     .bot3 {
  //       z-index: 20;
  //       margin-left: 13.8px;
  //       margin-right: 13.8px;
  //       cursor: pointer;
  //       position: absolute;
  //       top: -25%;
  //       left: 50.5%;
  //       right: 0;
  //       margin: auto;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         padding-left: 18px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         padding-left: 18px;
  //         // padding-top: 6px;
  //       }

  //       .p1 {
  //         width: 116.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         width: 116.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }
  //   }
  // }

  .huangse {
    color: #ff831f;
  }

  .head {
    top: 0;
    position: fixed;
    // pointer-events: none;
    z-index: 2;

    .img {
      overflow: hidden;
      width: 100%;
      height: 125px;
    }

    .title {
      background: url("../assets/image/head.png");
      background-size: 100% 100%;
      width: 1920px;
      height: 126px;
      text-align: center;
      font-family: FZZongYi-M05S;
      font-weight: 400;
      font-size: 37px;
      color: #ffffff;
      line-height: 72.2px;
      letter-spacing: 1.6px;
      // color: rgba(255, 249, 249, 1);
      text-shadow: 0px 3px 7px rgba(0, 0, 0, 0.35);
    }
  }

  .now-time {
    position: absolute;
    top: 7.96px;
    left: 10px;
    // height: 25px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 19px;
    color: #e4f3ff;
    display: flex;
    z-index: 2;

    img {
      width: 144px;
      height: 31px;
      margin-top: 0px;
    }

    span {
      margin-left: 42px;
    }
  }

  .chart {
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

.caidanlist {
  position: fixed;
  top: 6.3%;
  right: 22.1%;
  z-index: 10;

  .caidanimg1 {
    background: url("../assets/image/caidanimg1.png");
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;

    .caidanicon1 {
      width: 14px;
      height: 16px;
    }

    .caifonsiez {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }

    .caixiangxia {
      width: 12px;
      height: 7px;
      cursor: pointer;
    }
  }

  .list {
    // 108px x 30px
    width: 111px;
    height: 29px;
    background-color: rgba(113, 155, 224, 0.2);

    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-top: 1px solid #719bdf;
  }

  .list:hover {
    background-color: rgba(113, 155, 224, 0.5);
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
  }

  //
}

.active {
  background-color: rgba(113, 155, 224, 0.7) !important;
}

.xuhua {
  background: url('../assets/image/floor1.png');
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
  position: fixed;
  top: 294px;
  left: 370px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 26.3px;
  text-align: center;
  cursor: pointer;
}

.floorcd {



  cursor: pointer;
  // z-index: 999;
  position: fixed;
  top: 320px;
  left: 369.864px;

  .return {
    position: absolute;
    top: -200px;
    left: 10px;
    width: 34px;
    height: 35px;
  }

  .flist {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url('../assets/image/floor1.png');
    background-size: 100% 100%;
    width: 87.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 13px;
    color: #FFFFFF;
    line-height: 26.3px;
    text-align: center;
  }
}

.flist1 {
  margin-top: 2px;
  margin-bottom: 2px;
  background: url('../assets/image/floor.png');
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 15px;
  color: #7dffd9;
  line-height: 26.3px;
  text-align: center;
}

.flist:hover {


  background: url('../assets/image/floor.png');
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
}

.tit {
  position: absolute;
  top: -26px;
  left: 7px;
  width: 95px;
  height: 45px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 23px;
  color: #FFFFFF;
  // line-height: 21px;
  background: linear-gradient(0deg, #FFFFFF 0%, #0E96FA 98.6328125%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}

.floorcd1 {
  cursor: pointer;
  // z-index: 88;
  position: fixed;
  top: 322px;
  left: 452.864px;

  .tit {
    line-height: 21px;
    position: absolute;
    top: -26px;
    left: -13px;
    width: 102px;
    height: 45px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 23px;
    color: #FFFFFF;
    // line-height: 52px;
    // z-index: 1;
    background: linear-gradient(0deg, #FFFFFF 0%, #0E96FA 98.6328125%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
  }

  .return {
    position: absolute;
    top: -244px;
    left: 11px;
    width: 34px;
    height: 35px;
  }



  .flist {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url('../assets/image/floor1.png');
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 26.3px;
    text-align: center;
  }

  .flist1 {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url('../assets/image/floor.png');
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 15px;
    color: #7dffd9;
    line-height: 26.3px;
    text-align: center;
  }

  .flist:hover {

    background: url('../assets/image/floor.png');
    background: url('../assets/image/floor.png');
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
  }
}

.groups {
  cursor: pointer;
  z-index: 999;
  position: fixed;
  top: 43px;
  right: 423.864px;

  .subitem {
    margin-left: 3px;
    margin-right: -12px;
  }
}

/deep/.el-sub-menu .el-sub-menu__icon-arrow {
  margin-right: -12px !important;
}

/deep/.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item-group__title,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-sub-menu__title {
  padding-left: 27px !important;
}

/deep/ .el-menu-item-group__title {
  display: none !important;
}

/deep/ .el-sub-menu__title {
  background: url("../assets/image/caidanimg1.png");
  background-size: 100% 100%;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
  height: 32px;
}

/deep/ .el-menu-item {
  height: 32px !important;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
}

/deep/ .el-menu {
  border: none;
}

.loading_page {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  /* Stack items vertically */
  justify-content: center;
  /* Center items vertically */
  align-items: center;
  /* Center items horizontally */

  background-color: rgb(33, 33, 33);
  margin: 0;

  .inner-box {
    margin-left: 65px;
    position: relative;
    width: 72px;
    height: 72px;
    transform-style: preserve-3d;
    transform-origin: center;
    animation: 3s ctn infinite;
    transform-origin: 0 0;
    transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
  }

  .inner {
    position: absolute;
    width: 72px;
    height: 72px;
    text-align: center;
    line-height: 72px;
    color: #fff;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transform-origin: center;
  }

  .inner:nth-child(1) {
    transform: rotateX(90deg) translateZ(36px);
    animation: 3s top infinite;
  }

  .inner:nth-child(2) {
    transform: rotateX(-90deg) translateZ(36px);
    animation: 3s bottom infinite;
  }

  .inner:nth-child(3) {
    transform: rotateY(90deg) translateZ(36px);
    animation: 3s left infinite;
  }

  .inner:nth-child(4) {
    transform: rotateY(-90deg) translateZ(36px);
    animation: 3s right infinite;
  }

  .inner:nth-child(5) {
    transform: translateZ(36px);
    animation: 3s front infinite;
  }

  .inner:nth-child(6) {
    transform: rotateY(180deg) translateZ(36px);
    animation: 3s back infinite;
  }

  @keyframes ctn {
    from {
      transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
    }

    50% {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }

    to {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }
  }

  @keyframes top {
    from {
      transform: rotateX(90deg) translateZ(36px);
    }

    50% {
      transform: rotateX(90deg) translateZ(36px);
    }

    75% {
      transform: rotateX(90deg) translateZ(72px);
    }

    to {
      transform: rotateX(90deg) translateZ(36px);
    }
  }

  @keyframes bottom {
    from {
      transform: rotateX(-90deg) translateZ(36px);
    }

    50% {
      transform: rotateX(-90deg) translateZ(36px);
    }

    75% {
      transform: rotateX(-90deg) translateZ(72px);
    }

    to {
      transform: rotateX(-90deg) translateZ(36px);
    }
  }

  @keyframes left {
    from {
      transform: rotateY(90deg) translateZ(36px);
    }

    50% {
      transform: rotateY(90deg) translateZ(36px);
    }

    75% {
      transform: rotateY(90deg) translateZ(72px);
    }

    to {
      transform: rotateY(90deg) translateZ(36px);
    }
  }

  @keyframes right {
    from {
      transform: rotateY(-90deg) translateZ(36px);
    }

    50% {
      transform: rotateY(-90deg) translateZ(36px);
    }

    75% {
      transform: rotateY(-90deg) translateZ(72px);
    }

    to {
      transform: rotateY(-90deg) translateZ(36px);
    }
  }

  @keyframes front {
    from {
      transform: translateZ(36px);
    }

    50% {
      transform: translateZ(36px);
    }

    75% {
      transform: translateZ(72px);
    }

    to {
      transform: translateZ(36px);
    }
  }

  @keyframes back {
    from {
      transform: rotateY(180deg) translateZ(36px);
    }

    50% {
      transform: rotateY(180deg) translateZ(36px);
    }

    75% {
      transform: rotateY(180deg) translateZ(72px);
    }

    to {
      transform: rotateY(180deg) translateZ(36px);
    }
  }

  .loading-text {
    z-index: 9999;
    color: #fff;
    /* Text color */
    margin-top: 35px;
    /* Space between the cube and text */
    font-size: 18px;
    /* Text size */
    letter-spacing: 1px;
    /* Letter spacing */
    text-align: center;
  }

  /* Reset default margin */
}

.expand-leave-active {
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-name: expandIn;
}

.expand-leave-active {
  animation-name: shrinkAndFade;
}

@keyframes expandIn {
  0% {
    transform: scale(0.4);
    opacity: 0;
    transform-origin: center;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkAndFade {
  0% {
    transform: scale(1);
    opacity: 1;
    transform-origin: center;
  }

  100% {
    transform: scale(0.4);
    opacity: 0;
  }
}
</style>
