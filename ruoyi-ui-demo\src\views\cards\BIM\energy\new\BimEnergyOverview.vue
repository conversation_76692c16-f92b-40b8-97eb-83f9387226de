<template>
  <div class="left-card-box">
    <div class="left-card-title" v-text="title"></div>
    <!-- 能源类型切换下拉框 -->
    <div class="energy-selector mb10">
      <el-select v-model="selectedEnergyType" @change="onEnergyTypeChange" size="mini"
        style="width: 90px;background-color: #213353;">
        <el-option v-for="item in energyTypeOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="energy-overview-box">
      <img class="bg" src="/image/bim/electrical_bg.png" alt="">
      <div class="energy-overview-item-box">
        <img src="/image/bim/electrical.png" alt="">
        <div class="energy-overview-item">
          <div class="energy-overview-item-title">累计{{currentEnergyConfig.label}}({{ currentUnit }})</div>
          <div class="energy-overview-item-value">{{ totalEnergy }}</div>
        </div>
      </div>
    </div>
    <div class="energy-overview-box1">
      <div class="energy-box1">
        <div class="energy-box1-item">
          <img class="icon" src="/image/bim/electrical_icon.png" alt="">
          <div class="energy-box1-item-box">
            <div class="energy-box1-item-title">本月{{currentEnergyConfig.label}}({{ currentUnit }})</div>
            <div class="energy-box1-item-value">{{ yearMontSummary.curMonth }}</div>
          </div>
        </div>
        <div class="energy-box1-item left">
          <p class="energy-box1-item-title">环比</p>
          <p class="energy-box1-item-value1">{{ yearMontSummary.diffMonth }}%</p>
        </div>
        <img class="line" src="/image/bim/electrical_bg2.png" alt="">
        <div class="energy-box1-item top">
          <img class="icon" src="/image/bim/electrical_icon.png" alt="">
          <div class="energy-box1-item-box">
            <div class="energy-box1-item-title">上月{{currentEnergyConfig.label}}({{ currentUnit }})</div>
            <div class="energy-box1-item-value">{{ yearMontSummary.lastMonth }}</div>
          </div>
        </div>
      </div>
      <div class="energy-box1">
        <div class="energy-box1-item">
          <img class="icon" src="/image/bim/electrical_icon.png" alt="">
          <div class="energy-box1-item-box">
            <div class="energy-box1-item-title">本年{{currentEnergyConfig.label}}({{ currentUnit }})</div>
            <div class="energy-box1-item-value">{{ yearMontSummary.curYear }}</div>
          </div>
        </div>
        <div class="energy-box1-item left">
          <p class="energy-box1-item-title">环比</p>
          <p class="energy-box1-item-value1">{{ yearMontSummary.diffYear }}%</p>
        </div>
        <img class="line" src="/image/bim/electrical_bg2.png" alt="">
        <div class="energy-box1-item top">
          <img class="icon" src="/image/bim/electrical_icon.png" alt="">
          <div class="energy-box1-item-box">
            <div class="energy-box1-item-title">去年{{currentEnergyConfig.label}}({{ currentUnit }})</div>
            <div class="energy-box1-item-value">{{ yearMontSummary.lastYear }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { buildingEnergyDataList } from "@/api/energy/apis";

export default {
  props: {
    "title": {
      type: String,
      default: "用能概览",
    },
    "height": {
      type: String,
      default: "185px",
    },
    "autoHeight": {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      curBuilding: this.gf.getCurBuilding(),
      selectdTab: 'day',
      dayTotal: 0,
      weekTotal: 0,
      monthTotal: 0,
      // 累计用量
      totalEnergy: 0,
      // 年月比例
      yearMontSummary: {
        curMonth: 0,
        lastMonth: 0,
        curYear: 0,
        lastYear: 0,
        diffMonth: 0,   // 变化百分比
        diffYear: 0,    // 变化百分比
      },
      // 能源类型选择
      selectedEnergyType: 'electricity',
      energyTypeOptions: [
        { value: 'electricity', label: '用电', unit: 'kwh', deviceType: 'electricity' },
      ],
      // 能源盒子数据
      energyBoxes: [1, 2], // 用于v-for循环渲染两个盒子
      // 简化后的对比数据存储
      compareData: {}
    }
  },
  computed: {
    // 当前选择的能源类型配置
    currentEnergyConfig() {
      return this.energyTypeOptions.find(item => item.value === this.selectedEnergyType) || this.energyTypeOptions[0];
    },
    // 当前单位
    currentUnit() {
      return this.currentEnergyConfig.unit;
    },
    // 当前设备类型
    currentDeviceType() {
      return this.currentEnergyConfig.deviceType;
    }
  },
  mounted() {
    this.initAllData();
    this.initEnergyTypeOptions();
  },
  methods: {
    // 初始化所有数据
    initAllData() {
      this.getTotalData('day');
      this.getTotalData('week');
      this.getTotalData('month');
      this.getSummaryData();
    },
    // 能源类型切换事件
    onEnergyTypeChange() {
      this.initAllData();
    },
    // 获取数据总量
    getTotalData(type) {
      const param = this.buildParams(type);
      
      buildingEnergyDataList(param).then(res => {
        let total = 0;
        if (res.data && res.data.datas) {
          total = res.data.datas.reduce((sum, d) => sum + parseInt(d.totalVal || 0), 0);
        }
        
        this[`${type}Total`] = total;
      }).catch(error => {
        console.error(`获取${type}数据失败:`, error);
      });
    },
    
    // 构建API参数
    buildParams(type, isCompare = false) {
      const param = {
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "day",
        from: this.$moment().startOf("day").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
      };
      
      // 根据类型调整参数
      switch (type) {
        case 'week':
          param.displayType = 'day';
          param.from = this.$moment().startOf("week").format("YYYY-MM-DD");
          break;
        case 'month':
          param.displayType = isCompare ? 'day' : 'month';
          param.from = this.$moment().startOf("month").format("YYYY-MM-DD");
          break;
      }
      
      // 如果是对比数据，调整时间范围
      if (isCompare) {
        const period = type === 'day' ? 'day' : type === 'week' ? 'week' : 'month';
        param.displayType = type === 'day' ? 'hour' : 'day';
        param.from = this.$moment().subtract(1, period).startOf(period).format(type === 'day' ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD");
        param.to = this.$moment().subtract(1, period).endOf(period).format(type === 'day' ? "YYYY-MM-DD HH:mm:ss" : "YYYY-MM-DD");
      }
      
      return param;
    },

    async initEnergyTypeOptions() {
      try {
        const energyTypes = await this.gf.getBuildingEnergyTypes(this.curBuilding.id);
        if (!energyTypes || energyTypes.length === 0) {
          console.warn("未获取到能源类型数据");
          return;
        }

        this.energyTypeOptions = energyTypes.map(item => {
          let label = item.typeName;
          // 如果只有"电"，则改为"用电"
          if (item.type === 'electricity') {
            label = '用电';
          } else if (item.type === 'water') {
            label = '用水';
          }
          return {
            label,
            value: item.type,
            unit: item.typeUnit,
            deviceType: item.type,
          };
        });

        // 默认选中第一个能源类型
        this.selectedEnergyType = this.energyTypeOptions[0]?.value || '';
      } catch (error) {
        console.error("获取能源类型失败:", error);
        // 可以设置一个默认选项，防止 UI 异常
        this.energyTypeOptions = [
          { label: "用电", value: "electricity", unit: "kWh", deviceType: "electricity" }
        ];
        this.selectedEnergyType = 'electricity';
      }
    },
    
    // 简化后的tabChange方法
    tabChange(type) {
      this.selectdTab = type;
    },
    
    // 获取当月、上月、当年、上年数据及环比
    getSummaryData() {
      // 当月数据
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "year",
        from: this.$moment().startOf("month").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        if (res.data && res.data.datas && res.data.datas.length > 0) {
          this.yearMontSummary.curMonth = parseInt(res.data.datas[0].totalVal || 0);
          this.updateDiffMonth();
        }
      }).catch(error => {
        console.error("获取当月数据失败:", error);
      });
      
      // 上月同期数据
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "year",
        from: this.$moment().add(-1, "months").startOf("month").format("YYYY-MM-DD"),
        to: this.$moment().add(-1, "months").endOf("month").format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        if (res.data && res.data.datas && res.data.datas.length > 0) {
          this.yearMontSummary.lastMonth = parseInt(res.data.datas[0].totalVal || 0);
          this.updateDiffMonth();
        }
      }).catch(error => {
        console.error("获取上月数据失败:", error);
      });
      
      // 当年数据
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "year",
        from: this.$moment().startOf("year").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        if (res.data && res.data.datas && res.data.datas.length > 0) {
          this.yearMontSummary.curYear = parseInt(res.data.datas[0].totalVal || 0);
          this.updateDiffYear();
        }
      }).catch(error => {
        console.error("获取当年数据失败:", error);
      });
      
      // 去年同期数据
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "year",
        from: this.$moment().add(-1, "years").startOf("year").format("YYYY-MM-DD"),
        to: this.$moment().add(-1, "years").format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        if (res.data && res.data.datas && res.data.datas.length > 0) {
          this.yearMontSummary.lastYear = parseInt(res.data.datas[0].totalVal || 0);
          this.updateDiffYear();
        }
      }).catch(error => {
        console.error("获取去年数据失败:", error);
      });
      
      // 累计数据
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "total",
        from: this.$moment().add(-10, "years").startOf("year").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        if (res.data && res.data.datas && res.data.datas.length > 0) {
          this.totalEnergy = parseInt(res.data.datas[0].totalVal || 0);
        }
      }).catch(error => {
        console.error("获取累计数据失败:", error);
      });
    },
    
    // 更新月环比
    updateDiffMonth() {
      if (this.yearMontSummary.lastMonth > 0) {
        this.yearMontSummary.diffMonth = (((this.yearMontSummary.curMonth - this.yearMontSummary.lastMonth) / this.yearMontSummary.lastMonth) * 100).toFixed(2);
      }
    },
    
    // 更新年环比
    updateDiffYear() {
      if (this.yearMontSummary.lastYear > 0) {
        this.yearMontSummary.diffYear = (((this.yearMontSummary.curYear - this.yearMontSummary.lastYear) / this.yearMontSummary.lastYear) * 100).toFixed(2);
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/bimCard.scss";

.energy-selector {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  position: absolute;
  top: 10px;
  left: 234px;
  z-index: 1200;

  ::v-deep .el-select {
    .el-input__inner {
      color: #fff;
      font-size: 12px;
    }

    .el-input__suffix {
      color: #fff;
    }
  }
}
::v-deep .el-input {
    width: 90px!important;
}

.energy-overview-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100px;
  position: relative;

  .bg {
    width: 100%;
    height: 47.68px;
    position: absolute;
    top: 48px;
    left: 0;
    z-index: 1;
  }

  .energy-overview-item-box {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;

    .energy-overview-item {
      .energy-overview-item-title {
        font-family: Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: #FFFFFF;
        text-align: center;
        font-style: normal;
      }

      .energy-overview-item-value {
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 24px;
        color: #3CCCF9;
        text-align: center;
        font-style: normal;
      }
    }
  }

}

.energy-overview-box1 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 136px;
  gap: 10px;
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;

  .energy-box1 {
    width: 100%;
    height: 136px;
    background: url('/image/bim/electrical_bg1.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;

    // flex-direction: row;
    .icon {
      width: 32px;
      height: 32px;
    }

    .line {
      width: 115px;
      // height: 100%;
    }

    .left {
      margin-left: 33px;
    }

    .top {
      margin-top: 6px;
    }

    .energy-box1-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;
      margin-bottom: 3px;


      .energy-box1-item-box {
        display: flex;
        align-items: center;
        justify-content: left;
        flex-direction: column;
        text-align: left;


      }

      .energy-box1-item-title {
        font-family: MiSans;
        font-weight: 400;
        font-size: 12px;
        color: #C8DDF1;
        line-height: 18px;

        font-style: normal;
      }

      .energy-box1-item-value {
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 16px;
        color: #228FFF;
        margin-left: -15px;
        font-style: normal;
      }

      .energy-box1-item-value1 {
        font-family: HarmonyOS Sans SC;
        font-weight: 500;
        font-size: 16px;
        color: #11CEBB;
        margin-left: 1px;
        font-style: normal;
      }
    }
  }

}
</style>
