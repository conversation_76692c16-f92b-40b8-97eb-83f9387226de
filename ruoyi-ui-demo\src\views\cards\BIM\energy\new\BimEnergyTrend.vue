<template>
  <div class="left-card-box">
    <div class="left-card-title" v-text="title"></div>
    <!-- 能源类型切换下拉框 -->
    <div class="energy-selector mb10">
      <el-select v-model="selectedEnergyType" @change="onEnergyTypeChange" size="mini"
        style="width: 90px;background-color: #213353;">
        <el-option v-for="item in energyTypeOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="electrical-box">
      <div class="electrical-item" :class="selectdTab == 'day' ? 'active' : ''" @click="tabChange('day')">
        本日
      </div>
      <div class="electrical-item" :class="selectdTab == 'week' ? 'active' : ''" @click="tabChange('week')">
        本周
      </div>
      <div class="electrical-item" :class="selectdTab == 'month' ? 'active' : ''" @click="tabChange('month')">
        本月
      </div>
      <div class="electrical-item" :class="selectdTab == 'year' ? 'active' : ''" @click="tabChange('year')">
        本年
      </div>

    </div>
    <BaseChart :optionData="chartOption" :height="autoHeight ? 'calc(100% - 110px)' : height" />
  </div>
</template>

<script>

import { buildingEnergyDataList } from "@/api/energy/apis";
import BaseChart from "@/views/ScreenCommBase/components/common/BaseChart.vue";

export default {
  props: {
    "title": {
      type: String,
      default: "用能统计",
    },
    "height": {
      type: String,
      default: "215px",
    },
    "autoHeight": {
      type: Boolean,
      default: false
    }
  },
  components: {
    BaseChart
  },
  data() {
    return {
      loading: false,
      curBuilding: this.gf.getCurBuilding(),

      selectdTab: 'day',
      dayTotal: 0,
      weekTotal: 0,
      monthTotal: 0,
      yearTotal: 0,

      chartOption: {},
      // 能源类型选择
      selectedEnergyType: 'electricity',
      energyTypeOptions: [
        { value: 'electricity', label: '用电', unit: 'kwh', deviceType: 'electricity' },
        // { value: 'water', label: '用水', unit: '吨', deviceType: 'water' }
      ],
      // 对比数据存储
      compareData: {
        day: { current: [], previous: [] },
        week: { current: [], previous: [] },
        month: { current: [], previous: [] },
        year: { current: [], previous: [] },

      }
    }
  },
  computed: {
    // 当前选择的能源类型配置
    currentEnergyConfig() {
      return this.energyTypeOptions.find(item => item.value === this.selectedEnergyType) || this.energyTypeOptions[0];
    },
    // 当前单位
    currentUnit() {
      return this.currentEnergyConfig.unit;
    },
    // 当前设备类型
    currentDeviceType() {
      return this.currentEnergyConfig.deviceType;
    }
  },
  created() {
  },
  mounted() {
    this.initAllData();
    // console.log(this.gf.getBuildingEnergyTypes('1'), 111);
    this.initEnergyTypeOptions();

  },
  methods: {
    // 初始化所有数据
    initAllData() {
      this.getTotalData('day');
      this.getTotalData('week');
      this.getTotalData('month');
      this.getTotalData('year');

      this.tabChange('day');
    },
    // 能源类型切换事件
    onEnergyTypeChange() {
      this.initAllData();
    },
    getTotalData(type) {
      let param = {
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "day",
        from: this.$moment().startOf("day").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
      }
      if (type == 'week') {
        param.displayType = 'day'
        param.from = this.$moment().startOf("week").format("YYYY-MM-DD")
      } else if (type == 'month') {
        param.displayType = 'month'
        param.from = this.$moment().startOf("month").format("YYYY-MM-DD")
      } else if (type == 'year') {
        param.displayType = 'year'
        param.from = this.$moment().startOf("year").format("YYYY-MM-DD")
      }
      buildingEnergyDataList(param).then(res => {
        let total = 0
        res.data.datas.map(d => {
          total += parseInt(d.totalVal);
        });
        if (type == 'day') {
          this.dayTotal = total
        } else if (type == 'week') {
          this.weekTotal = total
        } else if (type == 'month') {
          this.monthTotal = total
        } else if (type == 'year') {
          this.yearTotal = total
        }
      })
    },

    async initEnergyTypeOptions() {
      try {
        const energyTypes = await this.gf.getBuildingEnergyTypes(this.curBuilding.id);
        if (!energyTypes || energyTypes.length === 0) {
          console.warn("未获取到能源类型数据");
          return;
        }

        this.energyTypeOptions = energyTypes.map(item => {
          let label = item.typeName;
          // 如果只有"电"，则改为"用电"
          if (item.type === 'electricity') {
            label = '用电';
          } else if (item.type === 'water') {
            label = '用水';
          }
          return {
            label,
            value: item.type,
            unit: item.typeUnit,
            deviceType: item.type,
          };
        });

        // 默认选中第一个能源类型
        this.selectedEnergyType = this.energyTypeOptions[0]?.value || '';
      } catch (error) {
        console.error("获取能源类型失败:", error);
        // 可以设置一个默认选项，防止 UI 异常
        this.energyTypeOptions = [
          { label: "用电", value: "electricity", unit: "kWh", deviceType: "electricity" }
        ];
        this.selectedEnergyType = 'electricity';
      }
    },

    getListData(type) {
      // 获取当前期间数据
      this.getCurrentPeriodData(type);
      // 获取对比期间数据
      this.getComparePeriodData(type);
    },
    // 获取当前期间数据
    getCurrentPeriodData(type) {
      let param = {
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "hour",
        from: this.$moment().startOf("day").format("YYYY-MM-DD HH:mm:ss"),
        to: this.$moment().format("YYYY-MM-DD HH:mm:ss"),
      }
      if (type == 'week') {
        param.displayType = 'day'
        param.from = this.$moment().startOf("week").format("YYYY-MM-DD")
        param.to = this.$moment().format("YYYY-MM-DD")
      } else if (type == 'month') {
        param.displayType = 'day'
        param.from = this.$moment().startOf("month").format("YYYY-MM-DD")
        param.to = this.$moment().format("YYYY-MM-DD")
      } else if (type == 'year') {
        param.displayType = 'month'
        param.from = this.$moment().startOf("year").format("YYYY-MM-DD")
        param.to = this.$moment().format("YYYY-MM-DD")
      }
      buildingEnergyDataList(param).then(res => {
        let xData = []
        let yData = []
        if (res.data && res.data.datas) {
          res.data.datas.forEach(ii => {
            if (type == 'day') {
              xData.push(ii.recordedAt.slice(11, 13))
            } else if (type == 'year' || type == 'lastYear') {
              xData.push(ii.recordedAt.slice(5, 7)) // 月份
            } else {
              xData.push(ii.recordedAt.slice(8, 10)) // 日期
            }
            yData.push(ii.totalVal)
          })
        }
        // 存储当前期间数据
        this.compareData[type].current = { xData, yData };
        this.updateChart(type);
      })
    },
    // 获取对比期间数据
    getComparePeriodData(type) {
      let param = {
        buildingId: this.curBuilding.id,
        deviceType: this.currentDeviceType,
        type: this.currentDeviceType,
        displayType: "hour",
      }

      // 设置对比期间的时间范围
      if (type == 'day') {
        param.displayType = 'hour'
        param.from = this.$moment().subtract(1, 'day').startOf("day").format("YYYY-MM-DD HH:mm:ss")
        param.to = this.$moment().subtract(1, 'day').endOf("day").format("YYYY-MM-DD HH:mm:ss")
      } else if (type == 'week') {
        param.displayType = 'day'
        param.from = this.$moment().subtract(1, 'week').startOf("week").format("YYYY-MM-DD")
        param.to = this.$moment().subtract(1, 'week').endOf("week").format("YYYY-MM-DD")
      } else if (type == 'month') {
        param.displayType = 'day'
        param.from = this.$moment().subtract(1, 'month').startOf("month").format("YYYY-MM-DD")
        param.to = this.$moment().subtract(1, 'month').endOf("month").format("YYYY-MM-DD")
      } else if (type == 'year') {
        param.displayType = 'month'
        param.from = this.$moment().subtract(1, 'year').startOf("year").format("YYYY-MM-DD")
        param.to = this.$moment().subtract(1, 'year').endOf("year").format("YYYY-MM-DD")
      }

      buildingEnergyDataList(param).then(res => {
        let yData = []
        if (res.data && res.data.datas) {
          res.data.datas.forEach(ii => {
            yData.push(ii.totalVal)
          })
        }
        // 存储对比期间数据
        this.compareData[type].previous = { yData };
        this.updateChart(type);
      })
    },
    // 更新图表
    updateChart(type) {
      // 确保当前和对比数据都已加载
      if (!this.compareData[type].current.yData || !this.compareData[type].previous.yData) {
        return;
      }

      const currentData = this.compareData[type].current;
      const previousData = this.compareData[type].previous;

      // 获取对比期间的标签
      const getCompareLabel = (type) => {
        switch (type) {
          case 'day': return '昨日';
          case 'week': return '上周';
          case 'month': return '上月';
          case 'year': return '去年';

          default: return '对比';
        }
      };

      // 获取当前期间的标签
      const getCurrentLabel = (type) => {
        switch (type) {
          case 'day': return '今日';
          case 'week': return '本周';
          case 'month': return '本月';
          case 'year': return '今年';

          default: return '当前';
        }
      };

      this.$nextTick(() => {
        this.chartOption = {
          title: {
            text: '单位：' + this.currentUnit,
            left: 10,
            top: 5,
            textStyle: {
              color: '#FFFFFF',
              fontSize: 12,
              fontWeight: 'normal'
            }
          },
          grid: {
            left: 10,
            right: 10,
            bottom: 5,
            top: 40,
            containLabel: true
          },
          legend: {
            data: [getCurrentLabel(type), getCompareLabel(type)],
            icon: 'rect',
            itemWidth: 12,
            itemHeight: 12,
            textStyle: {
              color: '#FFFFFF',
            }
          },
          xAxis: {

            axisLine: {
              show: true,
              lineStyle: {
                width: 2,
                color: '#3A9AD7'
              }
            },
            type: 'category',
            boundaryGap: true,
            data: currentData.xData
          },
          yAxis: {
            type: 'value',
            splitLine: {
              show: true,
              lineStyle: {
                color: 'rgba(192,211,229,0.24)'
              }
            },
          },
          series: [
            {
              name: getCurrentLabel(type),
              lineStyle: {
                color: '#16CEB9',
              },
              itemStyle: {
                color: '#16CEB9',
                borderWidth: 2
              },
              areaStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(22, 206, 185, 0.8)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(22, 206, 185, 0)'
                  }
                ])
              },
              data: currentData.yData,
              type: 'line'
            },
            {
              name: getCompareLabel(type),
              lineStyle: {
                color: '#2294FE',
              },
              itemStyle: {
                color: '#2294FE',
                borderWidth: 2
              },
              data: previousData.yData,
              type: 'line'
            }
          ]
        }
      })
    },
    tabChange(type) {
      this.selectdTab = type
      this.getListData(type)
    }
  }
}
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/bimCard.scss";
::v-deep .el-input {
    width: 90px!important;
  }
.energy-selector {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  position: absolute;
  top: 10px;
  left: 234px;
  z-index: 1200;

  ::v-deep .el-select {
    .el-input__inner {

      color: #fff;
      font-size: 12px;
    }

    .el-input__suffix {
      color: #fff;
    }
  }
}

.electrical-box {
  display: flex;
  justify-content: space-between;
  padding: 9px 7px;

  .electrical-item {
    width: 65px;
    height: 27px;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #048DF0;
    display: flex;
    align-items: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    color: #C8DDF1;
    // background-color: rgba(30, 81, 121, .4);
    justify-content: center;
    border-radius: 8px;
    cursor: pointer;
  }

  .active {
    background: #072D57;
    box-shadow: inset 0px 0px 13px 0px #048DF0;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #048DF0;
    border-radius: 8px;
  }
}
</style>
