<template>
  <div>
    <div class="co2_body">
      <div class="fl">
        <div class="co2_main_left">
          <div class="co2_devops_left">
            <DeviceGroupWarning v-if="cgBuildingFlag" class="" :title="devopsData.electricalEquipment.title" :types="elecTypes" :height="devopsData.electricalEquipment.height" />
            <DeviceGroupWarning v-if="cgBuildingFlag" class="" :title="devopsData.safetyEquipment.title" :types="secuTypes" :height="devopsData.safetyEquipment.height" />
          </div>
        </div>
      </div>

        <div class="co2_main_center co2_energy_center">
          <div class="ft">
            <div class="page_fixed_nav_box" style="right: 3%;">
                <el-dropdown class="avatar-container hover-effect" trigger="hover">
                    <div>
                      <img :src="`/image/screen/${devopsData.pageNavButton.iconName}.png`" class="mb10"/>
                      <p>{{devopsData.pageNavButton.title}}</p>
                    </div>
                    <el-dropdown-menu class="nav_wrap" slot="dropdown">
                      <a class="router-link" v-for="link in subLinks"
                        @click="openWindow(link)">{{ link.name }}</a>
                    </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          <div class="fb">
            <div class="co2_device_center_body">
              <WarningList v-if="cgBuildingFlag" :title="devopsData.warningList.title" />
            </div>
          </div>
        </div>

      <div class="fr">
        <div class="co2_main_right">
          <AlarmHandle v-if="cgBuildingFlag" class="mt10" :title="devopsData.alarmType.title" :types="secuTypes" />
          <MaintenanceEvent v-if="cgBuildingFlag" :title="devopsData.repairIncidents.title" />
          <MaintenanceSummary v-if="cgBuildingFlag" :title="devopsData.registrationStatistics.title" class="mb20" />
        </div>
      </div>
    </div>

    <DeviceWarningDialog
      ref="deviceWarningDialog"
      @updateData="updateData" />

  </div>
</template>
<script>
import Weather from "@/views/ScreenHztl/components/common/Weather";
import DateTime from "@/views/ScreenHztl/components/common/DateTime";
import BaseChart from "@/views/ScreenHztl/components/common/BaseChart";

import DeviceWarningDialog from "@/views/device/monitor/components/Device/components/dialog/components/deviceWarningDialog.vue";

import {
  deviceFaultOption,
  repairOption
} from "@/views/ScreenHztl/components/common/co2Charts"

import {
  buildingEnergyDataList,
  buildingEnergyAvgDataList,
  buildingEnergyTypeSummary,
} from "@/api/energy/apis";


import {
  warningSummary,
  deviceWarningSummary,
  deviceWarningSummaryList,
  deviceWarningList,
  deviceMaintenanceSummary,
  deviceMaintenanceSummaryByDeviceType,
} from "@/api/device/apis";

import BaseView from "@/views/components/cO2View/components/common/BaseView";

import DeviceGroupWarning from "@/views/components/cO2View/components/common/DeviceGroupWarning";
import AlarmHandle from "@/views/components/cO2View/components/common/AlarmHandle";
import MaintenanceEvent from "@/views/components/cO2View/components/common/MaintenanceEvent";
import MaintenanceSummary from "@/views/components/cO2View/components/common/MaintenanceSummary";
import WarningList from "@/views/components/cO2View/components/common/WarningList";

export default {
  mixins: [ BaseView ], // 继承父模块
  components: {
    Weather,
    DateTime,
    BaseChart,
    DeviceWarningDialog,

    WarningList,          // 报警列表
    DeviceGroupWarning,   // 电气设备报警，安全设备报警
    AlarmHandle,          // 告警类型
    MaintenanceEvent,     // 报修事件
    MaintenanceSummary,   // 报修类型统计
  },
  data() {
    return {
      loading: false,
      curBuilding: this.gf.getCurBuilding(),

      // 电气设备
      elecTypes: "",
      // 安全设备
      secuTypes: "",

      // 子菜单列表
      // links: [
      //   {
      //     name: "资产档案",
      //     path: "/maintananceMgt/assets/dashaboard",
      //   },
      //   {
      //     name: "入库登记",
      //     path: "/maintananceMgt/assets/inport",
      //   },
      //   {
      //     name: "出登记",
      //     path: "/maintananceMgt/assets/outport",
      //   },
      //   {
      //     name: "耗材管理",
      //     path: "/maintananceMgt/assets/supplies",
      //   },
      //   {
      //     name: "盘点登记",
      //     path: "/maintananceMgt/assets/inventory",
      //   },
      //   {
      //     name: "保养计划",
      //     path: "/maintananceMgt/assets/maintain",
      //   },
      //   {
      //     name: "报修记录",
      //     path: "/maintananceMgt/maintain/list",
      //   },
      //   {
      //     name: "巡检记录",
      //     path: "/maintananceMgt/maintain/list2",
      //   },
      //   {
      //     name: "保养记录",
      //     path: "/maintananceMgt/maintain/list2",
      //   },
      //   {
      //     name: "保养计划",
      //     path: "/maintananceMgt/maintain/maintenance",
      //   },
      //   {
      //     name: "报警管理",
      //     path: "#",
      //   },
      //   {
      //     name: "知识商城",
      //     path: "#",
      //   },
      // ],

      // 设备故障趋势选择值
      deviceFaultValue: '0',
      // 电器设备故障趋势折线图
      deviceFaultOption: {},
      // 报警事件
      alarmTable: [
        {
          address: 'A区',
          time: '2016-05-03',
          state: 0
        },
        {
          address: 'B区',
          time: '2016-05-03',
          state: 1
        },
        {
          address: 'C区',
          time: '2016-05-03',
          state: 0
        },
        {
          address: 'D区',
          time: '2016-05-03',
          state: 0
        },
         {
          address: 'E区',
          time: '2016-05-03',
          state: 0
        },
         {
          address: 'F区',
          time: '2016-05-03',
          state: 0
        }
      ],

      // 报修事件
      repairOption: {},
      repairData: [],
      // 报修数量
      repairQuantity: [],


      // 通用日期选择
      baseSelectOpt: [
        {
          label: '本周',
          value: '本周',
          display: 'day',
          from: this.$moment().startOf('week').format("YYYY-MM-DD"),
          to: this.$moment().format("YYYY-MM-DD"),
        },
        {
          label: '本月',
          value: '本月',
          display: 'day',
          from: this.$moment().startOf('month').format("YYYY-MM-DD"),
          to: this.$moment().format("YYYY-MM-DD"),
        },
        {
          label: '今年',
          value: '今年',
          display: 'month',
          from: this.$moment().startOf('year').format("YYYY-MM-DD"),
          to: this.$moment().format("YYYY-MM-DD"),
        },
        {
          label: '累计',
          value: '累计',
          display: 'year',
          from: '2000-01-01',
          to: '2500-01-01',
        },
      ],
      // 设备种类
      deviceTypes: {},

      // 选中的电器种类
      electricityType: "全部设备",
      securityType: "全部设备",

      // 电器选项
      electricityValue: '本月',
      electricitySelectOption: [],
      // 环境安全选项
      securityValue: '本月',
      securitySelectOption: [],

      warningList: [], // 报警记录

      // 维保记录
      deviceMaintenanceSummaryData: {},
      //字典数据
      devopsData: {}
    };
  },
  computed: {
    repairQuantityTotal() {
      let total = 0;
      this.repairQuantity.forEach(item => {
        total += item.value.total;
      });
      return total
    },
    subLinks() {
      let menus = [];
      let childMenus = this.$store.state.permission.topbarRouters.filter( m => {
        return m.name == "MaintananceMgt";
      }).pop().children.filter( m => {
        return !m.hidden;
      });
      menus = childMenus.map( m => {
        return {
          name: m.meta.title,
          path: "/maintananceMgt/" + m.path.replace("/maintananceMgt/",''),
        }
      });
      return menus;
    },
  },
  created() {
    // 复制所有日期选择
    this.electricitySelectOption = JSON.parse(JSON.stringify(this.baseSelectOpt));
    this.securitySelectOption = JSON.parse(JSON.stringify(this.baseSelectOpt));
  },
  async mounted() {
    try {
      this.devopsData = JSON.parse(this.projectSettings.screenDevops);
      await this.setData(this.devopsData)
    } catch (ex) {
      // pass
    }


    // 设备状态统计数据
    this.deviceWarningSummary();

    // 报警事件列表
    this.deviceWarningList();

    // 设备维保统计 报修事件
    this.deviceMaintenanceSummaryByDeviceType();

    // 维保统计
    this.deviceMaintenanceSummary();
  },
  methods: {
    // 标签切换数据
    changeDevice(tp, value) {
      this[tp + 'Type'] = value;
      this.deviceWarningSummary()
        .then(res => {
          this.warningSummary(tp);
          this.warningSummaryChart(tp);
        });
    },

    // 打开新窗口
    openWindow(link) {
      window.open(this.gf.getFullPath() + link.path, 'innerPage');
    },

    targetButtom(e) {
      console.log(this.$refs.co2_devops_center);
      let dom = this.$refs.co2_devops_center;
      let className = dom.attributes.class.nodeValue;
      if(className.indexOf("_hidden") >= 0) {
        dom.className = "co2_energy_carbon co2_devops_center";
      } else {
        dom.className = "co2_energy_carbon co2_devops_center _hidden";
      }
    },

    // 左
    // 获取设备报警统计数据
    deviceWarningSummary() {
      return deviceWarningSummary({buildingId: this.curBuilding.id})
        .then(({data}) => {
          console.log(data);
          this.updateSummary(data);

          this.warningSummary('electricity');
          this.warningSummaryChart('electricity');

          this.warningSummary('security');
          this.warningSummaryChart('security');
        });
    },
    updateSummary(data) {
      // 更新总计
      Object.keys(this.deviceTypes.electricity).map( k => {
        this.deviceTypes.electricity[k].summary = {
          total: 0,
          warning: 0,
        };
        data.map( d => {
          if(this.deviceTypes.electricity[k].value.indexOf(d.deviceType) >= 0) {
            this.deviceTypes.electricity[k].summary.total += d.total;
            this.deviceTypes.electricity[k].summary.warning += d.warningStatusWarn;
          }
        });
      });
      Object.keys(this.deviceTypes.security).map( k => {
        this.deviceTypes.security[k].summary = {
          total: 0,
          warning: 0,
        };
        data.map( d => {
          if(this.deviceTypes.security[k].value.indexOf(d.deviceType) >= 0) {
            this.deviceTypes.security[k].summary.total += d.total;
            this.deviceTypes.security[k].summary.warning += d.warningStatusWarn;
          }
        });
      });
    },
    // 故障统计
    warningSummary(type) {
      // 电器设备
      warningSummary({
        buildingId: this.curBuilding.id,
        deviceTypes: this.deviceTypes[type][this[type+'Type']].value,
      }).then(({data}) => {
        this.deviceTypes[type][this[type+'Type']].warningSummary = data;
      });
    },
    // 故障趋势分析曲线
    warningSummaryChart(type) {
      let curSelect = this[type+'SelectOption'].filter(d => {
        return d.value == this[type + 'Value'];
      }).pop();
      console.log(curSelect);
      deviceWarningSummaryList({
        buildingId: this.curBuilding.id,
        deviceTypes: this.deviceTypes[type][this[type+'Type']].value,
        // severity: "一般",
        displayType: curSelect.display,
        from: curSelect.from,
        to: curSelect.to,
      }).then(({data}) => {
        console.log(data);
        let deviceFaultData = {
          xAxis: data.map( d => d.recordedAt ),
          data: data.map( d => d.totalVal ),
        };
        this.deviceTypes[type][this[type+'Type']].chartOpts = deviceFaultOption(deviceFaultData);
      });
    },

    handleElectricity() {
      this.warningSummaryChart("electricity");
    },
    handleSecurity() {
      this.warningSummaryChart("security");
    },


    // 中
    deviceWarningList() {
      deviceWarningList({buildingId: this.curBuilding.id})
        .then(({rows}) => {
          this.warningList = rows;
        });
    },
    // 报警操作
    handleUpdate(warning) {
      console.log(this.$refs.deviceWarningDialog);
      this.$refs.deviceWarningDialog.handleUpdate({
        ...warning,
        name: warning.deviceName,
      });
    },
    handleDelete(warning) {
      this.$refs.deviceWarningDialog.handleDelete({
        ...warning,
        name: warning.deviceName,
      });
    },
    // 刷新数据
    updateData() {
      this.deviceWarningList();
    },

    // 右
    deviceMaintenanceSummaryByDeviceType() {
      console.log(this.deviceTypes,'this.deviceTypes')
      deviceMaintenanceSummaryByDeviceType({
          buildingId: this.curBuilding.id,
          types: "",
        })
        .then(({data}) => {
          console.log("-------", data);
          let total = 0;
          Object.keys(this.deviceTypes.electricity).map( k => {
            this.deviceTypes.electricity[k].maintenance = 0;
            data.map( d => {
              if(this.deviceTypes.electricity[k].value.indexOf(d.recordedAt) >= 0) {
                this.deviceTypes.electricity[k].maintenance += d.totalVal;
              }
            });
          });
          Object.keys(this.deviceTypes.security).map( k => {
            this.deviceTypes.security[k].maintenance = 0;
            data.map( d => {
              if(this.deviceTypes.security[k].value.indexOf(d.recordedAt) >= 0) {
                this.deviceTypes.security[k].maintenance += d.totalVal;
              }
            });
          });
          let repairData = [
            {
              name: '安全设备',
              itemStyle: {
                color: '#CA334E'
              },
              children: [
                {
                  name: '智慧消防',
                  value: this.deviceTypes.security["智慧消防"].maintenance,
                  itemStyle: {
                    color: '#B1122E'
                  },
                },
                {
                  name: '电器火灾',
                  value: this.deviceTypes.security["电器火灾"].maintenance,
                  itemStyle: {
                    color: '#D8566D'
                  },
                },
                {
                  name: '室内环境',
                  value: this.deviceTypes.security["室内环境"].maintenance,
                  itemStyle: {
                    color: '#EB98A7'
                  },
                },
                {
                  name: '外墙玻璃',
                  value: this.deviceTypes.security["外墙玻璃"].maintenance,
                  itemStyle: {
                    color: '#FED1D9'
                  },
                }
              ]
            },
            {
              name: '电器设备',
               itemStyle: {
                color: '#00C2FF'
              },
              children: [
                {
                  name: '智慧空调',
                  value: this.deviceTypes.electricity["智慧空调"].maintenance,
                  itemStyle: {
                    color: '#016686'
                  },
                },
                {
                  name: '智慧照明',
                  value: this.deviceTypes.electricity["智慧照明"].maintenance,
                  itemStyle: {
                    color: '#00AEE4'
                  },
                },
                {
                  name: '大型电器',
                  value: this.deviceTypes.electricity["大型电器"].maintenance,
                  itemStyle: {
                    color: '#48C6ED'
                  },
                },
                {
                  name: '智慧路灯',
                  value: this.deviceTypes.electricity["智慧路灯"].maintenance,
                  itemStyle: {
                    color: '#8CE0FB'
                  },
                },
                {
                  name: '电梯',
                  value: this.deviceTypes.electricity["电梯"].maintenance,
                  itemStyle: {
                    color: '#C3F1FF'
                  },
                }
              ]
            },
          ];
          this.repairData = repairData;
          this.repairOption = repairOption(repairData);
        });
    },
    deviceMaintenanceSummary() {
      // 报修对应  type 是 紧急维修
      // 保养对应  type 是 日常维护， 预见性维修，计划性维修，设备改进性维修
      // 巡检记录 type 是  日常巡检
      // 保养计划 type 是  保养计划
      this.repairQuantity = [
        {
          name: '紧急维修',
          types: "紧急维修",
          value: {
            new: 0,
            doing: 0,
            finished: 0,
            total: 0,
            percent: 0,
          },
        },
        {
          name: '设备保养',
          types: "日常维护,预见性维修,计划性维修,设备改进性维修",
          value: {
            new: 0,
            doing: 0,
            finished: 0,
            total: 0,
            percent: 0,
          },
        },
        {
          name: '日常巡检',
          types: "日常巡检",
          value: {
            new: 0,
            doing: 0,
            finished: 0,
            total: 0,
            percent: 0,
          },
        },
        {
          name: '保养计划',
          types: "保养计划",
          value: {
            new: 0,
            doing: 0,
            finished: 0,
            total: 0,
            percent: 0,
          },
        }
      ];
      this.repairQuantity.map( (d, ind) => {
        deviceMaintenanceSummary({
            buildingId: this.curBuilding.id,
            types: d.types,
          }).then(({data}) => {
            this.repairQuantity[ind].value = data;
            this.repairQuantity[ind].value.percent = data.total > 0 ? (Math.round(((data.new+data.doing)/data.total)*100/2)) : 100;
            console.log(this.repairQuantity)
          });
      });
    },
    setDeviceTypes (deviceTypes, field) {
      Object.keys(deviceTypes[field]).forEach(k => {
        const insert = {
          summary: {
            total: 0,
            warning: 0,
          },
          warningSummary: {
            flowStatusConfirm: 0,
            flowStatusIgnore: 0,
            flowStatusUnread: 0,
            hasFixedN: 0,
            hasFixedY: 0,
            severityNormal: 0,
            severitySerious: 0,
            total: 0,
          },
          chartOpts: {},
          maintenance: 0,
        }
        if(deviceTypes[field][k].hasSeveritySummary) {
          insert.severitySummary = {
            normal: 0,
            serious: 0,
          }
        }
        const item = {
          name: deviceTypes[field][k].name,
          value: deviceTypes[field][k].value,
          ...insert
        }

        console.log(item,'item')
        deviceTypes[field][k] = {...item}
      });
    },
    setData (data) {
      const {elecTypes, secuTypes, deviceTypes} = data;
      this.elecTypes = elecTypes;
      this.secuTypes = secuTypes;
      this.setDeviceTypes(deviceTypes, 'electricity');
      this.setDeviceTypes(deviceTypes, 'security');
      this.deviceTypes = deviceTypes
    }
  }
};
</script>
