<template>
  <div>
    <div class="co2_body">
      <div class="fl">
        <div class="co2_main_left">
          <EnergySavingsInformation title="节能统计" :energySavingsInformation="energySavingsInformation"
            v-if="componentShow.energySavingsInformation"></EnergySavingsInformation>
          <EnergyConsumption title="系统总能耗" :energyConsumption="energyConsumption"
            v-if="componentShow.energyConsumption"></EnergyConsumption>
          <DeviceDataChartView v-if="cgBuildingFlag && componentShow.coldLoadTrend" title="总冷负荷" :height="230"
            :ids="coolLoad" :multiple="true" :allDeviceShow="true" :deviceSelectHide="true" />
        </div>
      </div>

      <div class="co2_main_center co2_energy_center">
        <div class="ft">
          <iframe class="iframe" v-if="bimData.show" :src="bimData.url" frameborder="0" scrolling="no"></iframe>
          <Prototype class="prototype"
                     v-if="cgBuildingFlag && resourceData.show"
                     :deviceId="null"
                     :resourceId="resourceData.id"
                     :showTitle="false"
                     :event="false"
                     :editable="false"
                     autoResize />
        </div>
        <div class="fb">
          <div class="co2_energy_carbon1">
            <div class="co2_energy_carbon pb20">
              <div class="btm">
                <DeviceDataChartView class="btm_item" v-if="cgBuildingFlag && componentShow.dynamicScop" title="EER(冷机能效比)" :height="230"
                :ids="scop" :deviceSelectHide="true" />
                <DeviceDataChartView class="btm_item" v-if="cgBuildingFlag && componentShow.coolWaterTemperature"
                  title="冷冻水出水温度" :height="230" :ids="coolWaterTemperature" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="fr">
        <div class="co2_main_right">
          <DataOverview v-if="cgBuildingFlag && componentShow.dataOverview" title="数据总览" :dataOverview="dataOverview" />
          <ElectricitySummary v-if="cgBuildingFlag && componentShow.electricitySummary" title="用电概览"
            :summary="electricitySummary" />
          <KeyIndicators :height="406" v-if="cgBuildingFlag && componentShow.KeyIndicators" title="关键指标" :ids="keyIds">
          </KeyIndicators>
          <DeviceDataChartView v-if="cgBuildingFlag && componentShow.pumpFrequency" title="循环泵频率"
          :height="230" :ids="pumpFrequency" />
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import BaseView from "@/views/components/cO2View/components/common/BaseView";
import ElectricitySummary from '@/views/components/cO2View/components/common/screen/energySaveComp/electricitySummary.vue'
import DataOverview from '@/views/components/cO2View/components/common/screen/energySaveComp/dataOverview.vue'
import KeyIndicators from '@/views/components/cO2View/components/common/screen/energySaveComp/keyIndicators.vue'
import CoolingLoad from '@/views/components/cO2View/components/common/screen/energySaveComp/coolingLoad.vue'
import EnergyConsumption from '@/views/components/cO2View/components/common/screen/energySaveComp/energyConsumption.vue'
import DynamicScop from '@/views/components/cO2View/components/common/screen/energySaveComp/dynamicScop.vue'
import OutletWaterTemperature from '@/views/components/cO2View/components/common/screen/energySaveComp/outletWaterTemperature.vue'
import CirculatingPumpFrequency from '@/views/components/cO2View/components/common/screen/energySaveComp/circulatingPumpFrequency.vue'
import EnergySavingsInformation from '@/views/components/cO2View/components/common/screen/energySaveComp/energySavingsInformation.vue'

import DeviceDataView from "@/views/components/cO2View/components/common/DeviceDataView";
import DeviceDataChartView from "@/views/components/cO2View/components/common/DeviceDataChartView2.vue";
import Prototype from "@/views/device/monitor/components/Prototype/index";

export default {
  mixins: [ BaseView ], // 继承父模块
  components: {
    DeviceDataChartView,
    ElectricitySummary,
    KeyIndicators,
    CoolingLoad,
    EnergyConsumption,
    DynamicScop,
    OutletWaterTemperature,
    CirculatingPumpFrequency,
    EnergySavingsInformation,
    DataOverview,

    DeviceDataView,
    Prototype,      // 组态图
  },
  data() {
    return {
      // 用电概览
      electricitySummary: [],
      // 关键指标
      keyIds: "",
      // 冷负荷趋势
      coolLoad: [],
      // 冷冻水出水温度
      coolWaterTemperature: [],
      // 循环泵频率
      pumpFrequency: [],
      // 动态sop
      scop: [],
      //bim
      bimData: {},
      //系统总能耗
      energyConsumption: {},
      //色彩汇总
      colors: ["2294FE","F0D789"] ,
      //节能统计
      energySavingsInformation: {},
      //数据总览
      dataOverview: {},
      //组件显示控制
      componentShow: {
        energySavingsInformation: true,//节能统计
        energyConsumption: true,//系统总能耗
        coldLoadTrend: true,//冷负荷趋势
        coolWaterTemperature: true,//冷冻水出水温度
        pumpFrequency: true,//循环泵频率
        dataOverview: true,//数据总览
        electricitySummary: true,//用电概览
        KeyIndicators: true,//关键指标
        dynamicScop: true//动态SCOP
      },
      //组态图数据配置
      resourceData: {}
    };
  },
  computed: {
    subLinks() {
      return this.initSubLinks("EnergyMgt", "/energyMgt/");
    },
  },
  mounted() {
    let screenEnergySave = {};
    try {
      screenEnergySave = JSON.parse(this.projectSettings.screenEnergySave);
    } catch (ex) {
      // pass
    }
    this.electricitySummary = screenEnergySave.electricitySummary || [];
    this.keyIds = screenEnergySave.keyIds || "";
    this.coolLoad = screenEnergySave.coolLoad || [];
    this.coolWaterTemperature = screenEnergySave.coolWaterTemperature || [];
    this.pumpFrequency = screenEnergySave.pumpFrequency || [];
    this.scop = screenEnergySave.scop || [];
    this.bimData = screenEnergySave.bimData || {};
    this.energyConsumption = screenEnergySave.energyConsumption || {};
    this.energySavingsInformation = screenEnergySave.energySavingsInformation || {};
    this.dataOverview = screenEnergySave.dataOverview || {};
    this.componentShow = screenEnergySave.componentShow || {};
    this.resourceData = screenEnergySave.resourceData || {};
  },
  methods: {
  },
};
</script>

<style scoped lang="scss">
.co2_body {
  .btm {
    display: flex;
    justify-content: space-between;
    align-items: center;
    &_item {
      width: calc(50% - 8px);
    }
  }
  .co2_main_left,.co2_main_right {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
  }
  .fb {
    bottom: 0;
    .co2_energy_carbon {
      padding: 0;
      bottom: 60px;
      min-height: fit-content;
      left: auto;
      right: auto;
      width: 100%;
    }
  }


  //框架样式覆盖
  .iframe {
    width: 100%;
    height: 604px;
    pointer-events: auto;
  }
  .prototype {
    pointer-events: auto;
    width: 100%;
    height: 100%;
  }

  //bim图大屏展示
  .ft {
    height: 60%;
    z-index: 190;
    .iframe {
      height: 100%;
    }
  }
}
</style>
