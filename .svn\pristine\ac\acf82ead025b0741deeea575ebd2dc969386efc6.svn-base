<template>
  <div>
    <div class="show" v-if="!show">
      <div class="left">
        <Title tit="系统设备状态">
          <el-input placeholder="请输入内容" v-model="input" class="input-with-select">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input>

          <ul class="listul">
            <li v-for="(item, index) in items" :key="index" class="listli">
              <input class="listliinput1" type="radio" :id="'radio' + index" @click="changedevice(item.id, index)"
                v-model="selectedItem" :value="item.name" />
              <label class="listliinput2" :for="'radio' + index">
                {{ item.name }}
              </label>
              <!-- <div class="yuanqiu"></div> -->
            </li>
          </ul>
          <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
        </Title>
      </div>
      <div class="right-panel"></div>
      <iframe v-if='deviceId' class="right" :width="iframeWidth" :src="iframeSrc" :key="iframeSrc"
        frameborder="0"></iframe>
    </div>
    <div class="deviceshow" v-if="show">
      <div class="left-panel">

        <iframe class="lefti1" :src="`${iframeUrl}/#/card/left/BimDeviceSummaryGroupList`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/left/BimDeviceTypeAvailability`" frameborder="0"></iframe>
      </div>

    </div>
  </div>
</template>

<script>
import axios from "axios";
import Title from "@/components/common/Title.vue";
export default {
  components: {
    Title,
  },
  data() {
    return {
      iframeUrl,
      input: "",
      iframeWidth: '350px',
      selectedItem: null, // 用于存储被选中的项
      allSelected: false, // 全选状态
      items: [
        { name: "冷却塔/B3-CAD-1", checked: false },
        { name: "冷却塔/B3-CAD-2", checked: false },
        // 省略其他重复项...
      ],

      // data: {
      //   name: [], //当前项目所有的name(去重)
      //   build: [],  //当前项目所有的buildId(去重)
      //   floor: [],  //当前项目所有的floorId(去重)
      //   park: [],  //当前项目所有的parkId(去重)
      // },
      deviceId: '',
      show: true
    };
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl + `/#/card/deviceDetails?deviceId=${this.deviceId}`;
    }
  },
  mounted() {
    this.fetchDevices(602, 'DT');
    var that = this;
    window.addEventListener("message", function (event) {
      let data = event && event.data && event.data.message ? event.data.message.name : ''
      console.log(data);
      if (data == "open") {
        that.iframeWidth = "1335px"
      } else if (data == "close") {
        that.iframeWidth = "338px"
      }
    })
  },
  methods: {
    changedevice(id, index) {
      console.log(id, 112);
      this.deviceId = id
      if (index == 0) {
        this.sendToUE4('照明配电箱1')
      } else if (index == 1) {
        this.sendToUE4('照明配电箱2')
      }

    },
    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    childMethod(param1, param2) {
      if (param2 == 'close') {
        this.show = true
      } else {
        this.show = false
        console.log(param1, param2, 112);
        this.fetchDevices(param1, param2)
        this.deviceId = ''
      } this.selectedItem = null
    },
    getsblist() {
      const token = localStorage.getItem('token');
      console.log(token, 112);
    },
    async fetchDevices(resourceId, deviceTypes) {

      try {
        // 从 localStorage 中获取 token
        const token = localStorage.getItem('token');
        console.log(token, 112);
        // 如果没有 token，直接返回
        if (!token) {
          console.error('Token not found in localStorage');
          return;
        }

        const response = await axios.get(this.iframeUrl + '/api/device/api/resourceDeviceList', {
          params: {
            buildingId: 1,
            resourceId: resourceId,
            deviceTypes: deviceTypes
          },
          // 将 token 放入请求头中
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        console.log(response, 112);
        this.items = response.data.data;

      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    },
    toggleAllSelection() {
      if (this.allSelected) {
        this.selectedItems = this.items.map((item) => item.name);
      } else {
        this.selectedItems = [];
      }
    },
    checkItemSelection() {
      this.allSelected = this.selectedItems.length === this.items.length;
    },
  },
  watch: {
    allSelected(newVal) {
      this.toggleAllSelection();
    },
    selectedItems(newVal) {
      this.checkItemSelection();
    },
  },
};
</script>

<style lang="less" scoped>
.deviceshow {
  .left-panel {
    position: fixed;
    top: 68px;
    left: 15px;
    z-index: 99;
    width: 336px;
  }


  .lefti1 {
    width: 336px;
    height: 474px;
  }

  .lefti2 {
    width: 336px;
    height: 324px;
  }
}

.left {
  padding-left: 16px;
  padding-top: 14px;
  background: url("../assets/image/beijingzhe.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 336px;
  height: 980px;
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 2;
  text-align: center;

  .input-with-select {
    width: 305px;
    height: 43px;
  }

  .select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 305px;
    height: 42px;
    margin-left: 8px;
    margin-top: 8px;
    padding-left: 33px;
    background: url("../assets/image/selectbgc.png") !important;

    .selectAll {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #7dffd9;
      padding-left: 13px;
    }
  }

  .listul {
    height: 780px;
    overflow-y: scroll;
    width: 100%;

    &::-webkit-scrollbar {
      width: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #0f2459;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
    }
  }

  .listli {

    display: flex;
    align-items: center;
    margin-left: 8px;
    margin-top: 1px;
    width: 305px;
    height: 42px;
    background: url("../assets/image/selectbgc.png") !important;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #7dffd9;
    padding-left: 33px;
    cursor: pointer;

    .listliinput2 {
      padding-left: 14px;
      cursor: pointer;
    }

    .yuanqiu {
      cursor: pointer;
      width: 11px;
      height: 11px;
      background-color: #a0fdda;
      border-radius: 50%;
      margin-left: 17px;
    }
  }

  .zongshu {
    margin-top: 12px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #abe3f4;

    .dangq {
      color: #98f1d2 !important;
    }

    .dangqs {
      color: #fff !important;
    }
  }
}

.right {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
}

.right-panel {
  overflow: hidden;
  position: fixed;
  z-index: 1;
  right: 10px;
  width: 350px;
  top: 70px;
  height: 980px;
  // background: url("../assets/image/right.png");
  background-size: 100% 100%;
}

/deep/ .el-input__wrapper {
  background: url("../assets/image/inputbeij.png") !important;
}

/deep/ .el-input__inner {
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #b5eff7 !important;
}
</style>
