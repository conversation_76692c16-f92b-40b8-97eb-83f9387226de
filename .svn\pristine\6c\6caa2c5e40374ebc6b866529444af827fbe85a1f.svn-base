<template>
  <FitScreen :width="1920" :height="1080">
    <iframe src="https://hailuo.yuankong.org.cn:8060/#/login?autoLogin=1&username=admin&password=lanxing121%21" frameborder="0"
      v-show="false"></iframe>
    <div class="login_body">
      <img src="../assets/image/loginlogo.png" class="logo" alt="" />
      <div class="box">
        <div class="tit">
          <div class="title">嘉定海螺智慧园区综合管理平台</div>
          <div class="title1">Smart park integrated management platform</div>
        </div>

        <el-form ref="form" :model="form" :rules="rules" class="form">
          <el-form-item prop="username" class="formitem">
            <el-input v-model="form.username" class="input" placeholder="请输入账号" />

          </el-form-item>
          <img class="user1" src="../assets/image/user1.png" alt="">
          <el-form-item prop="password" class="formitem">
            <el-input v-model="form.password" placeholder="请输入密码" />
          </el-form-item>
          <img class="suo" src="../assets/image/suo.png" alt="">
          <el-form-item prop="code" class="formitem">
            <el-input v-model="form.code" style="width: 63%" placeholder="验证码" />
            <!-- <div class="login-code">
              <img :src="codeUrl" @click="getCode" class="login-code-img" />
            </div> -->
          </el-form-item>
          <img class="anquan" src="../assets/image/anquan.png" alt="">
          <div class="wangjimima">
            <el-form-item prop="remember">
              <el-checkbox v-model="remember">记住密码</el-checkbox>
            </el-form-item>
            <div class="wanji">忘记密码?</div>
          </div>

          <el-form-item>
            <!-- <el-button type="primary" class="login_btn" @click="loginHandler"
              >登录</el-button
            > -->
            <div class="denglu">
              <div class="de1">
                <img class="de1img" src="../assets/image/youxiangliang.png" alt="">
              </div>
              <div class="de2" @click="login">登录</div>
            </div>
          </el-form-item>
        </el-form>
        <div class="fblv">推荐分辨率: 1920*1080PX (100%字体缩放)</div>
        <div class="yuankong">Copyright© 2024 All Rights Reserved.上海源控</div>
      </div>
    </div>
  </FitScreen>
</template>

<script>
import axios from "axios";
import { baseConfigs, publicKey, captchaImage, apiLogin } from "../api/admin";
import { encrypt, decrypt } from "@/utils/jsencrypt";
export default {
  name: "Login",
  data() {
    return {
      // 表单对象
      codeUrl: "",
      form: {
        username: "",
        password: "",
        rememberMe: false,
        code: "",
        uuid: "",
      },
      publicKey: "",

      // // 规则对象
      // rules: {
      //   username: [
      //     {
      //       required: true,
      //       message: "请输入账号",
      //       trigger: "blur",
      //     },
      //   ],
      //   password: [
      //     {
      //       required: true,
      //       message: "请输入密码",
      //       trigger: "blur",
      //     },
      //   ],
      // },
    };
  },
  created() {
    // 去本地取一下之前存入的账号和密码 如果取到了 赋值操作
    // this.loginHandlers();
    this.fetchDevices();
  },
  methods: {
    async fetchDevices() {
      try {
        const response = await axios.post('https://hailuo.yuankong.org.cn:8060/api/apiLogin?username=admin&password=jVR4xs4YlL5Wke9CsP%2F1k5ez%2B9wsAP4Of6v7J7YjV4spAVNVj5Gvx52L043Rfg3bpFbNlypRDas9FKyTcRNynmxqBsLoeh2%2FRY7v9P4hG3%2BIBXJkKVKWTYY9MAvNEsGYsRqkG24sHw5DbcOvknHttxUN1vPVEVAxOt3gbO5GNC0%3D&code=123&uuid=123 ', {
          // params: {
          //   buildingId: 1,
          //   resourceId: 602,
          //   deviceTypes: 'DT'
          // }
        });

        console.log(response.data.token, 112);
        // 将 token 存储到 localStorage
        localStorage.setItem('token', response.data.token);
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    },
    login() {

      this.$router.push('/home');
    },
    loginHandler() {
      const password = encrypt(this.form.password, this.publicKey);
      const data = apiLogin({
        username: this.form.username,
        password: password,
        code: this.form.code,
        uuid: this.form.uuid,
      });
      console.log(data);
    },
    async loginHandlers() {
      await baseConfigs();
      const data = await publicKey();

      this.publicKey = data.publicKey;
      const res = await captchaImage();
      this.codeUrl = "data:image/gif;base64," + res.img;
      console.log(this.codeUrl, "captchaImage");
      this.form.uuid = res.uuid;
    },

    async getCode() {
      const res = await captchaImage();
      this.codeUrl = "data:image/gif;base64," + res.img;
      this.loginForm.uuid = res.uuid;
    },
  },
};
</script>

<style scoped lang="less">
.logo {
  width: 276px;
  height: 53px;
  position: fixed;
  top: 29px;
  left: 33px;
}

.login_body {
  background: url("../assets/image/background.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  width: 1920px;
  height: 1080px;

  .box {
    position: fixed;
    top: 242px;
    left: 1154px;
    width: 542px;
    height: 536px;

    .tit {
      padding-left: 20px;
    }

    .title {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 35px;
      color: #ffffff;

      background: linear-gradient(0deg, #009ff1 0%, #00e8cf 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .title1 {
      margin-top: 18px;
      margin-bottom: 23px;
      font-family: Source Han Sans SC;
      font-weight: 900;
      font-size: 13px;
      color: #00bff4;
      letter-spacing: 5.4px;
    }

    .form {
      background: url("../assets/image/loginbox.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      width: 542px;
      height: 453px;
      padding-top: 104px;
      padding-left: 46px;

      .formitem {
        width: 445px;
        height: 56px;

        .input {
          height: 100%;
        }
      }
    }
  }
}

/deep/ .el-form-item__label {
  color: #009ff1;
}

/deep/ .el-input__inner {
  color: #009ff1;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
  color: #52adf3;
}

// placeholder样式
/deep/ .el-input__inner::placeholder,
.el-textarea__inner::placeholder {
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
  color: #52adf3;
  letter-spacing: 5.4px;
}

/deep/ .el-input__wrapper {
  background: url("../assets/image/inputbgc.png");

  // 445px x 56px
  width: 445px;
  height: 56px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  border: none;
  box-shadow: none;
  padding-left: 81px;
}

/deep/ .el-input__wrapper.is-focus {
  box-shadow: none;
}

/deep/.el-input__wrapper:hover {
  box-shadow: none;
}

/deep/ .el-checkbox {
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 16px;
  color: #52adf3 !important;
}

.wangjimima {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .wanji {
    cursor: pointer;
    margin-bottom: 18px;
    font-family: Source Han Sans SC;
    font-weight: 400;
    font-size: 16px;
    color: #52adf3;
    margin-right: 76px;
  }
}

.login-code-img {
  margin-left: 25px;
  width: 119px;
  height: 100%;
  margin-top: 10px;
}

.denglu {
  cursor: pointer;
  display: flex;
  width: 100%;
  margin-left: 40px;

  .de1 {
    background: url("../assets/image/denglub1.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 127px;
    height: 43px;
    display: flex;
    align-items: center;
    justify-content: center;

    .de1img {
      width: 41px;
      height: 27px;
    }
  }

  .de2 {
    background: url("../assets/image/denglub2.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 221px;
    height: 43px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 16px;
    color: #52ADF3;
  }
}

.user1 {
  position: absolute;
  top: 217px;
  left: 78px;

}

.suo {
  position: absolute;
  top: 288px;
  left: 78px;
}

.anquan {
  position: absolute;
  top: 368px;
  left: 78px;
}

.fblv {
  margin-left: 110px;

  margin-top: 8px;
  margin-bottom: 13px;
  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 15px;
  color: #79CAFF;
}

.yuankong {
  margin-left: 94px;

  font-family: Source Han Sans SC;
  font-weight: 400;
  font-size: 15px;
  color: #79CAFF;
}
</style>
