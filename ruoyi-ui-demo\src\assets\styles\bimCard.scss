.right-card-box {
  width: 336px;
  background: linear-gradient(270deg, #0b274a -100%, rgba(11, 39, 74, 0.8) 78%, rgba(11, 39, 74, 0) 110%);
  padding: 12px;
  box-sizing: border-box;
  border-radius: 8px;
  .right-card-title {
    position: relative;
    height: 36px;
    z-index: 221;
    padding-left: 20px;
    padding-top: 5px;
    background: url('/image/co2_nav_bg.png') no-repeat 0;
    background-size: auto 100%;
    font-family: pangmenzhendao;
    font-size: 20px;
    
    .title-left {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 16px;
      color: #CCFFFE;
      cursor: pointer;
      img {
        width: 30px;
        height: 30px;
      }
    }
  }
  .right-card-title-sub {
    position: absolute;
    top: 18px;
    font-family:  MiSans;
    font-weight: 400;
    font-size: 14px;
    color: #C8DDF1;
    text-align: left;
    font-style: normal;
    z-index: 999;
  
  }
  
  .by-btn {
    background: url('/image/card/by_btn_bg.png') no-repeat;
    background-size: 100% 42px;
    height: 42px;
    line-height: 41px;
    text-align: center;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 18px;
    color: #79FFFF;
    cursor: pointer;
  }
}

.bim-tabs-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  &>div {
    width: 49%;
    height: 36px;
    background-color: rgba(20,78,149,.4);
    text-align: center;
    line-height: 35px;
    border-radius: 6px;
    cursor: pointer;
  }
  .sub-title {
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 12px;
    color: #14F4E9;
    margin-right: 8px;
  }
  .number {
    font-weight: 500;
    font-size: 19px;
    color: #FFFFFF;
  }
  .active {
    background-color: rgba(20,78,149,.8);
  }
}
.bim-tabs-box2 {
  display: flex;
  justify-content: space-between;
  &>div {
    width: 49%;
    height: 65px;
    text-align: center;
    border-radius: 6px;
  
  }
  .sub-title {
    margin-top: 10px;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 17px;
    color: #FFFFFF;
  
  }
  .title-bef1 {
    display: flex;
    align-items: center;
    img{
      margin-top: 10px;
      margin-left: 10px;
    }
    .sub-title::before {
      background-color: #FF6142;
    }
    .number {
      color: #FF6142;
    }
  }
  .title-bef2 {
    .sub-title::before {
      background-color: #FFFF00;
    }
    .number {
      color: #FFFF00;
    }
  }
  .title-bef3 {
    .sub-title::before {
      background-color: #00FFC0;
    }
    .number {
      color: #00FFC0;
    }
  }
  .number {
    font-weight: bold;
    font-size: 26px;
  }
  .active {
    // background-color: rgba(20,78,149,.8);
  }
}
.middle-card-title {
  margin-bottom: 16px;
  .title-left {
    width: 214px;
    height: 30px;
    padding-left: 34px;
    background: url('/image/card/middle_title_bg.png') no-repeat;
    background-size: 100% 30px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 22px;
    color: #FFFFFF;
  }
  .title-close {
    width: 20px;
    height: 20px;
    background: url('/image/card/title_close.png') no-repeat;
    background-size: 20px 20px;
    cursor: pointer;
  }
}
.flex-between {
  display: flex;
  justify-content: space-between;
}
.card-table {
  width: 100%;
  overflow-y: auto;
  background-color: transparent;
  color: #ffffff;
  &::before,.el-table__fixed-right::before {
    height: 0;
  }
  ::v-deep .el-table__fixed-right::before {
    height: 0;
  }
  //::v-deep .el-table__header-wrapper thead tr th,::v-deep .el-table__fixed-header-wrapper thead tr th{
  //  background-color: rgba(34,85,129,.7);
  //  font-family: DingTalk JinBuTi;
  //  font-weight: 400;
  //  font-size: 16px;
  //  color: #B1F2F2;
  //  border: none;
  //}
  //::v-deep .el-table__body-wrapper tbody tr td,::v-deep .el-table__fixed-body-wrapper tbody tr td{
  //  border: none;
  //  padding: 12px;
  //  font-family: Source Han Sans SC;
  //  font-weight: 400;
  //  font-size: 13px;
  //  color: #FFFFFF;
  //}
  ::v-deep .el-table__body-wrapper .even-row,::v-deep .el-table__fixed-body-wrapper .even-row {
    background-color: rgba(34,85,129,.4);
  }
  ::v-deep .el-table__body-wrapper .odd-row,::v-deep .el-table__fixed-body-wrapper .odd-row {
    background-color: transparent;
  }
  ::v-deep .el-table--enable-row-hover,::v-deep .el-table__body tr:hover>td {
    background-color: rgba(34,85,129,.7);
  }
  .even-row:hover,.odd-row:hover {
    background-color: rgba(34,85,129,.7);
  }
}

.left-card-box {
  width: 336px;
  background: linear-gradient(90deg, #0b274a -100%, rgba(11, 39, 74, 0.8) 78%, rgba(11, 39, 74, 0) 110%);
  border-radius: 10px;
  padding: 12px;
  box-sizing: border-box;
  border-radius: 8px;
  position: relative;
 
  .left-card-title {
    position: relative;
    height: 36px;
    z-index: 221;
    padding-left: 20px;
    padding-top: 5px;
    background: url('/image/co2_nav_bg.png') no-repeat 0;
    background-size: auto 100%;
    font-family: pangmenzhendao;
    font-size: 20px;
  }
 }
.bim-search-box {
  width: 100%;
  //border: none;
  //background-color: #0DD3A3;
}
.device-list-box {
  margin-top: 6px;
  .device-list-item {
    background: linear-gradient(to right, rgba(12,51,91,.5), rgba(48,96,131,.5));
    margin-bottom: 2px;
    padding: 10px 20px;
    font-size: 14px;
    color: #6EEADF;
    cursor: pointer;
    ::v-deep .el-checkbox {
      margin-bottom: 0;
    }
    .device-name {
      display: inline-block;
      margin-left: 16px;
      &:after {
        content: '';
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 6px;
        background-color: #6EEADF;
        margin-left: 20px;
      }
    }
  }
}




