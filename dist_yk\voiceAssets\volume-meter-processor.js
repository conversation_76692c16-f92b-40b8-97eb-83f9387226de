class VolumeMeterProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this._volume = 0;
    this._updateIntervalInMS = 25;
    this._nextUpdateFrame = this._updateIntervalInMS;
  }

  get intervalInFrames() {
    return this._updateIntervalInMS / 1000 * sampleRate;
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    if (input.length > 0) {
      const samples = input[0];
      let sum = 0;

      // 计算当前帧的音量
      for (let i = 0; i < samples.length; i++) {
        sum += samples[i] * samples[i];
      }
      const rms = Math.sqrt(sum / samples.length);
      this._volume = Math.max(rms, this._volume * 0.95);

      // 定期发送音量数据
      this._nextUpdateFrame -= samples.length;
      if (this._nextUpdateFrame < 0) {
        this._nextUpdateFrame += this.intervalInFrames;
        this.port.postMessage({
          volume: this._volume
        });
      }
    }

    return true;
  }
}

registerProcessor('volume-meter', VolumeMeterProcessor); 