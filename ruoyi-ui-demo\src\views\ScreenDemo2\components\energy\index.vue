<template>
  <div>
    <div class="co2_body">
      <div class="fl">
        <div class="co2_main_left">
          <!-- <CustCard title="项目概况" height="33.33%"><BuildingSummary v-if="cgBuildingFlag" title=""/></CustCard> -->
              
           <BimEnergyOverview class="mb5" />      
           <BimEnergyTrend class="mb5" /> 
           <BimEnergyRanking class="mb10" /> 

        </div>
      </div>


      <div class="fr">
        <div class="co2_main_right">
          <BimEnergyCarbonPrediction class="mb5"/> 
           <BimEnergyEmissionReduction class="mb5"/> 
           <BimEnergyAbnormal /> 
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import BimEnergyAbnormal from "@/views/cards/BIM/energy/new/BimEnergyAbnormal.vue";
import BimEnergyCarbonPrediction from "@/views/cards/BIM/energy/new/BimEnergyCarbonPrediction.vue";
import BimEnergyEmissionReduction from "@/views/cards/BIM/energy/new/BimEnergyEmissionReduction.vue";
import BimEnergyOverview from "@/views/cards/BIM/energy/new/BimEnergyOverview.vue";
import BimEnergyRanking from "@/views/cards/BIM/energy/new/BimEnergyRanking.vue";
import BimEnergyTrend from "@/views/cards/BIM/energy/new/BimEnergyTrend.vue";





export default {
  // mixins: [ BaseView ], // 继承父模块
  components: {
    BimEnergyAbnormal,
    BimEnergyCarbonPrediction,
    BimEnergyEmissionReduction,
    BimEnergyOverview,
    BimEnergyRanking,
    BimEnergyTrend,
  },
  data() {
    return {
 
    };
  },
  computed: {

  },
  mounted() {
  
  },
  methods: {
  },
};
</script>

<style lang="scss" scoped>
::v-deep {
  .page_nav {
    background: none;
  }
}
.co2_main_right{
  // padding-left: 68px;
  position: absolute;
  right: 0;
}
.co2_main .el-input {
    width: 90px;
}
</style>
