<template>
  <div>
    <div class="nengyuan" v-if="show">
      <div class="left-panel">
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/left/BimSecuritySummaryGroupList`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/left/BimWarningListSmall`" frameborder="0"></iframe>

      </div>
      <div class="right-panel">
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/right/BimDeviceSubentryAvailability`" frameborder="0"></iframe>

      </div>
    </div>
    <div class="show" v-if="sblist.length && !show">
      <div class="left">
        <!-- {{ sblist }} -->
        <Title tit="设备列表">
          <!-- <el-input placeholder="请输入内容" v-model="input" class="input-with-select">
            <i slot="prefix" class="el-input__icon el-icon-search"></i>
          </el-input> -->
          <!-- {{ iframeSrc }} -->
          <ul class="listul">
            <li v-for="(item, index) in sblist" :key="index" class="listli">
              <!-- 绑定公共的 selectedItem，确保每次只能选中一个 -->
              <input class="listliinput1" type="radio" :id="'radio' + index" @click="changedevice(item.deviceId, index)"
                v-model="selectedItem" :value="item.id" />
              <label class="listliinput2" :for="'radio' + index">
                {{ item.name }} {{ item.roomId }}
              </label>
            </li>
          </ul>
          <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
        </Title>
      </div>
      <!-- <div class="right-panel"></div> -->
      <iframe v-if='deviceId' class="right" :width="iframeWidth" :src="iframeSrc" :key="iframeSrc"
        frameborder="0"></iframe>
    </div>
    <iframe v-if='componentTag' :key="componentTag" class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
    <!-- {{ iframeSrc }} -->
  </div>
</template>

<script>
export default {
  props: ["sblist"], // 声明接收来自父组件的 sblist 数据
  data() {
    return {

      iframeUrl,
      componentTag: '',
      show: true,
      isshow: false,
      iframeWidth: '348px',
      // sblist: [
      //   // { id: 1, name: "Device 1" },
      //   // { id: 2, name: "Device 2" },
      //   // { id: 3, name: "Device 3" },
      // ],
      selectedItem: 0,
      deviceId: '',
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl + `/#/card/newDeviceDetails?deviceId=${this.deviceId}`;
    },
    // iframeSrc1() {
    //   return this.iframeUrl + `/#/card/deviceDetails?deviceId=${this.deviceId}`;
    // }
  },
  watch: {
    // 监听 sblist 变化
    sblist: {
      handler(newVal, oldVal) {
        // 当 sblist 发生变化时，将 deviceId 设置为空
        this.deviceId = '';
        console.log("sblist changed, deviceId reset to null");
      },
      deep: true, // 深度监听 sblist，如果 sblist 是对象或数组
      immediate: true, // 立即触发 handler，页面加载时
    },
  },

  mounted() {
    // this.fetchDevices(602, 'DT');
    var that = this;
    window.addEventListener("message", function (event) {
      let data = event && event.data && event.data.message ? event.data.message.name : ''
      console.log(data);
      if (data == "open") {
        that.iframeWidth = "1335px"
      } else if (data == "close") {
        that.iframeWidth = "338px"
      }
    })
    ue.interface.setSliderValue = (value) => {
      console.log(value, 'ue点击拿到的值');
      if (!isNaN(Number(value.data))) {
        let did = value.data; // 如果是数字，则赋值
        const result = this.sblist.filter(item => item.id == did);
        this.deviceId = result[0].deviceId

        console.log(this.deviceId, 'ue点击拿到的id');
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
  },
  methods: {
    changedevice(id, index) {
      // 打印选中的单选按钮的值
      console.log("选中的值:", this.selectedItem);
      // 或者打印设备ID和索引
      console.log("设备ID:", id, "索引:", index);
      this.deviceId = id
    },
    anquan(index) {
      console.log(index, '安全');

      if (index == 0) {
        this.componentTag = 'card/DeviceParking'
        this.show = false
        // } else if (index == 1) {
        //   this.componentTag = ''
        // } else if (index == 2) {
        //   this.componentTag = 'card/securityMgt/DZXG'
        // } else if (index == 3) {
        //   this.componentTag = 'card/securityMgt/RQBJ'
        // } else if (index == 4) {
        //   this.componentTag = 'card/securityMgt/MJGL'
        // } else if (index == 5) {
        //   this.componentTag = 'card/securityMgt/SPJK'
        // 
      } else if (index == 9) {
        this.componentTag = ''
        this.show = true
        this.isshow = false
      } else {
        this.componentTag = ''
        this.show = false
        this.isshow = true
      }

      //获取数据

      console.log(this.iframeSrc);
    },
  }
};
</script>

<style scoped lang="less">
.show {
  .left {
    padding-left: 16px;
    padding-top: 14px;
    background: url("../assets/image/beijingzhe.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 336px;
    height: 980px;
    position: fixed;
    top: 70px;
    left: 10px;
    z-index: 2;
    text-align: center;

    .input-with-select {
      width: 305px;
      height: 43px;
    }

    .select {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 305px;
      height: 42px;
      margin-left: 8px;
      margin-top: 8px;
      padding-left: 33px;
      background: url("../assets/image/selectbgc.png") !important;

      .selectAll {
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 14px;
        color: #7dffd9;
        padding-left: 13px;
      }
    }

    .listul {
      height: 780px;
      overflow-y: scroll;
      width: 100%;

      &::-webkit-scrollbar {
        width: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f1f1f1;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #0f2459;
      }

      &::-webkit-scrollbar-thumb:hover {
        background-color: #555;
      }
    }

    .listli {

      display: flex;
      align-items: center;
      margin-left: 8px;
      margin-top: 1px;
      width: 305px;
      height: 42px;
      background: url("../assets/image/selectbgc.png") !important;
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #7dffd9;
      padding-left: 33px;
      cursor: pointer;

      .listliinput2 {
        padding-left: 14px;
        cursor: pointer;
      }

      .yuanqiu {
        cursor: pointer;
        width: 11px;
        height: 11px;
        background-color: #a0fdda;
        border-radius: 50%;
        margin-left: 17px;
      }
    }

    .zongshu {
      margin-top: 12px;
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #abe3f4;

      .dangq {
        color: #98f1d2 !important;
      }

      .dangqs {
        color: #fff !important;
      }
    }
  }

}

.componentTag {
  position: fixed;
  z-index: 1;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
}

.nengyuan {
  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    // background: url("../assets/image/left.png");
    // background-size: 100% 100%;
  }

  .lefti1 {
    width: 336px;
    height: 300px;
  }

  .lefti2 {
    width: 336px;
    height: 436px;
  }



  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
  }
}

.right {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
}
</style>