<template>
  <div class="left-card-box">
    <div class="left-card-title" v-text="title"></div>
    <!-- 能源类型切换下拉框 -->
    <div class="energy-selector mb10">
      <el-select v-model="selectedEnergyType" @change="onEnergyTypeChange" size="mini"
        style="width: 90px;background-color: #213353;">
        <el-option v-for="item in energyTypeOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div :class="{ 'chart-box': autoHeight }">
      <div ref="chartThree" :style="{ 'height': (`${autoHeight ? '100%' : height}`) }"></div>
      <!-- 加载状态和空数据提示 -->
      <div v-if="loading" class="chart-loading">
        <i class="el-icon-loading"></i>
      </div>
      <div v-if="!loading && isEmpty" class="chart-empty">
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import BaseChart from "@/views/ScreenCommBase/components/common/BaseChart.vue";
import {
  buildingGroupList,
  buildingEnergyTypeSummary,
  buildingEnergyDataList,
} from "@/api/energy/apis";
import { log } from '../../../../../components/byEditor/js/utils/mqtt';
export default {
  props: {
    "title": {
      type: String,
      default: "年度分区能耗排名",
    },
    "height": {
      type: String,
      default: "252px",
    },
    "autoHeight": {
      type: Boolean,
      default: false
    }
  },
  components: {
    BaseChart
  },
  data() {
    return {
      loading: false,
      isEmpty: false,
      curBuilding: this.gf.getCurBuilding(),
      chartOption: {},
      chart: null,
      // 能源类型选择
      selectedEnergyType: 'electricity',
      energyTypeOptions: [
        { value: 'electricity', label: '用电', unit: 'kwh', deviceType: 'electricity' },
        // { value: 'water', label: '用水', unit: '吨', deviceType: 'water' }
      ],
      // 时间范围
      dateRange: null,
      // 区域用电/区域用水
      groupName: '区域用电',
      // 最大显示项数
      maxDisplayItems: 5
    }
  },
  computed: {
    // 当前选择的能源类型配置
    currentEnergyConfig() {
      return this.energyTypeOptions.find(item => item.value === this.selectedEnergyType) || this.energyTypeOptions[0];
    },
    // 当前单位
    currentUnit() {
      const unit = this.currentEnergyConfig.unit || '';
      // 标准化单位显示
      if (unit.toLowerCase() === 'kwh') {
        return 'kWh'; // 正确的电能单位格式
      }
      return unit;
    },
    // 当前设备类型
    currentDeviceType() {
      return this.currentEnergyConfig.deviceType;
    }
  },
  watch: {
    // 监听时间范围变化
    dateRange() {
      this.getAreaRankingData();
    }
  },
  created() {
  },
  mounted() {
    // 首先初始化能源类型配置
    this.initEnergyTypeOptions().then(() => {
      // 初始化完成后获取区域能耗数据
      this.getAreaRankingData();
    }).catch(error => {
      console.error("初始化能源类型失败:", error);
      // 使用默认能源类型获取数据
      this.getAreaRankingData();
    });

    // 添加窗口大小变化事件监听
    this.resizeHandler = () => {
      this.chart && this.chart.resize();
    };
    window.addEventListener('resize', this.resizeHandler);
  },
  beforeDestroy() {
    // 组件销毁前清理事件监听器和图表实例
    window.removeEventListener('resize', this.resizeHandler);
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  },
  methods: {
    // 格式化数值，对大数值进行处理
    formatValue(value) {
      if (value >= 10000) {
        // return (value / 10000).toFixed(2) + '万';
        return value.toString();
      }  else {
        return value.toString();
      }
    },
    // 能源类型切换事件
    onEnergyTypeChange() {
      // 根据能源类型更新groupName
      this.groupName = this.selectedEnergyType === 'electricity' ? '区域用电' : '区域用水';
      this.getAreaRankingData();
    },
    // 获取区域能源排名数据
    getAreaRankingData() {
      this.loading = true;
      this.isEmpty = false;
      // 调用接口获取数据
      buildingEnergyTypeSummary({
        buildingId: this.curBuilding.id,
        from: this.dateRange ? this.$moment(this.dateRange[0]).format("YYYY-MM-DD") : this.$moment().startOf("year").format("YYYY-MM-DD"),
        to: this.dateRange ? this.$moment(this.dateRange[1]).format("YYYY-MM-DD") : this.$moment().format("YYYY-MM-DD"),
        deviceTypes: this.groupName,
        displayType: "year",
      }).then(({ data }) => {
        console.log(data, 'data');



        // 处理接口返回的数据
        const categoryData = [];



        // 获取对应分类下的数据
        const categoryItems = data[this.groupName];

        if (categoryItems) {
          // 将每个子分类及其total值提取出来
          Object.keys(categoryItems).forEach(key => {
            if (categoryItems[key] && typeof categoryItems[key].total !== 'undefined') {
              categoryData.push({
                name: key,
                value: Math.round(categoryItems[key].total) // 四舍五入取整数
              });
            }
          });

          // 按能耗值从小到大排序
          categoryData.sort((a, b) => a.value - b.value);

          // 如果有数据，则更新图表
          if (categoryData.length > 0) {
            // 限制最大显示项数
            const limitedData = categoryData.slice(0, this.maxDisplayItems);
            this.updateRankingChart(limitedData);
            this.isEmpty = false;
          } else {
            console.warn("未获取到区域能耗数据");
            this.isEmpty = true;
            // 使用空数据更新图表
            this.updateRankingChart([]);
          }
        } else {
          console.warn(`未找到${mainCategory}分类数据`);
          this.isEmpty = true;
          this.updateRankingChart([]);
        }

        this.loading = false;
      }).catch(error => {
        console.error("获取区域能耗数据出错:", error);
        this.loading = false;
        this.isEmpty = true;
        this.updateRankingChart([]);
      });
    },
    // 更新排名图表
    updateRankingChart(categoryData) {
      // 检查是否有有效数据
      if (!categoryData || categoryData.length === 0) {
        this.isEmpty = true;
        // 清除现有图表
        if (this.chart) {
          this.chart.clear();
        }
        return;
      }

      this.isEmpty = false;

      // 使用API获取的数据
      let category = categoryData;

      // 计算所有项的总和作为总量
      let sum = category.reduce((acc, item) => acc + item.value, 0);
      // 给总和增加20%的空间
      let total = sum * 1.1;


      let datas = [];
      category.forEach(value => {
        datas.push(value.value);
      });

      // 图表配置
      const option = {
        xAxis: {
          max: total,
          splitLine: {
            show: false
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        grid: {
          left: 10,
          top: 20, // 设置条形图的边距
          right: 10,
          bottom: 5
        },
        yAxis: [{
          type: "category",
          inverse: false,
          data: category,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        }],
        series: [{
          // 内部条形图
          type: "bar",
          barWidth: 9,
          legendHoverLink: false,
          silent: true,
          itemStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 1,
              y2: 0,
              colorStops: [{
                offset: 0,
                color: "#2493FE" // 0% 处的颜色
              },
              {
                offset: 0.4,
                color: "#3ABDF2" // 100% 处的颜色
              },
              {
                offset: 0.8,
                color: "#4FE4E6" // 100% 处的颜色
              }
              ]
            }
          },
          // 修改点1：名称显示在左上角
          label: {
            show: true,
            position: [0, -20], // 左上角位置
            formatter: '{b}',
            textStyle: {
              color: '#C8DDF1',
              fontSize: 12,
              fontWeight: '400'
            },
            distance: 0 // 关闭自动位置
          },
          data: category,
          z: 1,
          animationEasing: "elasticOut"
        },
        {
          // 分隔线
          type: "pictorialBar",
          itemStyle: {
            color: "#000"
          },
          symbolRepeat: "fixed",
          symbolMargin: 12,
          symbol: "rect",
          symbolClip: true,
          symbolSize: [1, 11],
          symbolPosition: "start",
          symbolOffset: [0, 0],
          symbolBoundingData: total,
          data: category,
          z: 2,
          animationEasing: "elasticOut"
        },
        {
          // 外边框
          type: "pictorialBar",
          symbol: "rect",
          symbolBoundingData: total,
          itemStyle: {
            normal: {
              color: "none"
            }
          },
          // 修改点2：数值显示在右上角
          label: {
            show: true,
            position: [230, -8], // 右上角位置
            align: 'left',
            formatter: (params) => {
              // 根据当前能源类型显示对应单位，并格式化数值
              return this.formatValue(params.data) + this.currentUnit;
            },
            textStyle: {
              color: "#C8DDF1",
              fontSize: 12,
              fontWeight: '400'
            },
            distance: 0 // 关闭自动位置
          },
          data: datas,
          z: 0,
          animationEasing: "elasticOut"
        },
        {
          name: "外框",
          type: "bar",
          barGap: "-120%", // 设置外框粗细
          data: Array(category.length).fill(total), // 根据数据项数量创建对应长度的数组
          barWidth: 13,
          itemStyle: {
            normal: {
              color: "#082C54", // 填充色
              barBorderColor: "#124A78", // 边框色
              barBorderWidth: 1, // 边框宽度
              label: {
                show: false
              }
            }
          },
          z: 0
        }
        ]
      };

      // 初始化或更新图表
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartThree);
      }
      this.chart.setOption(option, true);
    },
    async initEnergyTypeOptions() {
      try {
        const energyTypes = await this.gf.getBuildingEnergyTypes(this.curBuilding.id);
        if (!energyTypes || energyTypes.length === 0) {
          console.warn("未获取到能源类型数据");
          return;
        }

        this.energyTypeOptions = energyTypes.map(item => {
          let label = item.typeName;
          // 如果只有"电"，则改为"用电"
          if (item.type === 'electricity') {
            label = '用电';
          } else if (item.type === 'water') {
            label = '用水';
          }
          return {
            label,
            value: item.type,
            unit: item.typeUnit,
            deviceType: item.type,
          };
        });

        // 默认选中第一个能源类型
        this.selectedEnergyType = this.energyTypeOptions[0]?.value || '';
      } catch (error) {
        console.error("获取能源类型失败:", error);
        // 可以设置一个默认选项，防止 UI 异常
        this.energyTypeOptions = [
          { label: "用电", value: "electricity", unit: "kWh", deviceType: "electricity" }
        ];
        this.selectedEnergyType = 'electricity';
      }
    },
  }
}
</script>
<style lang="scss" scoped>
@import "~@/assets/styles/bimCard.scss";
::v-deep .el-input {
    width: 90px!important;
  }
.energy-selector {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  position: absolute;
  top: 10px;
  left: 234px;
  z-index: 1200;

  ::v-deep .el-select {
    .el-input__inner {
      color: #fff;
      font-size: 12px;
    }

    .el-input__suffix {
      color: #fff;
    }
  }
}

.electrical-box {
  display: flex;
  justify-content: space-between;
  padding: 9px 7px;
}

.chart-loading,
.chart-empty {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.1);
  color: #C8DDF1;
  font-size: 14px;
}
</style>
