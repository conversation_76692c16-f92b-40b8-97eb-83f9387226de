<template>
  <div>
    <div class="nengyuan" v-if="show">
      <div class="left-panel">
        <iframe class="lefti1" :src="`${iframeUrl}/#/card/EnergyElectricityUse`"
          frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimEnergyUsageTrends`"
          frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimWaterUsageTrends`"
          frameborder="0"></iframe>
      </div>
      <div class="right-panel">
        <iframe class="lefti1" :src="`${iframeUrl}/#/card/EnergyCarbonEmission`"
          frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/EnergyCarbonPrediction`"
          frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimSummaryElectricity`"
          frameborder="0"></iframe>
      </div>
    </div>
    <iframe v-if='componentTag' class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
  </div>
</template>

<script>
export default {
  data() {
     return {
      iframeUrl,
      componentTag: '',
      show: true,
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl+`/#/card/energyMgt/${this.componentTag}`;
    }
  },
  methods: {
    nengyuan(index) {
      console.log(index,1212);
      this.show = false
      if (index == 0) {
        this.componentTag = 'electricity/summary'
      } else if (index == 1) {
        this.componentTag = 'electricity/subOption'
      } else if (index == 2) {
        this.componentTag = 'electricity/partition'
      } else if (index == 3) {
        this.componentTag = 'water/summary'
      } else if (index == 4) {
        this.componentTag = 'water/subOption'
      } else if (index == 5) {
        this.componentTag = 'water/partition'
      } else if (index == 6) {
        this.componentTag = 'summary/energy'
      } else {
        this.componentTag = ''
        this.show = true
      }
    },
  }
};
</script>

<style scoped lang="less">
.componentTag {
  position: fixed;
  z-index: 1;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
}

.nengyuan {
  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    // background: url("../assets/image/left.png");
    // background-size: 100% 100%;
  }

  .lefti1 {
    width: 336px;
    height: 300px;
  }

  .lefti2 {
    width: 336px;
    height: 330px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
  }
}
</style>