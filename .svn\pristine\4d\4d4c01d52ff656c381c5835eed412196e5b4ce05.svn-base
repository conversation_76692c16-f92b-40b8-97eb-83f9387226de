<template>
  <div>
    <SlidingPanel v-if="show" :showLeftPanel="true" :showRightPanel="true">
      <!-- 左侧内容 -->
      <template #left>

        <iframe class="item" :src="`${iframeUrl}/#/card/left/BimDeviceSummaryGroupList`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/left/BimWarningListSmall?type=device`" frameborder="0"></iframe>
      </template>

      <!-- 右侧内容 -->
      <!-- <template #right>
        <iframe class="item" :src="`${iframeUrl}/#/card/right/BimDeviceSubentryAvailability`" frameborder="0"></iframe>
      </template> -->
    </SlidingPanel>
    <div class="show" v-if="!show && isshow">
      <SlidingPanel v-if="!show && isshow" :showLeftPanel="true" :showRightPanel="true" :showLeftbg="true">
        <!-- 左侧内容 -->
        <template #left>
          <div class="item" style="height: 920px;">
            <img src="../assets/more.png" class="more" @click="more()" alt="">
            <Title :tit="sbtitle">
              <el-input placeholder="请输入内容" v-model="input" class="input-with-select">

              </el-input>
              <!-- {{ iframeSrc }} -->
              <ul class="listul" v-if="filteredList.length && !show">
                <li v-for="(item, index) in filteredList" :key="index" class="listli">
                  <!-- 绑定公共的 selectedItem，确保每次只能选中一个 -->
                  <!-- <input class="listliinput1" type="radio" :id="'radio' + index"
      @click="changedevice(item.deviceId, index)" v-model="selectedItem" :value="item.id" /> -->
                  <div :class="listactive == index ? 'listliinput2' : 'listliinput1'" :for="'radio' + index"
                    @click="changedevice(item.deviceId, index, true)">
                    {{ item.name }} {{ item.roomId }}
                  </div>
                </li>
              </ul>
              <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
            </Title>
          </div>
        </template>

        <!-- 右侧内容 -->
        <template #right>
          <iframe v-if='!deviceId && sbTag' class="item" :src="`${iframeUrl}/#/${sbTag}`" :key="sbTag"
            frameborder="0"></iframe>
          <transition name="expand" mode="out-in" v-if='deviceId'>
            <iframe :class="sbtitle == '视频监控设备列表' ? 'right2 item1' : 'right2 item1'" :width="iframeWidth" :src="iframeSrc"
              :key="iframeSrc" frameborder="0"></iframe>
          </transition>
          <img class="closedet" @click="closedet" v-if="deviceId" src="../assets/close.png" alt="">
        </template>
      </SlidingPanel>
    </div>

    <transition name="expand" mode="out-in">
      <iframe v-if='componentTag' :key="componentTag" class="componentTag" :src="`${iframeUrl}/#/${componentTag}`"
        frameborder="0"></iframe>
    </transition>

    <img class="close" @click="close" v-if="isclose" src="../assets/close.png" alt="">
  </div>
</template>

<script>
import axios from "axios";
import Title from "@/components/common/Title.vue";
import SlidingPanel from "@/components/common/SlidingPanel.vue";
export default {
  components: {
    SlidingPanel,
    Title,
  },
  props: ["sblist", "sbtitle"], // 声明接收来自父组件的 sblist 数据
  data() {
    return {
      input: '',
      filteredList: [],
      sbTag: '',//右侧的iframe
      isshow: false,
      iframeUrl,
      listactive: null,
      isclose: false,
      input: "",
      iframeWidth: '372px',
      selectedItem: null, // 用于存储被选中的项
      allSelected: false, // 全选状态
      items: [
        { name: "冷却塔/B3-CAD-1", checked: false },
        { name: "冷却塔/B3-CAD-2", checked: false },
        // 省略其他重复项...
      ],
      componentTag: '', //点击更多弹出的iframe

      // data: {
      //   name: [], //当前项目所有的name(去重)
      //   build: [],  //当前项目所有的buildId(去重)
      //   floor: [],  //当前项目所有的floorId(去重)
      //   park: [],  //当前项目所有的parkId(去重)
      // },
      deviceId: '',
      show: true
    };
  },
  computed: {
    filteredList() {
      return this.sblist.filter(item =>
        item.name.includes(this.input) || item.roomId.includes(this.input)
      )
    },
    iframeSrc() {
      return this.iframeUrl + `/#/card/deviceDetailCardBig?deviceId=${this.deviceId}`;
    }
  },
  watch: {
    // 监听 sblist 变化
    sblist: {
      handler(newVal, oldVal) {
        // 当 sblist 发生变化时，将 deviceId 设置为空
        this.deviceId = '';
        console.log("sblist changed, deviceId reset to null");
      },
      deep: true, // 深度监听 sblist，如果 sblist 是对象或数组
      immediate: true, // 立即触发 handler，页面加载时
    },
  },
  mounted() {
    this.fetchDevices(602, 'DT');
    var that = this;
    window.addEventListener("message", function (event) {
      let data = event && event.data && event.data.message ? event.data.message.name : ''
      console.log(data);
      if (data == "open") {
        that.iframeWidth = "1890px"
      } else if (data == "close") {
        that.iframeWidth = "372px"
      }
      if (event && event.data && event.data.type == 'alarmMessage') {
        console.log(event.data.param.deviceId, 142);
        that.deviceId = event.data.param.deviceId
      }
    })
    ue.interface.setSliderValue = (value) => {
      console.log(value, 'ue点击拿到的值');
      if (!isNaN(Number(value.data))) {
        let did = value.data; // 如果是数字，则赋值
        const result = this.sblist.map((item, index) => ({ item, index })) // 将元素和其下标一起打包
          .filter(obj => obj.item.id == did);      // 过滤匹配的元素
        console.log(result);
        this.deviceId = result[0].item.deviceId
        this.changedevice(result[0].item.deviceId, result[0].index, false)
        console.log(result[0].item.deviceId, 'ue点击拿到的id');
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };

  },
  methods: {
    closedet() {
      this.deviceId = '';
      // this.sbTag = ''
      this.iframeWidth = "372px"
    },
    more() {
      console.log(this.sbtitle);
      if (this.sbtitle == '给排水设备列表') {
        this.componentTag = 'card/pmtManagePage?type=PSXT'
      } else if (this.sbtitle == '新风空调设备列表') {
        this.componentTag = 'card/tabsManagePage?type=KTMD60'
      } else if (this.sbtitle == '通风机设备列表') {
        this.componentTag = 'card/tabsManagePage?type=TFJ11,TFJ41,TFJ21,TFJ20,TFJ14'
      } else if (this.sbtitle == '电梯系统设备列表') {
        this.componentTag = 'card/pmtManagePage?type=DT10'
      } else if (this.sbtitle == '照明系统设备列表') {
        this.componentTag = 'card/pmtManagePage?type=ZM'
      }
      if (this.componentTag) {
        this.isclose = true
      }else{
        this.isclose = false
      }

    },
    close() {
      this.componentTag = ''
      this.isclose = false
    },
    // changedevice(id, index) {
    //   console.log(id, 112);
    //   this.listactive = index
    //   this.deviceId = id
    //   if (index == 0) {
    //     this.sendToUE4('照明配电箱1')
    //   } else if (index == 1) {
    //     this.sendToUE4('照明配电箱2')
    //   }

    // },
    changedevice(id, index, flag) {
      this.iframeWidth = "372px"
      console.log(this.sblist, id, 112);
      this.listactive = index
      const result = this.sblist.find(item => item.deviceId == id);
      // 打印选中的单选按钮的值
      if (flag) {
        this.seedue(result)
      }

      console.log("选中的值:", this.selectedItem);
      // 或者打印设备ID和索引
      console.log("设备ID:", id, "索引:", index);
      this.deviceId = id

    },
    seedue(item) {

      // this.$emit('seedsb', item.buildId + item.floorId);
      console.log("单个设备", item,);

      this.sendToUE41('jujiao', item)
    },
    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UE收到的");
    },
    // childMethod(param1, param2) {
    //   if (param2 == 'close') {
    //     this.show = true
    //   } else {
    //     this.show = false
    //     console.log(param1, param2, 112);
    //     this.fetchDevices(param1, param2)
    //     this.deviceId = ''
    //   } this.selectedItem = null
    // },
    shebei(index) {
      this.deviceId = ''
      this.listactive = null
      console.log(index, '设备');
      if (this.sbtitle == '给排水设备列表') {
        this.sbTag = ''
      } else if (this.sbtitle == '新风空调设备列表') {
        this.sbTag = 'card/bimDevideInfoByType?type=KT&deviceTypes=KTMD60'
      } else if (this.sbtitle == '通风机设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=FJ&deviceTypes=TFJ11,TFJ21,TFJ14,TFJ20,TFJ41'
      } else if (this.sbtitle == '照明系统设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=ZM&deviceTypes=ZM11,ZM12,ZM13'
      } else if (this.sbtitle == '电梯系统设备列表') {
        this.sbTag = 'card/bimDevideInfoByType?type=DT&deviceTypes=DT10'
      } else if (this.sbtitle == '电力监控设备列表') {
        this.sbTag = ''
      }

      if (index == 0) {
        // this.isclose = true
        // this.componentTag = 'card/ParkingSystemTabs'
        // this.show = false
        // this.isshow = false

      } else if (index == 9) {
        this.componentTag = ''
        this.show = true
        this.isshow = false
      } else {
        this.componentTag = ''
        this.show = false
        this.isshow = true
        this.isclose = false
      }

      //获取数据

      console.log(this.iframeSrc);
    },
    getsblist() {
      const token = localStorage.getItem('token');
      console.log(token, 112);
    },
    async fetchDevices(resourceId, deviceTypes) {

      try {
        // 从 localStorage 中获取 token
        const token = localStorage.getItem('token');
        console.log(token, 112);
        // 如果没有 token，直接返回
        if (!token) {
          console.error('Token not found in localStorage');
          return;
        }

        const response = await axios.get(this.iframeUrl + '/api/device/api/resourceDeviceList', {
          params: {
            buildingId: 1,
            resourceId: resourceId,
            deviceTypes: deviceTypes
          },
          // 将 token 放入请求头中
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        console.log(response, 112);
        this.items = response.data.data;

      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    },
    toggleAllSelection() {
      if (this.allSelected) {
        this.selectedItems = this.items.map((item) => item.name);
      } else {
        this.selectedItems = [];
      }
    },
    checkItemSelection() {
      this.allSelected = this.selectedItems.length === this.items.length;
    },
  },
  watch: {
    allSelected(newVal) {
      this.toggleAllSelection();
    },
    selectedItems(newVal) {
      this.checkItemSelection();
    },
  },
};
</script>

<style lang="less" scoped>
.componentTag {
  position: fixed;
  z-index: 23;
  top: 70px;
  left: 1%;
  width: 98.2%;
  height: 895px;
}

.item {
  width: 336px;
  height: 340px;
  overflow: hidden;
  margin-bottom: 20px;
}

.expand-leave-active {
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-name: expandIn;
}

.expand-leave-active {
  animation-name: shrinkAndFade;
}

@keyframes expandIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
    transform-origin: center;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkAndFade {
  0% {
    transform: scale(1);
    opacity: 1;
    transform-origin: center;
  }

  100% {
    transform: scale(0.4);
    opacity: 0;
  }
}


.left-panel .item:nth-child(1) {
  height: 465px;
}

.left-panel .item:nth-child(2) {
  height: 400px;
}


.item1 {
  // width: 336px;
  height: 340px;
  overflow: hidden;
  margin-bottom: 20px;
}

.right1 {
  height: 626px !important;
  width: 714px !important;
  overflow: hidden;
}

.right2 {
  height: 884px !important;
  // width: 1356px !important;
  overflow: hidden;
}

.right-panel .item:nth-child(1) {
  height: 937px;
}

.closedet {
  position: absolute;
  z-index: 100;
  top: 4px;
  right: 4px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

// .right-panel .item:nth-child(3) {
//   height: 340px;
// }

.deviceshow {
  .left-panel {
    position: fixed;
    top: 68px;
    left: 15px;
    z-index: 99;
    width: 336px;
  }


  .lefti1 {
    width: 336px;
    height: 474px;
  }

  .lefti2 {
    width: 336px;
    height: 324px;
  }
}

.close {
  position: fixed;
  z-index: 100;
  top: 72px;
  right: 25px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.more {
  position: absolute;
  right: 10px;
  top: 20px;
  font-size: 16px;
  width: 49px;
  height: 21px;
  color: #fff;
  // text-shadow: 2px 2px 3px #000000;
  cursor: pointer;
}


.more:hover {
  color: #7dffd9;
  text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.1);
}

.show {
  padding-left: 16px;
  padding-top: 14px;
  // background: url("../assets/image/beijingzhe.png")
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 336px;
  height: 980px;
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 12;
  text-align: center;
  position: relative;


  .input-with-select {
    width: 305px;
    height: 32px;
    margin-bottom: 10px;
    margin-top: 5px;
  }


  .select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 305px;
    height: 42px;
    margin-left: 8px;
    margin-top: 8px;
    padding-left: 33px;
    background: url("../assets/image/selectbgc.png") !important;

    .selectAll {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #7dffd9;
      padding-left: 13px;
    }
  }

  .listul {
    height: 880px;
    overflow-y: scroll;
    width: 100%;

    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(151, 151, 151, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.4);
    }


  }

  .listli {
    white-space: nowrap;
    /* 不换行 */
    overflow: hidden;
    /* 隐藏溢出部分 */
    text-overflow: ellipsis;
    /* 使用省略号替代溢出的文本 */
    display: flex;
    align-items: center;
    // margin-left: 8px;
    margin-top: 1px;
    width: 305px;
    height: 42px;
    background: url("../assets/image/selectbgc.png") !important;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 14px;

    padding-left: 18px;
    cursor: pointer;
    text-align: left;

    .listliinput1 {
      width: 283px;
      height: 33px;
      padding-left: 25px;
      padding-right: 25px;
      line-height: 33px;
      cursor: pointer;
      color: #D0DEEE;
      background: url('../assets/act1.png');
      background-size: 100% 100%;
    }

    .listliinput2 {
      width: 283px;
      padding-right: 25px;
      line-height: 33px;
      height: 33px;
      padding-left: 25px;
      cursor: pointer;
      color: #fff;
      background: url('../assets/act2.png');
      background-size: 100% 100%;
    }

    .yuanqiu {
      width: 250px;
      cursor: pointer;
      width: 11px;
      height: 11px;
      background-color: #a0fdda;
      border-radius: 50%;
      margin-left: 17px;
    }
  }

  .zongshu {
    margin-top: 12px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #abe3f4;

    .dangq {
      color: #98f1d2 !important;
    }

    .dangqs {
      color: #fff !important;
    }
  }
}


.right {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
}

.right-panel {
  overflow: hidden;
  position: fixed;
  z-index: 1;
  right: 10px;
  width: 350px;
  top: 70px;
  height: 980px;
  // background: url("../assets/image/right.png");
  background-size: 100% 100%;
}

/deep/ .el-input__wrapper {
  // background: url("../assets/image/inputbeij.png") !important;
}

/deep/ .el-input__inner {
  // font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  // color: #b5eff7 !important;
}
</style>
