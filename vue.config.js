const path = require("path");

// 获取API基础地址（可根据环境配置）
const getIpLocation = (port) => {
  // 这里保持你的原有逻辑，虽然port参数未使用
  return "https://ai-api-test.3dzhanting.cn";
};
module.exports = {
  outputDir: "dist_new", // 修改输出目录为 dist_new
  productionSourceMap: false,
  publicPath: process.env.NODE_ENV === "production" ? "././" : "",

  // devServer: {
  //   proxy: {
  //     // 语音识别API代理
  //     // "/rgApi": {
  //     //   target: "https://ai-api-test.3dzhanting.cn", // 使用统一的基础地址
  //     //   changeOrigin: true,
  //     //   logLevel: "debug", // 可选：查看代理日志
  //     // },
  //     // 语音合成API代理
  //     // "/ttsApi": {
  //     //   target: "https://ai-api-test.3dzhanting.cn", // 使用统一的基础地址
  //     //   changeOrigin: true,
  //     // },
  //     // 通用API代理
  //     // "/v1": {
  //     //   target: "https://test.ai.3dzhanting.cn", // 单独的目标地址
  //     //   changeOrigin: true,
  //     //   // 保留v1前缀，不需要pathRewrite
  //     // },
  //   },
  // },

  // 如果需要可以取消注释
  /*
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [path.resolve(__dirname, './src/assets/styles/base.less')],
    },
  },
  */
};
