<template>
  <div>
    <div class="nengyuan" v-if="show">
      <div class="left-panel">
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimSecuritySummaryGroupList`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimWarningListSmall`" frameborder="0"></iframe>

      </div>
      <div class="right-panel">
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/BimDeviceSubentryAvailability`" frameborder="0"></iframe>

      </div>
    </div>
    <iframe v-if='componentTag' :key="componentTag" class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
    {{ iframeSrc }}
  </div>
</template>

<script>
export default {
  data() {
    return {
      iframeUrl,
      componentTag: '',
      show: true,
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl+`/#/${this.componentTag}`;
    }
  },
  methods: {
    anquan(index) {
      console.log(index, '安全');
      this.show = false
      if (index == 0) {
        this.componentTag = 'card/DeviceParking'
      } else if (index == 1) {
        this.componentTag = 'card/securityMgt/WXDJ'
      } else if (index == 2) {
        this.componentTag = 'card/securityMgt/DZXG'
      } else if (index == 3) {
        this.componentTag = 'card/securityMgt/RQBJ'
      } else if (index == 4) {
        this.componentTag = 'card/securityMgt/MJGL'
      } else if (index == 5) {
        this.componentTag = 'card/securityMgt/SPJK'
      } else {
        this.componentTag = ''
        this.show = true
      }
      console.log(this.iframeSrc);
    },
  }
};
</script>

<style scoped lang="less">
.componentTag {
  position: fixed;
  z-index: 1;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
}

.nengyuan {
  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    // background: url("../assets/image/left.png");
    // background-size: 100% 100%;
  }

  .lefti1 {
    width: 336px;
    height: 300px;
  }

  .lefti2 {
    width: 336px;
    height: 400px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
  }
}
</style>