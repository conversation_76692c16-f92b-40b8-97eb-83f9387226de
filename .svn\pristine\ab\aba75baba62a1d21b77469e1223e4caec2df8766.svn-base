<template>
  <div>
    <component :is="componentTag" @fatherMethoddd="fatherMethoddd"></component>
    <div class="container">
      <div class="left-panel">
        <!-- <iframe class="lefti1" src="https://3d.dddtask.cn/enginner-xufeng/hailuo/img/%E8%B5%84%E6%BA%90%2020%402x.png"
          frameborder="0"></iframe>
        <iframe class="lefti2" src="https://3d.dddtask.cn/enginner-xufeng/hailuo/img/%E8%B5%84%E6%BA%90%2021%402x.png"
          frameborder="0"></iframe>
        <iframe class="lefti3" src="https://3d.dddtask.cn/enginner-xufeng/hailuo/img/%E8%B5%84%E6%BA%90%2022%402x.png"
          frameborder="0"></iframe> -->
      
          <iframe class="lefti1" src="http://***************:8060/#/card/BimBuildingSummary" frameborder="0"></iframe>
          <iframe class="lefti1" src="http://***************:8060/#/card/BimDeviceAvailability" frameborder="0"></iframe>
          <iframe class="lefti1" src="http://***************:8060/#/card/BimElectricalClassify" frameborder="0"></iframe>
      </div>
      <!-- 右侧内容 -->
      <div class="right-panel">
        <iframe class="lefti1" src="http://***************:8060/#/card/BimWorkOrderStatistics" frameborder="0"></iframe>
        <iframe class="lefti1" src="http://***************:8060/#/card/BimAlarmStatistics" frameborder="0"></iframe>
        <iframe class="lefti1" src="http://***************:8060/#/card/BimEventDetails" frameborder="0"></iframe>
      </div>
    </div>
  </div>
</template>

<script>
// 这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
// 例如：import 《组件名称》 from '《组件路径》';
import huanxing from "@/components/echarts/huanxing.vue";
import zhexian from "@/components/echarts/zhexian.vue";
import SystemDete from "@/components/echarts/SystemDete.vue";
export default {
  // import引入的组件需要注入到对象中才能使用
  components: {
    huanxing,
    zhexian,
    SystemDete,
  },
  props: {},
  data() {
    // 这里存放数据
    return {
      optionData: [
        {
          name: "一级告警",
          value: 16,
          itemStyle: { color: "#EB6877", opacity: 0.9 },
        },
        {
          name: "二级告警",
          value: 27,
          itemStyle: { color: "#F8B551", opacity: 0.9 },
        },
        {
          name: "三级告警",
          value: 17,
          itemStyle: { color: "#B954E8", opacity: 0.9 },
        },
        {
          name: "四级告警",
          value: 40,
          itemStyle: { color: "#0284F0", opacity: 0.9 },
        },
      ],
      echartData1: {
        legend: ["今日", "昨日"],
        xAxis: [5.11, 5.12, 5.13, 5.14, 5.15, 5.16, 5.17],
        series1: [500, 600, 450, 560, 700, 580, 590], //今日
      },
      yqlist: [
        {
          title: "总面积",
          status: "40万",
          unit: "㎡",
        },
        {
          title: "厂房面积",
          status: "20.9万",
          unit: "㎡",
        },
        {
          title: "系统总数",
          status: "12",
          unit: "个",
        },
        {
          title: "设备总数",
          status: "108",
          unit: "个",
        },
        {
          title: "光伏发电量",
          status: "1256 ",
          unit: "kwh",
        },
        {
          title: "用电能量",
          status: "108",
          unit: "kwh",
        },
      ],
      nhlist: [
        {
          title: "总能耗(kgce)",
          status: "2574",
          unit: "㎡",
        },
        {
          title: "碳排放值(kg)",
          status: "16070.28",
          unit: "㎡",
        },
        {
          title: "节能量(KWh)",
          status: "12454",
          unit: "个",
        },
        {
          title: "节能率(%)",
          status: "6881",
          unit: "个",
        },
        {
          title: "厂区单台(KWh)",
          status: "18232",
          unit: "个",
        },
        {
          title: "人均用电(KWh)",
          status: "81",
          unit: "个",
        },
        // {
        //   title: "耗热量(KW/h)",
        //   status: "35",
        //   unit: "个",
        // },
        // {
        //   title: "耗油量(KW/h)",
        //   status: "6",
        //   unit: "个",
        // },
      ],
      warnlist: [
        {
          type: "1",
          time: "2023-09.02 10:00:00",
          typeName: "已处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:02:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "未授权",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "3",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:00:00",
          typeName: "处理中",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
        {
          type: "2",
          time: "2023-09.02 10:34:00",
          typeName: "待处理",
          status: "01号设备1",
          statuslocal: "2F",
          content: "故障",
        },
      ],
      isButton2Active: false,
      status: "巡检中",
      status1: "已完成",
      status2: "待巡检",
      selectedIndex: 0,
      componentTag: "component0",
    };
  },
  // 计算属性类似于data概念
  computed: {},
  // 监控data中的数据变化
  watch: {},
  // 方法集合
  methods: {
    getClassForStatus(status) {
      if (status === "巡检中") {
        return "completed";
      } else if (status === "待巡检") {
        return "incomplete";
      } else if (status === "已完成") {
        return "warning";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
    getClassForStatuss(status) {
      if (status === "巡检中") {
        return "completeds";
      } else if (status === "待巡检") {
        return "incompletes";
      } else if (status === "已完成") {
        return "warnings";
      } else {
        return "default"; // 没有匹配的状态时可以添加一个默认类
      }
    },
  },
  // 生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  // 生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { console.log(1222); },
  beforeCreate() { }, // 生命周期 - 创建之前
  beforeMount() { }, // 生命周期 - 挂载之前
  beforeUpdate() { }, // 生命周期 - 更新之前
  updated() { }, // 生命周期 - 更新之后
  beforeUnmount() {
    // 在组件销毁之前清除定时器
    console.log(1111);
  },

  unmounted() {
    console.log(2222);
  },// 生命周期 - 销毁之前
  destroyed() { console.log(1221); }, // 生命周期 - 销毁完成
  activated() { }, // 如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="less" scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  height: 1080px;
  text-align: center;

  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    background: url("../assets/image/left.png");
    background-size: 100% 100%;
  }

  .lefti1 {
    // margin-top: 12px;
    width:100%;
    height: 320px;
    overflow: hidden;
  }

  .lefti2 {
    width: 98%;
    height: 600px;
  }

  .lefti3 {
    width: 98%;
    height: 200px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
    background: url("../assets/image/right.png");
    background-size: 100% 100%;

    .rtitle {
      margin-left: -17px;
      margin-top: 25.2px;
    }

    .echart2 {
      margin-left: 20px;
    }

    .shinei {
      width: 100%;

      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-evenly;
    }

    .item {
      width: 156px;
      margin-top: 20px;
      margin-bottom: -8.68px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .simg {
        width: 60px;
        height: 60px;
      }

      .wenz {
        text-align: left;
        padding-left: 3px;
        width: 105px;
        height: 21px;
        background: linear-gradient(90deg,
            #2676b0 0%,
            rgba(38, 118, 176, 0) 100%);
        font-size: 14.28px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
      }

      .wendu {
        display: flex;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 24px;
        color: #59ffc4;
      }
    }

    .allwarn {
      height: 228px;
      overflow-y: auto;
      margin-top: 18px;
      margin-left: 48px;
      margin-right: 1.6px;
    }

    /* 设置滚动条的样式 */
    .allwarn::-webkit-scrollbar {
      width: 5px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条轨道的样式 */
    .allwarn::-webkit-scrollbar-track {
      background-color: #013f70;
      /* 设置滚动条轨道的背景色 */
    }

    /* 设置滚动条滑块的样式 */
    .allwarn::-webkit-scrollbar-thumb {
      background-color: #ffffff;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */

    .warn {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 7px;

      .red {
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ff0000;
        // line-height: .1625rem;
      }

      .yellow {
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #f7931e;
        // line-height: .1625rem;
      }

      .green {
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #00ffcc;
        // line-height: .1625rem;
      }

      img {
        width: 21px;
        height: 21px;
        margin-left: -8px;
        margin-right: 6.4px;
      }

      .warn1 {
        flex: 1.15;
      }

      .warn2 {
        flex: 5;
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #ffffff;
        line-height: 13px;

        .tit {
          margin-top: 1.04px;
          text-align: left;
          margin-left: 10.4px;
          font-size: 16px;
        }

        .warnred {
          color: #ff6142;
        }

        .warnyellow {
          color: #7ad0ff;
        }

        .warngreen {
          color: #00ffc6;
        }

        .line {
          width: 338px;
          height: 1px;
          background: #fbfbfb;
          border: 1px solid #ffffff;
          opacity: 0.2;
          margin-top: 10.4px;
        }

        .warn21 {
          margin-left: 10.4px;

          span {
            margin-left: 6.6px;
          }

          display: flex;
          background-size: 100% 100%;
          height: 23px;
          line-height: 22px;

          .warnt1 {
            flex: 1;
          }

          .warnt2 {
            flex: 2.2;
          }

          .warnt3 {
            margin-top: 1.2px;
            flex: 0.8;
            margin-left: 42px;
            color: #3afdff;
          }
        }
      }
    }

    .xtwarn {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 18px;
      width: 350px;
      margin-left: 48px;

      .warnbox {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex: 1;
        margin-left: 11.496px;
        margin-right: 11.496px;
        background: url("../assets/image/warnbox.png");
        background-size: 100% 100%;
        height: 36px;

        .warntitle {
          margin-left: 9px;
          font-family: Alibaba PuHuiTi;
          font-weight: 400;
          font-size: 14px;

          opacity: 0.8;
        }

        .t1 {
          color: #e9593f;
        }

        .t2 {
          color: #00e9b6;
        }

        .warnstatus {
          margin-left: 10px;
          font-family: Alibaba PuHuiTi;
          font-weight: bold;
          font-size: 21px;
          color: #ffffff;
        }
      }
    }

    .completed {
      background: #7ad0ff;
    }

    .incomplete {
      background: #ff6041;
    }

    .warning {
      background: #00ffc0;
    }

    .completeds {
      color: #7ad0ff;
    }

    .incompletes {
      color: #ff6041;
    }

    .warnings {
      color: #00ffc0;
    }
  }
}
</style>