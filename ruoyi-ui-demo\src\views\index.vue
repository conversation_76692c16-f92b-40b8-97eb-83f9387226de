<template>
  <div style="background: #000205; height: 100%">
    <!-- <DeviceEnergyUseBase title="电器能耗" deviceType="light" deviceTypes="FS,WT" /> -->

    <!-- <Dashboard3dh v-if="!display" /> -->
    <div v-show="!loading" style="height: 100%">
      <FullComponents v-if="display == 'fc' " />
      <FullComponents2 v-else-if="display == 'fc2' " />
      <JsMuseum v-else-if="display == 'jsMuseum' " />
      <HangzhouDianli v-else-if="display == 'hzdl' " />
      <HangzhouDianliVertical v-else-if="display == 'hzdlv' " />
      <Jiaxing v-else-if="display == 'jx' " />
      <JinhuaHengdian v-else-if="display == 'jhhd' " />
      <HangzhouJianghui v-else-if="display == 'hzjh' " />
      <ShanghaiDianqi v-else-if="display == 'shdq' " />
      <ShanghaiNenglv v-else-if="display == 'shnl' " />
      <HuzhouTailun v-else-if="display == 'hztl' " />
      <ShanghaiGuohui v-else-if="display == 'shgh' " />
      <NingboNengyan v-else-if="display == 'nbny' " :menu="screenMenu" />
      <YuyaoShunneng v-else-if="display == 'yysn' " :menu="screenMenu" />
      <NingboZongneng v-else-if="display == 'nbzn' " :menu="screenMenu" />
      <YuyaoDianli v-else-if="display == 'yydl' " :menu="screenMenu" />
      <JiaxingXincheng v-else-if="display == 'jxxc' " :menu="screenMenu" />
      <QingdaoDitie v-else-if="display == 'qddt' " :menu="screenMenu" />
      <JinghuaXiaozhen v-else-if="display == 'jhxz' " />
      <NingboAkd v-else-if="display == 'nbakd' " />
      <HangzhouQilun v-else-if="display == 'hzql' " />
      <CommBase v-else-if="display == 'base' " />
      <LongjiEnergy v-else-if="display == 'ljne' " />
      <DemoPage v-else-if="display == 'demo' " />
      <HomePage v-else-if="display == 'home' " />
      <JaxingRongtong v-else-if="display == 'rongtong' " />
      <HuzhouShuanglin v-else-if="display == 'hzsl' " />
      <ShangHaiJianKeYuan v-else-if="display == 'shjky' " />
      <ScreenShxd v-else-if="display == 'shxd' " />
      <DemoPage2 v-else-if="display == 'demo2' " />
      <Dashboard v-else />
      <div class="clearfix" />
    </div>
  </div>
</template>

<script>

import DeviceEnergyUseBase from "@/views/components/cO2View/components/common/DeviceSummaryBase";

import Dashboard from "@/views/dashboard/index";
import Dashboard3dh from "@/views/components/bimView/index-debug";

import FullComponents from "@/views/ScreenFullComponents/index.vue";
import FullComponents2 from "@/views/ScreenFullComponents/index-full.vue";

// 嘉善博物馆定制化首页
import JsMuseum from "@/views/ScreenJsMuseum";
import HangzhouDianli from "@/views/ScreenHangzhoudianli";
import HangzhouDianliVertical from "@/views/ScreenHangzhoudianli/index-v";
import Jiaxing from "@/views/ScreenJiaxing";
import JinhuaHengdian from "@/views/ScreenJinhuaHengdian";

import ShanghaiNenglv from "@/views/ScreenShnl";
import HangzhouJianghui from "@/views/ScreenHzjh";
import ShanghaiDianqi from "@/views/ScreenShdq";
import HuzhouTailun from "@/views/ScreenHztl";
import NingboNengyan from "@/views/ScreenNbny";
import ShanghaiGuohui from "@/views/ScreenShgh";
import YuyaoShunneng from "@/views/ScreenYysn";
import YuyaoDianli from "@/views/ScreenYydl";
import JiaxingXincheng from "@/views/ScreenJxxc";
import QingdaoDitie from "@/views/ScreenQddt";
import CommBase from "@/views/ScreenCommBase";
import NingboZongneng from "@/views/ScreenNbzn";  //
import JinghuaXiaozhen from "@/views/ScreenJhxz";
import NingboAkd from "@/views/ScreenNbakd";
import HangzhouQilun from "@/views/ScreenHzql";
import LongjiEnergy from "@/views/ScreenLjEnergy";
import DemoPage from "@/views/ScreenDemo";
import DemoPage2 from "@/views/ScreenDemo2";
import HomePage from "@/views/homePage/homePage";
import JaxingRongtong from "@/views/ScreenJxrt";
import HuzhouShuanglin from "@/views/ScreenHzsl";
import ShangHaiJianKeYuan from "@/views/ScreenShjky";
import ScreenShxd from "@/views/ScreenShxd";

export default {
  components: {
    HomePage,
    DeviceEnergyUseBase,
    DemoPage2,
    Dashboard,
    Dashboard3dh,

    FullComponents,     // 所有组件
    FullComponents2,     // 所有组件

    CommBase,         // 通用大屏(由泰伦改编)

    // 定制化首页
    JsMuseum,  // 嘉兴博物馆
    HangzhouDianli,   // 杭州电力调度大楼
    HangzhouDianliVertical,  // 杭州电力调度大楼 竖屏版本
    Jiaxing,          // 嘉兴电力大屏
    ShanghaiNenglv,   // 上海能率大屏
    JinhuaHengdian,   // 金华横店影视城大屏
    HangzhouJianghui, // 杭州江晖大屏
    ShanghaiDianqi,   // 上海电气--高领
    HuzhouTailun,     // 湖州泰伦
    NingboNengyan,    // 宁波能源
    ShanghaiGuohui,   // 上海国际会议中心
    JiaxingXincheng,  // 嘉兴新塍
    QingdaoDitie,     // 青岛地铁
    YuyaoShunneng,    // 余姚舜能
    YuyaoDianli,      // 余姚电力
    JinghuaXiaozhen,  // 金华汽车小镇
    NingboZongneng,   // 宁波综合能源 半成品 作废
    NingboAkd,        // 宁波综合能源(余姚甬电云) 爱科迪
    HangzhouQilun,    // 杭汽轮

    LongjiEnergy,     // 隆基光电能源管理平台新能耗
    DemoPage,         // 标准演示大屏
    JaxingRongtong,   //嘉兴融通
    HuzhouShuanglin,  //中国银行湖州双林支行
    ShangHaiJianKeYuan,//上海建科院
    ScreenShxd,         //上海现代院
  },
  data() {
    return {
      loading: true,
      // 区分 bim debug
      isDebug: this.$route.query.isdebug,

      // 区分定制化首页
      display: "",

      // redirect 默认首页地址
      redirect: "",

      // 是否假数据
      mockeData: "",

      // 大屏顶部菜单
      screenMenu: ['EnergyView', 'DeviceView', 'SecurityView','DevopsView'],

      // 载入项目配置
      projectSettings: this.gf.projectSettings(),
    };
  },
  created () {
    console.log("index getDicts system_configs", this.projectSettings);
    // 覆盖原基本配置
    this.display = this.projectSettings.display ? this.projectSettings.display : this.display;
    console.log(this.display,'this.display');
    
    if(this.projectSettings.hasOwnProperty("mocke_data")) {
      this.mockeData = this.projectSettings.mocke_data;
    }
    this.loading = false;
  },
  methods: {
  }
}
</script>
