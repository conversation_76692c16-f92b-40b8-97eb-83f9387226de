<template>
  <div>
    <div class="nengyuan" v-if="show">
      <div class="left-panel">
        <iframe class="lefti1" :src="`${iframeUrl}/#/card/left/EnergyElectricityUse`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/left/BimEnergyUsageTrends`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/left/BimWaterUsageTrends`" frameborder="0"></iframe>
      </div>
      <div class="right-panel">
        <iframe class="lefti1" :src="`${iframeUrl}/#/card/right/EnergyCarbonEmission`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/right/EnergyCarbonPrediction`" frameborder="0"></iframe>
        <iframe class="lefti2" :src="`${iframeUrl}/#/card/right/BimSummaryElectricity`" frameborder="0"></iframe>
      </div>
    </div>

    <iframe v-if='componentTag' class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
    <div class="close" @click="close" v-if="isclose">×</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      iframeUrl,
      componentTag: '',
      show: true,
      isclose: false,
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl + `/#/card/energyMgt/${this.componentTag}`;
    }
  },
  methods: {
    close() {

      this.componentTag = ''
      this.isclose = false
    },
    nengyuan(index) {
      console.log(index, 1212);
      this.show = false
      if (index == 0) {
        this.componentTag = 'electricity/summary'
        this.isclose = true
      } else if (index == 1) {
        this.componentTag = 'electricity/subOption'
        this.isclose = true
      } else if (index == 2) {
        this.componentTag = 'electricity/partition'
        this.isclose = true
      } else if (index == 3) {
        this.componentTag = 'water/summary'
        this.isclose = true
      } else if (index == 4) {
        this.componentTag = 'water/subOption'
        this.isclose = true
      } else if (index == 5) {
        this.componentTag = 'water/partition'
        this.isclose = true
      } else if (index == 6) {
        this.componentTag = 'summary/energy'
        this.isclose = true
      } else {
        this.componentTag = ''
        this.show = true
        this.isclose = false
      }
    },
  }
};
</script>

<style scoped lang="less">
.componentTag {
  position: fixed;
  z-index: 99;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
}

.close {
  position: fixed;
  z-index: 100;
  top: 75px;
  right: 25px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
}

.nengyuan {
  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    // background: url("../assets/image/left.png");
    // background-size: 100% 100%;
  }

  .lefti1 {
    width: 336px;
    height: 320px;
  }

  .lefti2 {
    width: 336px;
    height: 350px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
  }
}
</style>