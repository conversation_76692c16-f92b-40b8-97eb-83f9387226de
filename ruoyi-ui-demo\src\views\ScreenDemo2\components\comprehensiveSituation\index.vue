<template>
  <div>
    <div class="co2_body">
      <div class="fl">
        <div class="co2_main_left">
          <BimBuildingSummary autoHeight></BimBuildingSummary>
          <BimDeviceAvailability autoHeight></BimDeviceAvailability>
          <BimElectricalClassify autoHeight></BimElectricalClassify>
        </div>
      </div>

      <div class="co2_main_center co2_energy_center">
        <div class="ft">
        </div>
        <div class="fb">
          <div class="flex">
          </div>
        </div>
      </div>

      <div class="fr">
        <div class="co2_main_right">
          <BimWorkOrderStatistics autoHeight></BimWorkOrderStatistics>
          <BimAlarmStatistics autoHeight></BimAlarmStatistics>
          <BimEventDetails class="ml10" autoHeight></BimEventDetails>
        </div>
      </div>

    </div>
  </div>
</template>
<script>
import BimBuildingSummary from '@/views/cards/BIM/home/<USER>'
import BimDeviceAvailability from '@/views/cards/BIM/home/<USER>'
import BimElectricalClassify from '@/views/cards/BIM/home/<USER>'
import BimWorkOrderStatistics from '@/views/cards/BIM/home/<USER>'
import BimAlarmStatistics from '@/views/cards/BIM/home/<USER>'
import BimEventDetails from '@/views/cards/BIM/home/<USER>'

export default {
  components: {
    BimBuildingSummary,
    BimDeviceAvailability,
    BimElectricalClassify,
    BimWorkOrderStatistics,
    BimAlarmStatistics,
    BimEventDetails
  },
  data() {
    return {};
  },
  computed: {

  },
  mounted() {
 
  },
  methods: {
  },
};
</script>

<style scoped lang="scss">
.co2_body {
  .fb {
    bottom: 60px;
    height: fit-content;
    .flex {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

//bim大屏组件样式覆盖
.left-card-box,.right-card-box {
  width: auto;
  flex: 1;
  height: 33.33%;
  background: url('/image/screen/card_content_bg.png') no-repeat 0;
  background-size: 100% 100%;
}
.fb {
  .left-card-box,.right-card-box {
    height: auto;
  }
}
::v-deep {
  .left-card-box,.right-card-box {
    border-radius: 0 !important;
    padding: 0 !important;
    .left-card-title,.right-card-title {
      background: url('/image/screen/co2_nav_bg.png') no-repeat 0;
      background-size: auto 100%;
      font-family: pangmenzhendao;
      padding-left: 6%;
      height: 36px;
      text-align: left;
      line-height: 36px;
    }
  }
  .co2_radiation_curve_chart {
    padding: 10px;
  }
}

</style>