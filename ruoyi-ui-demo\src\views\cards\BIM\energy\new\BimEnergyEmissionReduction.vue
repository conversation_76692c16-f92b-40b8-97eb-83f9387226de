<template>
  <div class="right-card-box">
    <div class="right-card-title" v-text="title"></div>

    <div class="sub_title">
      <div class="title_left">
        <img src="/image/co2_energy_carbon.png" alt="">
        <span class="title_left_span">能源年度碳排放</span>
      </div>
      <div class="title_right">
        <span class="title_right_span">{{ carbonTotal }}</span><i> tCO2</i>
      </div>
    </div>
    <div :style="{ height: `${height}` }">
      <BaseChart :optionData="energyCarbonPieData" height="100%" />
    </div>
  </div>

</template>

<script type="text/javascript">
import {
  buildingEnergyDataList, buildingEnergyTypeSummary,
} from "@/api/energy/apis";

import BaseChart from "@/views/components/cO2View/components/common/BaseChart";

export default {
  props: {
    "title": {
      type: String,
      default: "用能统计",
    },
    "height": {
      type: String,
      default: "205px",
    },
    "autoHeight": {
      type: Boolean,
      default: false
    }
  },
  components: {
    BaseChart,
  },
  data() {
    return {
      curBuilding: this.gf.getCurBuilding(),
      colors: this.gf.chartColors(),
      project: this.gf.projectInfo(),
      settings: this.gf.projectSettings(),
      energyCarbonPieData: {},
      electricityTotal: null,
      carbonTotal: 0,
      energyTypes: ['分项用电', '分项燃气'],
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.getData();
    // this.initChart();
  },

  methods: {

    getData() {
      let pieData = [
        { value: 0, name: "分项用电", selected: true },
        { value: 0, name: "分项用水" },
      ];
      buildingEnergyTypeSummary({
        buildingId: this.curBuilding.id,
        from: this.$moment().startOf('year').format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
        deviceTypes: this.energyTypes.join(','),
        displayType: 'total',
        calculation: 1, // 用总表计量
      }).then((res) => {
        if (res.code == 200 && res.data) {
          let data = res.data
          this.carbonTotal = 0;
          for (let o in data) {
            for (let p in data[o]) {
              if (o == '分项用电') {
                this.carbonTotal += data[o][p].total * (this.settings.co2ElectricityRate || 1)
                pieData[0].value += data[o][p].total * (this.settings.co2ElectricityRate || 1)
              } else if (o == '分项用水') {
                this.carbonTotal += data[o][p].total * (this.settings.co2GasRate || 1)
                pieData[1].value += data[o][p].total * (this.settings.co2GasRate || 1)
              }
            }
            this.carbonTotal = parseFloat(this.carbonTotal.toFixed(2));
            pieData[0].value = parseFloat(pieData[0].value.toFixed(2));
            pieData[1].value = parseFloat(pieData[1].value.toFixed(2));
            this.energyCarbonPieData = this.initChart(pieData);
          }
          console.log('this.energyInfo12...........', pieData)
        }
      })
    },
    initChart(data) {
      let total = 0;
      let legend = []
      data = data.map(d => {
        d.per = 0;
        d.unit = "tCO2";
        total += d.value;
        legend.push(d.name);
        return d;
      });
      if (total != 0) {
        data = data.map(d => {
          d.per = parseFloat(d.value * 100 / total).toFixed(2);
          return d;
        });
      }
      return {
        title: {
          text: '碳排放',
          top: '38%',
          left: '24.5%',
          textAlign: 'center',
          textStyle: {
            color: '#fff',
          }
        },
        legend: {
          orient: 'vertical',
          top: '22%',
          left: "47%",
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 15,
          itemGap: 15,
          position: 'center',
          data: legend,
          align: "left",
          formatter: function (name) {
            let target;
            let value;
            let per;
            let unit;
            for (let i = 0; i < data.length; i++) {
              if (data[i].name === name) {
                target = data[i].value;
                unit = data[i].unit;
                value = data[i].value;
                per = data[i].per;
              }
            };
            return '{a|' + (name == '分项用电' ? '用电  ' : '用水  ') + '}{b|' + value + ' }{c|' + unit + '}\n{d|占 }{e|' + per + '}{f|%}';
          },

          textStyle: {  // 添加
            padding: [0, 0, 0, 5],
            rich: {
              a: {
                lineHeight: 20,
                fontSize: 14,
                color: '#D0DEEE',
              },
              b: {
                fontSize: 16,
                color: '#228FFF',
              },
              c: {
                fontSize: 12,
                color: '#D0DEEE',
              },
              d: {
                fontSize: 14,
                color: '#228FFF',
                align: 'right'
              },
              e: {
                fontSize: 16,
                color: '#3CCCF9',
              },
              f: {
                fontSize: 14,
                color: '#D0DEEE',
              }
            }
          }
        },
        xAxis: {
          show: false
        },
        yAxis: {
          show: false
        },
        color: ["#2294FE", "#0DD3A3", "#3CCCF9"],
        series: [
          {
            name: '总量',
            type: 'pie',
            radius: ['48%', '70%'],
            center: ['25%', '48%'],
            itemStyle: {
              borderRadius: 2,
              borderColor: "rgba(0,0,0,0.5)",
              borderWidth: 2
            },
            label: {
              show: false
            },
            data: data
          }
        ]
      }
    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/bimCard.scss";


.sub_title {
  // margin-top: 10px;
  height: 36px;
  //height: 40px;
  //background: linear-gradient( 90deg, rgba(21,154,255,0.4) 0%, rgba(21,154,255,0) 100%);
  background: linear-gradient(90deg, rgba(1, 59, 152, 0.26) 0%, rgba(15, 172, 235, 0.26) 49%, rgba(12, 164, 224, 0) 100%);
  color: #fff;
  font-size: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  margin-bottom: 5px;
  margin-top: 10px;
  font-family: Source Han Sans CN;
  font-weight: 500;

  .title_left_span {
    font-size: 14px;
    color: #FFFFFF;

  }

  .title_left {
    font-weight: 600;

    img {
      width: 27px;
      height: 24px;
      margin-right: 8px;
    }
  }

  .title_right {
    .title_right_span {
      font-size: 18px;
      color: #228FFF;

    }

    i {
      font-size: 14px;
      color: #FFFFFF;
    }
  }
}
</style>
