<template>
  <div>


    <SlidingPanel v-if="show" :showLeftPanel="true" :showRightPanel="true">

      <!-- 左侧内容 -->
      <template #left>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimKeyEquipment?deviceTypes=DT10,camera,MJ00,TFJ11,KTMD60,DTZT,RQBJ,JPSGL`"
          frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimAccessControlRecord`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimVehiclePassage`" frameborder="0"></iframe>
      </template>

      <!-- 右侧内容 -->
      <template #right>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimAreaMonitoring?count=8`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/new/BimEventStatistics?type=1`" frameborder="0"></iframe>
      </template>
    </SlidingPanel>

    <div class="show" v-if="!show && isshow">
      <SlidingPanel v-if="!show && isshow" :showLeftPanel="true" :showRightPanel="true" :showLeftbg="true">
        <!-- 左侧内容 -->
        <template #left>
          <div class="item" style="height: 920px;">
            <!-- {{ sblist }} -->
            <!-- <p class="more" @click="more()">更多</p> -->
            <img src="../assets/more.png" class="more" @click="more()" alt="">
            <Title :tit="sbtitle">
              <el-input placeholder="请输入内容" v-model="input" class="input-with-select">

              </el-input>
              <!-- {{ iframeSrc }} -->
              <ul class="listul" v-if="filteredList.length && !show">
                <li v-for="(item, index) in filteredList" :key="index" class="listli">
                  <!-- 绑定公共的 selectedItem，确保每次只能选中一个 -->
                  <!-- <input class="listliinput1" type="radio" :id="'radio' + index"
                    @click="changedevice(item.deviceId, index)" v-model="selectedItem" :value="item.id" /> -->
                  <div :class="listactive == index ? 'listliinput2' : 'listliinput1'" :for="'radio' + index"
                    @click="changedevice(item.deviceId, index, isjj)">
                    {{ item.title }}
                  </div>
                </li>
              </ul>
              <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
            </Title>
          </div>
        </template>

        <!-- 右侧内容 -->
        <template #right>
          <iframe v-if='!deviceId && sbTag' class="item" :src="`${iframeUrl}/#/${sbTag}`" :key="sbTag"
            frameborder="0"></iframe>
          <transition name="expand" mode="out-in">
            <iframe :width="iframeWidth" v-if='deviceId'
              :class="sbtitle == '视频监控设备列表' ? 'right2 item1' : 'right2 item1'" :src="iframeSrc" :key="iframeSrc"
              frameborder="0"></iframe>

          </transition>
          <img class="closedet" @click="closedet" v-if="deviceId" src="../assets/close.png" alt="">
        </template>
      </SlidingPanel>
      <!-- <div class="right-panel"></div> -->

    </div>

    <transition name="expand" mode="out-in">
      <iframe v-if='componentTag' :key="componentTag" class="componentTag" :src="`${iframeUrl}/#/${componentTag}`"
        frameborder="0"></iframe>
    </transition>
    <transition name="expand" mode="out-in">
      <div :key="resourceId">
        <iframe ref="iframe1" v-show='componentTag1' class="componentTag"
          :src="`${iframeUrl}/#/card/ParkingSystemTabs?resourceId=${resourceId}&type=CWTCQ00`" frameborder="0"></iframe>
      </div>
    </transition>
    <img class="close" @click="close" v-if="isclose" src="../assets/close.png" alt="">
    <!-- <div class="close" @click="close" v-if="isclose">×</div> -->
    <!-- {{ iframeSrc }} -->
  </div>
</template>

<script>
import SlidingPanel from "@/components/common/SlidingPanel.vue";
import { log } from "../../../ruoyi-ui-demo/src/components/byEditor/js/utils/mqtt";
export default {
  components: {
    SlidingPanel,

  },
  props: ["sblist", "sbtitle"], // 声明接收来自父组件的 sblist 数据
  data() {
    return {
      resourceId: 182,
      isjj: false,
      listactive: null,
      isclose: false,
      componentTag1: false,
      iframeUrl,
      componentTag: '', //点击更多弹出的iframe
      sbTag: '',//右侧的iframe
      show: true,
      isshow: false,
      iframeWidth: '372px',
      // sblist: [
      //   // { id: 1, name: "Device 1" },
      //   // { id: 2, name: "Device 2" },
      //   // { id: 3, name: "Device 3" },
      // ],
      selectedItem: 0,
      deviceId: '',
      input: '',
      filteredList: [],
      carstatuslsit: [],
      iframeKey: 0  // 初始 key，用于强制重新渲染 iframe
    }
  },
  computed: {
    filteredList() {
      return this.sblist.filter(item =>
        item.name.includes(this.input) || item.roomId.includes(this.input)
      )
    },
    iframeSrc() {
      if (this.sbtitle == '视频监控设备列表') {
        return this.iframeUrl + `/#/card/caremaDetailCard?deviceId=${this.deviceId}`;
      }
      else {
        return this.iframeUrl + `/#/card/deviceDetailCardBig?deviceId=${this.deviceId}`;
      }

      //return this.iframeUrl + `/#/card/videoDetail?deviceId=${this.deviceId}`;
    },
    // iframeSrc1() {
    //   return this.iframeUrl + `/#/card/deviceDetails?deviceId=${this.deviceId}`;
    // }
  },
  watch: {
    // 监听 sblist 变化
    sblist: {
      handler(newVal, oldVal) {
        // 当 sblist 发生变化时，将 deviceId 设置为空
        this.deviceId = '';
        console.log("sblist changed, deviceId reset to null");
      },
      deep: true, // 深度监听 sblist，如果 sblist 是对象或数组
      immediate: true, // 立即触发 handler，页面加载时
    },
  },

  mounted() {
    // this.fetchDevices(602, 'DT');
    var that = this;
    window.addEventListener("message", function (event) {
      let data = event && event.data && event.data.message ? event.data.message.name : ''
      let type = event && event.data && event.data.type ? event.data.type : ''
      console.log(event, type, '收到的数据aq');
      if (data == "open") {
        that.iframeWidth = "1890px"
      } else if (data == "close") {
        that.iframeWidth = "372px"
      }
      // if (type == "freshBimDeviceData") {
      //   let carlist = event.data.param
      //   // console.log(carlist, '车位状态');
      //   that.carstatuslsit = carlist.map(item => {
      //     // 提取每个数据对象中的 id 和 dVal 和 dmName
      //     return item.deviceDataBase.map(subItem => ({
      //       id: item.id,
      //       dVal: subItem.dVal,
      //       name: item.name
      //     }));
      //   }).flat();  // 使用 flat() 展开嵌套数组
      //   console.log(that.carstatuslsit, '车位状态');

      //   that.$emit('update-data', that.carstatuslsit);

      // }
    })

    ue.interface.setSliderValue = (value) => {
      console.log(value, 'ue点击拿到的值');
      if (!isNaN(Number(value.data))) {
        let did = value.data; // 如果是数字，则赋值
        const result = this.sblist.map((item, index) => ({ item, index })) // 将元素和其下标一起打包
          .filter(obj => obj.item.id == did);      // 过滤匹配的元素
        console.log(result);
        this.deviceId = result[0].item.deviceId
        this.changedevice(result[0].item.deviceId, result[0].index, false)
        console.log(result[0].item.deviceId, 'ue点击拿到的id');
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
  },
  methods: {
    updateChildValue(newValue) {
      this.isjj = newValue;
    },
    changeresourceId(id) {
      console.log(id, 'changeresourceId');
      this.resourceId = id

      // 手动更新 iframe 的 src
      // 通过 ref 获取 iframe 元素，并强制刷新
      // this.$nextTick(() => {
      //   const iframe = this.$refs.iframe1;
      //   if (iframe) {
      //     iframe.contentWindow.location.reload(); // 刷新 iframe
      //   }
      // });
    },
    more() {
      console.log(this.sbtitle);
      if (this.sbtitle == '无线对讲设备列表') {
        this.componentTag = 'card/pmtManagePage?type=WXDJ'
        this.componentTag1 = false
      } else if (this.sbtitle == '电子巡更设备列表') {
        this.componentTag = 'card/pmtManagePage?type=XG00'
        this.componentTag1 = false
      } else if (this.sbtitle == '入侵报警设备列表') {
        this.componentTag = 'card/pmtManagePage?type=RQFQ00'
        this.componentTag1 = false
      } else if (this.sbtitle == '门禁管理设备列表') {
        this.componentTag = 'card/pmtManagePage?type=MJ00'
        this.componentTag1 = false
      } else if (this.sbtitle == '视频监控设备列表') {
        this.componentTag = 'card/pmtManagePage?type=camera'
        this.componentTag1 = false
      } else if (this.sbtitle == '停车场车位列表') {
        this.componentTag = ''
        this.componentTag1 = true
        console.log(this.componentTag1);
      }
      else {
        this.componentTag = ''
      }

      this.isclose = true

    },
    close() {
      this.componentTag = ''
      this.componentTag1 = false
      this.isclose = false
    },
    closedet() {
      this.deviceId = ''
      // this.sbTag = ''
      this.iframeWidth = "372px"
    },
    changedevice(id, index, flag) {
      this.iframeWidth = "372px"
      console.log(this.sblist, id, 112);
      this.listactive = index
      const result = this.sblist.find(item => item.deviceId == id);
      // 打印选中的单选按钮的值
      if (flag) {
        this.seedue(result)
      }

      console.log("选中的值:", this.selectedItem);
      // 或者打印设备ID和索引
      console.log("设备ID:", id, "索引:", index);
      this.deviceId = id

    },
    seedue(item) {

      // this.$emit('seedsb', item.buildId + item.floorId);
      console.log("单个设备", item,);

      this.sendToUE41('jujiao', item)
    },
    sendToUE4(data) {
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UE收到的");
    },
    anquan(index) {
      console.log(index, '安全');
      if (this.sbtitle == '无线对讲设备列表') {
        this.sbTag = ''
      } else if (this.sbtitle == '电子巡更设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=XG&deviceTypes=XG00'
      } else if (this.sbtitle == '入侵报警设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=RQ&deviceTypes=RQFQ00'
      } else if (this.sbtitle == '门禁管理设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=MJ&deviceTypes=MJ00'
      } else if (this.sbtitle == '视频监控设备列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=JK&deviceTypes=camera'
      }
      else if (this.sbtitle == '停车场车位列表') {
        this.sbTag = 'card/bimImportantDevideInfoByType?type=TCC&deviceTypes=CWTCQ00'
      }
      if (index == 0) {
        // this.isclose = true
        this.componentTag = ''
        this.show = false
        this.isshow = true
        // } else if (index == 1) {
        //   this.componentTag = ''
        // } else if (index == 2) {
        //   this.componentTag = 'card/securityMgt/DZXG'
        // } else if (index == 3) {
        //   this.componentTag = 'card/securityMgt/RQBJ'
        // } else if (index == 4) {
        //   this.componentTag = 'card/securityMgt/MJGL'
        // } else if (index == 5) {
        //   this.componentTag = 'card/securityMgt/SPJK'
        // 
      } else if (index == 9) {
        this.componentTag = ''
        this.show = true
        this.isshow = false
      } else {
        this.componentTag = ''
        this.show = false
        this.isshow = true
        this.isclose = false
      }

      //获取数据

      console.log(this.iframeSrc);
    },
  }
};
</script>

<style scoped lang="less">
.item {
  width: 336px;
  height: 330px;
  overflow: hidden;
  margin-bottom: 5px;
}

.left-panel .item:nth-child(1) {
  height: 320px;
}

.left-panel .item:nth-child(2) {
  height: 327px;
}

.item1 {
  // width: 336px;
  height: 340px;
  overflow: hidden;
  margin-bottom: 20px;
}

.expand-leave-active {
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-name: expandIn;
}

.expand-leave-active {
  animation-name: shrinkAndFade;
}

@keyframes expandIn {
  0% {
    transform: scale(0.4);
    opacity: 0;
    transform-origin: center;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkAndFade {
  0% {
    transform: scale(1);
    opacity: 1;
    transform-origin: center;
  }

  100% {
    transform: scale(0.4);
    opacity: 0;
  }
}

.right1 {
  height: 626px !important;
  width: 714px !important;
  overflow: hidden;
}

.right2 {
  height: 884px !important;
  // width: 1356px !important;
  overflow: hidden;
}

.right-panel .item:nth-child(1) {
  height: 560px;
}

.right-panel .item:nth-child(2) {
  height: 420px;
}

.closedet {
  position: absolute;
  z-index: 100;
  top: 4px;
  right: 4px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}



.close {
  position: fixed;
  z-index: 100;
  top: 72px;
  right: 25px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.show {
  .left {}

  padding-left: 16px;
  padding-top: 14px;
  // background: url("../assets/image/beijingzhe.png")
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 336px;
  height: 980px;
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 12;
  text-align: center;
  position: relative;

  .more {
    position: absolute;
    right: 10px;
    top: 20px;
    font-size: 16px;
    width: 49px;
    height: 21px;
    color: #fff;
    // text-shadow: 2px 2px 3px #000000;
    cursor: pointer;
  }

  // .more:hover {
  //   color: #7dffd9;
  //   text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.1);
  // }

  .input-with-select {
    width: 305px;
    height: 32px;
    margin-bottom: 10px;
    margin-top: 5px;
  }

  .select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 305px;
    height: 42px;
    margin-left: 8px;
    margin-top: 8px;
    padding-left: 33px;
    // background: url("../assets/image/selectbgc.png") !important;

    .selectAll {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #7dffd9;
      padding-left: 13px;
    }
  }

  .listul {
    height: 820px;
    overflow-y: scroll;
    width: 100%;

    &::-webkit-scrollbar {
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background-color: rgba(151, 151, 151, 0.1);
    }

    &::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.4);
    }


  }

  .listli {

    display: flex;
    align-items: center;
    // margin-left: 8px;
    margin-top: 1px;
    width: 305px;
    height: 42px;
    // background: url("../assets/image/selectbgc.png") !important;
    font-family: Alibaba PuHuiTi;
    font-weight: 400;
    font-size: 14px;

    padding-left: 18px;
    cursor: pointer;
    text-align: left;

    .listliinput1 {
      width: 283px;
      height: 33px;
      padding-left: 25px;
      line-height: 33px;
      cursor: pointer;
      color: #D0DEEE;
      background: url('../assets/act1.png');
      background-size: 100% 100%;
    }

    .listliinput2 {
      width: 283px;
      line-height: 33px;
      height: 33px;
      padding-left: 25px;
      cursor: pointer;
      color: #fff;
      background: url('../assets/act2.png');
      background-size: 100% 100%;
    }

    .yuanqiu {
      width: 250px;
      cursor: pointer;
      width: 11px;
      height: 11px;
      background-color: #a0fdda;
      border-radius: 50%;
      margin-left: 17px;
    }
  }

  .zongshu {
    margin-top: 12px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #abe3f4;

    .dangq {
      color: #98f1d2 !important;
    }

    .dangqs {
      color: #fff !important;
    }
  }


}

.componentTag {
  position: fixed;
  z-index: 23;
  top: 70px;
  left: 1%;
  width: 98.2%;
  height: 895px;
}

// .nengyuan {
//   position: relative;
// }

.right {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
}

/deep/ .el-input__inner {
  // font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  // color: #b5eff7 !important;
}
</style>