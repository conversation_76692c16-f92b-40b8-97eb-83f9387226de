import axios from "axios";

// 创建axios实例
const request = axios.create({
  baseURL: iframeUrl+"/api", // 基础URL
  timeout: 10000, // 请求超时时间
});
// http://139.196.200.244:8060/api/
// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    // 例如添加token
    // const token = localStorage.getItem("token");
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data;
  },
  (error) => {
    // 对响应错误做点什么
    if (error.response && error.response.status === 401) {
      // 处理401错误，例如跳转到登录页面
      console.error("Authentication failed, redirecting...");
    }
    return Promise.reject(error);
  }
);
const http = {
  get(url, params) {
    return request({
      method: "get",
      url: url,
      params,
    });
  },
  post(url, payload = undefined, quest) {
    return request({
      method: "post",
      url: url,
      data: payload,
      params: quest,
      headers: {
        isToken: false,
      },
    });
  },
  delete(url, payload = undefined) {
    return request({
      method: "delete",
      url: url,
      data: payload,
    });
  },
};

export default http;
