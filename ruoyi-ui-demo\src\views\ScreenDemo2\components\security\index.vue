<template>
  <div>
    <div class="co2_body">
      <div class="fl">
        <div class="co2_main_left">
          <component
            v-if="cgBuildingFlag && leftTop && leftTop.component"
            :is="leftTop.component"
            :title="leftTop.title"
            :strategy="leftTop.strategy"
            :deviceTypes="leftTop.deviceTypes"
            :operationRate="leftTop.operationRate"
            />

          <component
            v-if="cgBuildingFlag && leftBtm && leftBtm.component"
            :is="leftBtm.component"
            :title="leftBtm.title"
            :strategy="leftBtm.strategy"
            :deviceTypes="leftBtm.deviceTypes"
            :operationRate="leftBtm.operationRate"
          />
<!--          <Lighting></Lighting>-->
        </div>
      </div>

      <div class="co2_main_center">
        <div class="ft" :style="{backgroundImage: `url(${middleTop.showBg ? middleTop.bgImg : ''})`}">
          <div class="page_fixed_nav_box" v-if="screenMenus && screenMenus.length <= 1">
            <el-dropdown class="avatar-container hover-effect" trigger="hover">
              <div>
                <img src="@/assets/image/co2_daily.png" class="mb10"/>
                <p>日常管理</p>
              </div>
              <el-dropdown-menu class="nav_wrap" slot="dropdown">
                <a class="router-link" v-for="link in subLinks"
                   @click="openWindow(link)">{{ link.name }}</a>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <div class="fb">
          <div class="co2_energy_carbon1">
            <div class="co2_energy_carbon pb20">
              <WarningList ></WarningList>
            </div>
          </div>
        </div>
      </div>

      <div class="fr">
        <div class="co2_main_right">
          <component
            v-if="cgBuildingFlag && rightTop && rightTop.component"
            :is="rightTop.component"
            :title="rightTop.title"
            :strategy="rightTop.strategy"
            :deviceTypes="rightTop.deviceTypes"
            :operationRate="rightTop.operationRate"
          />

          <component
            v-if="cgBuildingFlag && rightBtm && rightBtm.component"
            :is="rightBtm.component"
            :title="rightBtm.title"
            :strategy="rightBtm.strategy"
            :deviceTypes="rightBtm.deviceTypes"
            :operationRate="rightBtm.operationRate"
            :waterCoolingTemperature="rightBtm.waterCoolingTemperature"
            :elevatorMaintenanceRemainder="rightBtm.elevatorMaintenanceRemainder"
            :elevatorTableData="rightBtm.elevatorTableData"
          />
<!--          <SupplyExhaustFan></SupplyExhaustFan>-->
<!--           <VideoMonitoring></VideoMonitoring>-->
<!--          <WaterCooling></WaterCooling>-->
        </div>
      </div>
    </div>

  </div>
</template>
<script>

import BaseView from "@/views/components/cO2View/components/common/BaseView";
import AirConditioning from "@/views/components/cO2View/components/common/screen/deviceComp/airConditioning.vue";
import Lighting from "@/views/components/cO2View/components/common/screen/deviceComp/lighting.vue";
// import WarningList from "@/views/components/cO2View/components/common/screen/deviceComp/warningList.vue";
import SupplyExhaustFan from "@/views/components/cO2View/components/common/screen/deviceComp/supplyExhaustFan.vue";
import VideoMonitoring from "@/views/components/cO2View/components/common/screen/deviceComp/videoMonitoring.vue";
import WaterCooling from "@/views/components/cO2View/components/common/screen/deviceComp/waterCooling.vue";
import LargeEectrical from "@/views/components/cO2View/components/common/screen/deviceComp/largeEectrical.vue";
import Elevator from "@/views/components/cO2View/components/common/screen/deviceComp/elevator.vue";

import WarningList from "@/views/components/cO2View/components/common/WarningList";
export default {
  mixins: [ BaseView ], // 继承父模块
  components: {
    AirConditioning,
    Lighting,
    WarningList,
    SupplyExhaustFan,
    VideoMonitoring,
    WaterCooling,
    LargeEectrical,
    Elevator
  },
  data() {
    return {
      leftTop: {},
      leftBtm: {},
      rightTop: {},
      rightBtm: {},
      middleTop: {},
    };
  },
  computed: {
    subLinks() {
      return this.initSubLinks("SecurityMgt", "/securityMgt/");
    },
  },
  created() {
  },
  mounted() {
    let screenSecurity = {};
    try {
      screenSecurity = JSON.parse(this.projectSettings.screenSecurity);
    } catch (ex) {
      // pass
    }
    this.leftTop = screenSecurity.leftTop || {};
    this.leftBtm = screenSecurity.leftBtm || {};
    this.rightTop = screenSecurity.rightTop || {};
    this.rightBtm = screenSecurity.rightBtm || {};
    this.middleTop = screenSecurity.middleTop || {};
  },
  methods: {
  }
};
</script>

<style lang="scss" scoped>
.ft {
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  text-align: center;
  height: 47vh;
}
</style>
