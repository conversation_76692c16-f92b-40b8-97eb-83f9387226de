<template>
  <div class="card">
    <div class="title"><span>{{title}}</span></div>
    <div class="body">
      <slot/>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
    };
  },
};
</script>

<style scoped lang="scss">
.card {
  width: 100%;
  box-sizing: border-box;
  background: url('/image/screen/card_content_bg.png') no-repeat 0;
  background-size: 100% 100%;
  .title {
    background: url('/image/screen/co2_nav_bg.png') no-repeat 0;
    background-size: auto 100%;
    position: relative;
    height: 36px;

    span {
      position: absolute;
      top: 6px;
      left: 26px;
      font-family: pangmenzhendao;
      font-size: 20px;
      color: #ffffff;
    }
  }
  .body {
    padding: 12px;

  }
}
</style>