<template>
  <div>

    <SlidingPanel v-if="show" :showLeftPanel="true" :showRightPanel="true">
      <!-- 左侧内容 -->
      <template #left>
        <iframe class="item" :src="`${iframeUrl}/#/card/left/EnergyElectricityUse`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/left/BimEnergyUsageTrends`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/left/BimWaterUsageTrends`" frameborder="0"></iframe>
      </template>

      <!-- 右侧内容 -->
      <template #right>
        <iframe class="item" :src="`${iframeUrl}/#/card/right/EnergyCarbonEmission`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/right/EnergyCarbonPrediction`" frameborder="0"></iframe>
        <iframe class="item" :src="`${iframeUrl}/#/card/right/BimSummaryElectricity`" frameborder="0"></iframe>
      </template>
    </SlidingPanel>

    <transition name="expand">
      <iframe v-if='componentTag' :key="componentTag" class="componentTag" :src="iframeSrc" frameborder="0"></iframe>
    </transition>
    <img class="close" @click="close" v-if="isclose" src="../assets/close.png" alt="">
  </div>
</template>

<script>
import SlidingPanel from "@/components/common/SlidingPanel.vue";
export default {
  components: {
    SlidingPanel,

  }, data() {
    return {
      iframeUrl,
      componentTag: '',
      show: true,
      isclose: false,
    }
  },
  computed: {
    iframeSrc() {
      return this.iframeUrl + `/#/card/${this.componentTag}`;
    }
  },
  methods: {
    close() {

      this.componentTag = ''
      this.isclose = false
    },
    nengyuan(index) {
      console.log(index, 1212);
      this.show = false
      if (index == 0) {
        this.componentTag = 'energyMgt/electricity/summary'
        this.isclose = true
      } else if (index == 1) {
        this.componentTag = 'energyMgt/electricity/subOption'
        this.isclose = true
      } else if (index == 2) {
        this.componentTag = 'energyMgt/electricity/partition'
        this.isclose = true
      } else if (index == 3) {
        this.componentTag = 'bimEnergyReport?type=electricity'
        this.isclose = true
      } else if (index == 4) {
        this.componentTag = 'energyTopology?type=electricity&pageTitle=用电拓扑图'
        this.isclose = true
      } else if (index == 5) {
        this.componentTag = 'energyMgt/water/summary'
        this.isclose = true
      } else if (index == 6) {
        this.componentTag = 'energyMgt/water/subOption'
        this.isclose = true
      }
      else if (index == 7) {
        this.componentTag = 'energyMgt/water/partition'
        this.isclose = true
      }
      else if (index == 8) {
        this.componentTag = 'bimEnergyReport?type=water'
        this.isclose = true
      }
      else if (index == 9) {
        this.componentTag = 'energyTopology?type=water&pageTitle=用水拓扑图'
        this.isclose = true
      }
      else if (index == 10) {
        this.componentTag = 'energyWarning'
        this.isclose = true
      } else {
        this.componentTag = ''
        this.show = true
        this.isclose = false
      }
    },
  }
};
</script>

<style scoped lang="less">
.item {
  width: 336px;
  height: 300px;
  // overflow: hidden;
  margin-bottom: 10px;
}

.left-panel .item:nth-child(1) {
  height: 287px;
}

.left-panel .item:nth-child(2) {
  height: 347px;
}

.left-panel .item:nth-child(3) {
  height: 348px;
}

.right-panel .item:nth-child(2) {
  height: 330px;
}

.right-panel .item:nth-child(3) {
  height: 330px;
}

.expand-enter-active,
.expand-leave-active {
  transition: all 0.8s ease;
}

.expand-enter,
.expand-leave-to

/* 离开时使用的状态 (即消失后的状态) */
  {
  transform: scale(0);
  opacity: 0;
}

.expand-enter {
  transform-origin: center;
}

.componentTag {
  position: fixed;
  z-index: 1;
  top: 80px;
  left: 1%;
  width: 98%;
  height: 890px;
  opacity: 1;
  transform-origin: center;
  /* 动画从中间展开 */
}


.close {
  position: fixed;
  z-index: 100;
  top: 85px;
  right: 25px;
  font-size: 55px;
  color: #fff;
  cursor: pointer;
  width: 30px;
  height: 30px;
}


.nengyuan {
  .left-panel {
    position: fixed;
    z-index: 1;
    width: 336px;
    top: 70px;
    left: 15px;
    height: 980px;
    // background: url("../assets/image/left.png");
    // background-size: 100% 100%;
  }

  .lefti1 {
    width: 336px;
    height: 320px;
  }

  .lefti2 {
    width: 336px;
    height: 350px;
  }

  .right-panel {
    position: fixed;
    z-index: 1;
    right: 15px;
    width: 336px;
    top: 70px;
    height: 980px;
  }
}
</style>