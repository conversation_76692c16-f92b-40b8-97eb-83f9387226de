<template>
  <div class="alll" v-show="shouall">
    <FitScreen :width="1920" :height="1080">
      <div class="voice-container" v-if="false">
        <!-- <iframe class="voice-iframe" src="https://3d.dddtask.cn/engineer-qw/ai-test/ai-dist/index.html"
          allow="microphone"></iframe> -->
      </div>
      <!-- <iframe src="http://139.196.200.244:8060/#/login?autoLogin=1&username=admin&password=lanxing121%21" frameborder="0"  
        v-show="false"></iframe> -->
      <div v-show="false" :key="parkId">
        <iframe id="myIframe"
          :src="`${iframeUrl}/#/login?autoLogin=1&username=admin&password=lanxing121%21&buildingId=${parkId}`"
          frameborder="0">
          :key="parkId"
        </iframe>
        <iframe id="myIframe1"
          :src="`${iframeUrl}/#/card/BimMessageIntervalFresh?freshList=warningList_10&deviceTypes=DT10,ZM10_5&buildingId=1,2&buildingIdSet=${parkId}`"
          frameborder="0">
        </iframe>
      </div>
      <!-- <iframe ref="iframe1" v-show="false" class="componentTag" :key="resourceId"
        :src="`${iframeUrl}/#/card/ParkingSystemTabs?resourceId=${resourceId}&type=CWTCQ00`" frameborder="0"></iframe> -->
      <transition name="expand" mode="out-in" v-if="deviceId">
        <iframe class="right2 item1" :width="iframeWidth" :src="iframeSrc" :key="iframeSrc" frameborder="0"></iframe>
      </transition>
      <transition name="expand" mode="out-in">
        <iframe class="sbiframe" id="sbiframe" v-if="issbiframe"
          src="https://qiye.3dzhanting.cn/share-project.html?ids=hMZ6Lw4fiu2iHLp5PIYD3A==" frameborder="0">
        </iframe>
      </transition>
      <img class="ai" @click="openai" v-if="true" :class="{ 'ai-active': isWebSocketConnected }" src="../assets/AI1.png"
        alt="" />
      <img class="close" @click="closesbiframe" v-if="issbiframe" src="../assets/close.png" alt="" />
      <div class="container">
        <div class="bg"></div>
        <component v-if="iframeLoaded && !deviceId" @seedsb="seedsb" @update-data="handleDataFromChild"
          :is="componentTag" ref="childComponent" :sblist="sblist" :sbtitle="sbtitle" :key="componentKey"></component>
        <!-- 
        <iframe id="ifram" ref="mainIframe" class="iframe" name="mainIframe" src="https://3d.dddtask.cn/enginner-xufeng/bacheng3D/index.html"
          frameborder="0"></iframe> -->
        <!-- 头部内容 -->
        <div class="head">
          <p class="title">{{ captions }}</p>
        </div>
        <div class="xiala" v-if="false">
          <div class="xiala-item">
            <p>当前地块：</p>
            <el-select v-model="curBuilding" placeholder="请选择地块" size="small" @change="handleBuildingChange">
              <el-option label="F15B-01北地块" :value="1"></el-option>
              <el-option label="F15C-01南地块" :value="2"></el-option>
            </el-select>
          </div>
        </div>
        <div class="now-time">
          <!-- <img class="img" src="../assets/image/log.png" alt="" /> -->
          <span>{{ timeStr }}</span>
          <!--<span>2023-03-19 15:45:25</span>-->
        </div>
        <div class="select" v-if="false">
          <div class="el-select" placeholder="selectvalue" @click="toggleContent">
            <!-- 可以添加一个指示器，显示当前状态，比如箭头或加号/减号 -->
            <span class="pp">{{ selectvalue }}{{ selectvalue1 }}</span>
            <span class="sp" v-if="showlist">▲</span>
            <span class="sp" v-else>▼</span>
          </div>
          <transition name="expand1" @before-enter="beforeEnter" @enter="enter" @before-leave="beforeLeave"
            @leave="leave">
            <div class="content" v-show="showlist">
              <div v-for="(item, index) in floorlist" :key="index">
                <div class="butn" @click="sendlou(item.name)">
                  {{ item.name }}
                </div>
                <div class="btnbtn" v-for="(item1, index1) in floorlist[index].floor" :key="index1">
                  <div class="btn1" @click="sendTofloor(item1.name)">
                    {{ item1.num }}
                  </div>
                </div>
              </div>
            </div>
          </transition>
        </div>
        <!-- 
        <div
          class="xuhua"
          :class="modelindex == 1 ? 'flist1' : 'flist'"
          v-if="isshowfloorcd"
          @click="sendxs()"
        >
          {{ modelname }}
        </div> -->
        <div class="floorcd" v-if="isshowfloorcd && !deviceId">
          <!-- <p class="tit">楼栋</p> -->
          <div @click="selectbuild(item, index1)" :class="floorindex1 == index1 ? 'flist1' : 'flist'"
            v-for="(item, index1) in fllist" :key="index1" v-show="!hideBuildings || item.name === '整体建筑'">
            {{ item.bname }}
          </div>
        </div>
        <div class="floorcd1" v-if="showfloor">
          <!-- <p class="tit">{{ fllist[floorindex].name }}</p> -->
          <!-- <img class="return" @click="returnbuild()" src="../assets/image/return.png" alt=""> -->
          <div class="flist" @click="
            selectfloor(
              item,
              index1,
              fllist[floorindex].uename,
              fllist[floorindex].list.length
            )
            " v-for="(item, index1) in fllist[floorindex].list" :key="index1"
            :class="floorindex2 == index1 ? 'flist1' : 'flist'">
            {{ floorindex == 1 && item == '4F' ? 'JCF' : floorindex == 1 && index1 < 9 && 1 < index1 ?
              fllist[floorindex].list[index1 + 1] : floorindex == 2 && item == '12F' ? 'JFF' : item }} </div>
          </div>
          <div class="groups" v-if="false">
            <el-radio-group v-model="isCollapse" style="margin-bottom: 20px">
            </el-radio-group>
            <el-menu default-active="2" class="el-menu-vertical-demo" :collapse="isCollapse" background-color="#12355D"
              text-color="#00e5ff" active-text-color="#00e5ff" @open="handleOpen" @close="handleClose"
              :unique-opened="true">
              <el-sub-menu v-for="(building, buildingIndex) in setlist" :key="buildingIndex"
                :index="`${buildingIndex + 1}`">
                <template #title>
                  <span class="subitem">{{ building.name }}</span>
                </template>
                <el-menu-item-group>
                  <el-menu-item v-for="(floor, floorIndex) in building.child" :key="floorIndex"
                    @click="loucxuanz(floorIndex, building.name + floor.title)"
                    :index="`${buildingIndex + 1}-${floorIndex + 1}`">
                    {{ floor.title }}
                  </el-menu-item>
                </el-menu-item-group>
              </el-sub-menu>
              <!-- <el-sub-menu index="2">
              <template #title>
                <span class="subitem">2楼</span>
              </template>
              <el-menu-item-group>
                <el-menu-item index="2-1">B1</el-menu-item>
                <el-menu-item index="2-2">B2</el-menu-item>
              </el-menu-item-group>
            </el-sub-menu>
            <el-sub-menu index="3">
              <template #title>
                <span class="subitem">3楼</span>
              </template>
              <el-menu-item-group>
                <el-menu-item index="3-1">B1</el-menu-item>
                <el-menu-item index="3-2">B2</el-menu-item>
              </el-menu-item-group>
            </el-sub-menu> -->
            </el-menu>
          </div>
          <div class="btt2" @click="sendxs" @mouseenter="expandList" @mouseleave="startCloseTimer"
            v-if="alarmList.length">
            <img class="imgg" src="../assets/image/bjicon.png" alt="" />
            <p class="bjw">{{ alarmList.length }}</p>
          </div>
          <div class="bjlist" @mouseenter="cancelCloseTimer" @mouseleave="startCloseTimer"
            v-show="activeCollapse === '1'">
            <el-collapse v-model="activeCollapse">
              <el-collapse-item name="1">
                <div class="alarm-list">
                  <div v-for="(item, index) in alarmList" :key="index" class="alarm-item">
                    <div class="alarm-content">
                      <div class="alarm-title">
                        <span class="alarm-name">{{ item.deviceName }}</span>
                        <span :class="item.severity == '一级'
                          ? 'alarm-level1'
                          : item.severity == '二级'
                            ? 'alarm-level2'
                            : 'alarm-level3'
                          ">{{ item.severity }}</span>
                      </div>
                      <div class="alarm-time">
                        发生时间：{{ item.reportedAt }}
                      </div>
                    </div>
                    <div class="alarm-detail">
                      <span class="detail-text" @click="seedbj(item)">详情 ></span>
                    </div>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div class="btt">
            <button class="btt1" @click="changetqlist">
              <img class="ttqq" src="../assets/img/tq.png" alt="" />
            </button>
            <button class="btt1" @click="seedfw()">
              <img src="../assets/image/flooricon.png" alt="" />
            </button>

            <button class="btt1">
              <img class="imgg" @click="handleLogout()" src="../assets/image/tuichu.png" alt="" />
            </button>
          </div>
          <!-- 底部菜单 -->
          <div class="bot">
            <div class="bot1" v-for="(item, index) in botlist" :key="index" @mouseleave="closeBot"
              @mouseenter="openBot(index, $event)" @click="selectBot(index, item)">
              <div :class="selectedIndex == index ? 'activeimg' : 'img'">
                <img :src="require(`../assets/image/b${selectedIndex == index ? '' : 'a'
                  }${index + 1}.png`)
                  " alt="" />
              </div>
              <p :class="selectedIndex == index ? 'p2' : 'p1'">{{ item.name }}</p>
            </div>
          </div>
          <div class="fuwei" @click="seed('fuwei')" v-if="false">
            <img src="../assets/image/fuwei.png" alt="" />
            <p>复位</p>
          </div>

          <div class="caidan" v-show="fwshow" v-if="false">
            <img src="../assets/image/home.png" @click="show4 = !show4" class="cdimg" alt="" />
            <el-collapse-transition>
              <div class="cd" v-show="show4">
                <img src="../assets/image/home1.png" alt="" @click="seed('fuwei')" />
                <img src="../assets/image/home2.png" @click="changela()" alt="" />
              </div>
            </el-collapse-transition>
          </div>
          <div class="caidanlist" v-show="false">
            <div class="caidanimg1" @click="show3 = !show3">
              <img class="caidanicon1" src="../assets/image/caidanicon1.png" alt="" />
              <div class="caifonsiez">B3栋</div>
              <img class="caixiangxia" src="../assets/image/caixiangxia.png" alt="" />
            </div>
            <el-collapse-transition>
              <div v-show="show3">
                <div class="list" v-for="(item, index) in loulist" :key="index"
                  :class="{ active: xuanzindex === index }" @click="loucxuanz(index, '空调箱b3栋' + item.title)">
                  {{ item.title }}
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </div>
        <!-- 中间内容 -->
        <el-collapse-transition>
          <div class="boxtap1 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1"
            v-show="fenyeTag === 'component0'">
            <div class="tapli" v-for="(item, index) in ['漫游管理', '大屏显示']" :key="item"
              :style="{ color: index === resIndex0 ? '#14fcd5' : '' }" @click="(resIndex0 = index), setoptions(item)">
              {{ item }}
            </div>
          </div>
        </el-collapse-transition>
        <el-collapse-transition>
          <div class="boxtap2 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1"
            v-show="fenyeTag === 'component1'">
            <div class="tapli" v-for="(item, index) in items" :style="{ color: index === resIndex1 ? '#14fcd5' : '' }"
              :key="index" @click="(resIndex1 = index), seedchild(index)">
              {{ item.title }}
            </div>
          </div>
        </el-collapse-transition>
        <!-- <el-collapse-transition>
          <div class="boxtap2 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1"
            v-show="fenyeTag === 'component2'">
            <div class="tapli" v-for="(item, index) in [
              '用电概览',
              '分项用电',
              // '分区用电',
              '用电报表',
              // '用电拓扑图',
              '用水概览',
              // '分项用水',
              '分区用水',
              '用水报表',
              // '用水拓扑图',
              '异常用能报警',
            ]" :style="{ color: index === resIndex2 ? '#14fcd5' : '' }" :key="item"
              @click="(resIndex2 = index), seednengyuan(index)">
              {{ item }}
            </div>
          </div>
        </el-collapse-transition> -->
        <el-collapse-transition>
          <div class="boxtap3 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1"
            v-show="fenyeTag === 'component2'">
            <div class="tapli" v-for="(item, index) in [
              '停车场管理',
              // '电子巡更',
              '入侵报警',
              '门禁管理',
              '视频监控',
            ]" :style="{ color: index === resIndex3 ? '#14fcd5' : '' }" :key="item"
              @click="(resIndex3 = index), seedanquan(index)">
              {{ item }}
            </div>
          </div>
        </el-collapse-transition>
        <el-collapse-transition>
          <div class="boxtap4 boxt" @mouseenter="cancelClose" @mouseleave="closeBot1"
            v-show="fenyeTag === 'component3'">
            <div class="tapli" v-for="(item, index) in [
              '资产档案',
              '报警管理',
              '维修管理',
              '保养管理',
              '巡检管理',
              '排班管理',
              '资料管理',
            ]" :style="{ color: index === resIndex4 ? '#14fcd5' : '' }" :key="item"
              @click="(resIndex4 = index), seedyunwei(index)">
              {{ item }}
            </div>
          </div>
        </el-collapse-transition>

        <!-- <div class="boxtap5" v-if="fenyeTag === 'component4'">
        <div class="tapli" v-for="item in ['停车场管理']" :key="item">
          {{ item }}
        </div>
      </div> -->
        <!-- <div class="boxtap6" v-if="fenyeTag === 'component5'">
        <div class="tapli" v-for="item in ['楼栋招商管理']" :key="item">
          {{ item }}
        </div>
      </div> -->
        <transition name="content-toggle">
          <div class="content1" v-if="showtq">
            <div class="xuanzeqi">
              <p class="pp">时间与现实同步</p>
              <input class="switch-btn switch-btn-animbg" v-model="isChecked" @change="handleCheckboxChange"
                type="checkbox" checked />
            </div>
            <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
              <div class="tianqi" v-for="(item, index) in tqlist" :key="index" @click="tqchange1(index, item.time)">
                <img class="img" :src="require(`../assets/img/${tq1 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 1}.png`)
                  " alt="" />
                <p class="time">{{ item.time }}</p>
              </div>
            </div>
            <div class="xuanzeqi">
              <p class="pp">天气设置</p>
            </div>
            <div :class="isChecked == true ? 'tianjiandtime1' : 'tianjiandtime'">
              <div class="tianqi" v-for="(item, index) in tqlist1" :key="index" @click="tqchange(index, item.uename)">
                <img class="img" :src="require(`../assets/img/${tq2 == index ? 'tianqi1' : 'tianqi'
                  }/tianqi${index + 5}.png`)
                  " alt="" />
                <p class="time">{{ item.time }}</p>
              </div>
            </div>
            <!-- <label><input class="switch-btn switch-btn-animbg" type="checkbox" checked> 默认选中</label> -->
          </div>
        </transition>
    </FitScreen>
  </div>
</template>
<script>
import Title from "@/components/common/Title.vue";
import Title1 from "@/components/common/Title1.vue";
import component1 from "@/views/index.vue";
import component2 from "@/views/device.vue";
// import component13 from "@/views/peidian.vue";
// import component3 from "@/views/nengyuan.vue";
import component3 from "@/views/anquan.vue";
import component4 from "@/views/yunwei.vue";
// import component5 from "@/views/moduan.vue";
// import component7 from "@/views/zhenkong.vue";
// import component8 from "@/views/centerAc.vue";
// import component9 from "@/views/feipai.vue";
// import component10 from "@/views/reshui.vue";
// import component12 from "@/views/zilaishui.vue";
// import component2 from "@/views/dianbiao.vue";
// import component6 from "@/views/kongya.vue";

// import component21 from "@/views/nenghao.vue";
import axios from "axios";
import { VoiceHelper } from '../ai-voice-helper';
import '../ai-voice-helper/style.css'
let voiceHelper;
import { onMounted, onBeforeUnmount } from 'vue';
import { log } from '../../../ruoyi-ui-demo/src/components/byEditor/js/utils/mqtt';

export default {
  components: {
    component3,
    component2,
    component1,
    // component2,
    // component3,
    component4,
    // component5,
    // component6,
    // component8,
    // component9,
    // component10,
    // component12,
    // component13,
    // component3,
    // component11,
    // component7,
    Title,
    Title1,
  },

  data() {
    return {
      componentKey: 0,
      curBuilding: 1,
      shouxl: true,
      iframeWidth: "372px",
      deviceId: "",
      alarmdata: [],
      severity: "", //'报警等级'
      parkId: null, // 用于接收 parkId
      deviceListCache: new Map(), // 用来缓存设备数据
      isshowfloorcd: true,
      modelname: "虚化",
      modelindex: 0,
      alldeviceList: [],
      issbiframe: false,
      isWebSocketConnected: false, // 跟踪WebSocket连接状态
      shouldKeepWebSocketConnected: false, // 用户是否希望保持WebSocket连接
      hideBuildings: false, // 控制是否隐藏其他楼栋
      iframeLoaded: true,
      iframeUrl,
      tqlist: [
        {
          time: "7:00",
        },
        {
          time: "12:00",
        },
        {
          time: "17:00",
        },
        {
          time: "22:00",
        },
      ],
      isChecked: true,
      tqlist1: [
        {
          time: "晴朗",
          uename: "Sunny",
        },
        {
          time: "多云",
          uename: "Cloudy",
        },
        {
          time: "下雨",
          uename: "Rain",
        },
        {
          time: "下雪",
          uename: "Snow",
        },
      ],
      showtq: false,
      weather: "晴朗",
      weatherstatus: "开",
      dsweather: "",

      tq1: 0,
      tq2: 0,
      shouall: false,
      items: [
        // {
        //   title: "机房动环",
        //   type: "shiDuChuanGanQi",
        // },
        {
          title: "电梯管理",
          type: "DT",
        },
        // {
        //   title: "照明系统",
        //   type: "ZM1",
        // },
        // {
        //   title: "电力监控",
        //   type: "DLJKXT",
        // },
        // {
        //   title: "冷热源",
        //   type:'',
        // },
        {
          title: "给排水",
          type: "JPSGL",
        },
        {
          title: "通风机",
          type: "SPFGL",
        },
        {
          title: "新风空调",
          type: "XFKTGL  ",
        },
      ],
      loading: true,
      setlou: false,
      lablevalue: false,
      fenyeTag: "",
      isseed: false, //是否发送数据
      loulist: [
        {
          title: "整体",
        },
        {
          title: "13F",
        },
        {
          title: "12F",
        },
        {
          title: "11F",
        },
        {
          title: "10F",
        },
        {
          title: "9F",
        },
        {
          title: "8F",
        },
        {
          title: "7F",
        },
        {
          title: "6F",
        },
        {
          title: "5F",
        },
        {
          title: "4F",
        },
        {
          title: "3F",
        },
        {
          title: "2F",
        },
        {
          title: "1F",
        },
      ],
      setlist: [
        {
          name: "B2栋",
          child: [
            {
              title: "整体",
            },
            {
              title: "顶楼",
            },
            {
              title: "4F",
            },
            {
              title: "3F",
            },
            {
              title: "2F",
            },
            {
              title: "1F",
            },
          ],
        },
        {
          name: "A1栋",
          child: [
            {
              title: "整体",
            },
            {
              title: "13F",
            },
            {
              title: "12F",
            },
            {
              title: "11F",
            },
            {
              title: "10F",
            },
            {
              title: "9F",
            },
            {
              title: "8F",
            },
            {
              title: "7F",
            },
            {
              title: "6F",
            },
            {
              title: "5F",
            },
            {
              title: "4F",
            },
            {
              title: "3F",
            },
            {
              title: "2F",
            },
            {
              title: "1F",
            },
          ],
        },
      ],
      show4: false,
      xuanzindex: "",
      show3: true,
      fwshow: true,
      timeStr: "",
      weather: "晴朗",
      isExpanded: true, // 控制bot容器展开/收起
      fwshow1: false,
      lastClickedTitle: "",
      showlist: false,
      showdata: true,
      componentTag: "component1",
      iframe,
      selectedIndex: 0,
      isButton2Active: false,
      captions,
      selectvalue: "整体场景",
      selectvalue1: "",
      floorlist: [
        {
          name: "整体场景",
          floor: [
            { num: "一期", name: "一期" },
            { num: "二期", name: "二期" },
            { num: "三期", name: "三期" },
            { num: "邻里中心", name: "邻里中心" },
          ],
        },

        {
          name: "B1栋",
          floor: [
            { num: "1F", name: "B1栋1F" },
            { num: "2F", name: "B1栋2F" },
            { num: "3F", name: "B1栋3F" },
            { num: "4F", name: "B1栋4F" },
            { num: "顶层", name: "B1栋顶层" },
          ],
        },
        {
          name: "B2栋",
          floor: [
            { num: "1F", name: "B2栋1F" },
            { num: "2F", name: "B2栋21F" },
            { num: "3F", name: "B2栋3F" },
            { num: "4F", name: "B2栋4F" },
            { num: "顶层", name: "B2栋顶层" },
          ],
        },
        {
          name: "B3栋",
          floor: [
            { num: "1F", name: "B3栋1F" },
            { num: "2F", name: "B3栋2F" },
            { num: "3F", name: "B3栋3F" },
            { num: "4F", name: "B3栋4F" },
            { num: "顶层", name: "B3栋顶层" },
          ],
        },
        {
          name: "B4栋",
          floor: [
            { num: "1F", name: "B4栋1F" },
            { num: "2F", name: "B4栋2F" },
            { num: "3F", name: "B4栋3F" },
            { num: "4F", name: "B4栋4F" },
            { num: "顶层", name: "B4栋顶层" },
          ],
        },
        {
          name: "W1栋",
          floor: [
            { num: "1F", name: "W1栋1F" },
            { num: "2F", name: "W1栋2F" },
            { num: "3F", name: "W1栋3F" },
            { num: "4F", name: "W1栋4F" },
          ],
        },
        {
          name: "W2栋",
          floor: [
            { num: "1F", name: "W2栋1F" },
            { num: "2F", name: "W2栋2F" },
            { num: "3F", name: "W2栋3F" },
            { num: "4F", name: "W2栋4F" },
          ],
        },
      ],
      isshowfloor: true, //是否显示
      showfloor: false,
      floorindex: 1,
      floorindex1: 0,
      floorindex2: 0,
      yyfloorindex: 0,
      flist: [
        "整体建筑",
        "A1栋",
        "A2栋",
        "A3栋",
        "A4栋",
        "A5栋",
        "B1栋",
        "B2栋",
        "B3栋",
        "B4栋",
        "B5栋",
        "B6栋",
        "A6地下室",
        "B7地下室",
      ],
      bflist: [
        {
          name: "整体建筑",
          uename: "ParkView",
          bname: "整体建筑",
          list: [""],
        },
        // {
        //   name: "A1栋",
        //   uename: "A1",
        //   bname: "A1栋",
        //   list: [
        //     "整体",
        //     "楼顶",
        //     "12F",
        //     "11F",
        //     "10F",
        //     "9F",
        //     "8F",
        //     "7F",
        //     "6F",
        //     "5F",
        //     "4F",
        //     "3F",
        //     "2F",
        //     "1F",
        //   ],
        // },
        // {
        //   name: "A2栋",
        //   bname: "A2栋",
        //   uename: "A2",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "A3栋",
        //   uename: "A3",
        //   bname: "A3栋",
        //   list: [
        //     "整体",
        //     "楼顶",
        //     "12F",
        //     "11F",
        //     "10F",
        //     "9F",
        //     "8F",
        //     "7F",
        //     "6F",
        //     "5F",
        //     "4F",
        //     "3F",
        //     "2F",
        //     "1F",
        //   ],
        // },
        // {
        //   name: "A4栋",
        //   bname: "A4栋",
        //   uename: "A4",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "A5栋",
        //   uename: "A5",
        //   bname: "A5栋",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "B1栋",
        //   bname: "B1栋",
        //   uename: "B1",
        //   list: [
        //     "整体",
        //     "楼顶",
        //     "14F",
        //     "13F",
        //     "12F",
        //     "11F",
        //     "10F",
        //     "9F",
        //     "8F",
        //     "7F",
        //     "6F",
        //     "5F",
        //     "4F",
        //     "3F",
        //     "2F",
        //     "1F",
        //   ],
        // },
        // {
        //   name: "B2栋",
        //   bname: "B2栋",
        //   uename: "B2",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "B3栋",
        //   bname: "B3栋",
        //   uename: "B3",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "B4栋",
        //   bname: "B4栋",
        //   uename: "B4",
        //   list: [
        //     "整体",
        //     "楼顶",
        //     "15F",
        //     "14F",
        //     "13F",
        //     "12F",
        //     "11F",
        //     "10F",
        //     "9F",
        //     "8F",
        //     "7F",
        //     "6F",
        //     "5F",
        //     "4F",
        //     "3F",
        //     "2F",
        //     "1F",
        //   ],
        // },
        // {
        //   name: "B5栋",
        //   bname: "B5栋",
        //   uename: "B5",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "B6栋",
        //   bname: "B6栋",
        //   uename: "B6",
        //   list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        // },
        // {
        //   name: "A6地下室",
        //   bname: "A6地下室",
        //   uename: "A6",
        //   list: ["-2F", "-1F"],
        // },
        // {
        //   name: "B7地下室",
        //   bname: "B7地下室",
        //   uename: "B7",
        //   list: ["-2F", "-1F"],
        // },
      ],
      fllist: [
        {
          name: "整体建筑",
          uename: "ParkView",
          bname: "整体建筑",
          list: [""],
        },
        {
          name: "医疗楼",
          uename: "JZ1",
          bname: "医疗楼",
          list: [
            "整体",
            "楼顶",
            "11F",
            "10F",
            "9F",
            "8F",
            "7F",
            "6F",
            "5F",
            "4F",
            "3F",
            "2F",
            "1F",
            "B2F",
            "B1F",
          ],
        },
        {
          name: "科教楼",
          bname: "科教楼",
          uename: "JZ2",
          list: [
            "整体",
            "楼顶",
            "12F",
            "11F",
            "10F",
            "9F",
            "8F",
            "7F",
            "6F",
            "5F",
            "4F",
            "3F",
            "2F",
            "1F",
            "B2F",
            "B1F",
          ],
        },
        {
          name: "A3栋",
          uename: "JZ3",
          bname: "病房楼",
          list: [
            "整体",
            "楼顶",
            "11F",
            "10F",
            "9F",
            "8F",
            "7F",
            "6F",
            "5F",
            "4F",
            "3F",
            "2F",
            "1F",
          ],
        },
        {
          name: "A4栋",
          bname: "后勤楼",
          uename: "JZ4",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "A5栋",
          uename: "JZ5",
          bname: "门诊楼",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "A6栋",
          bname: "急诊楼",
          uename: "JZ6",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "A7栋",
          bname: "核医学楼",
          uename: "JZ7",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },
        {
          name: "A8栋",
          bname: "行政楼",
          uename: "JZ8",
          list: ["整体", "楼顶", "5F", "4F", "3F", "2F", "1F"],
        },

      ],
      //电梯楼栋
      dtfllist: [
        {
          name: "整体建筑",
          uename: "ParkView",
          bname: "整体建筑",
          list: [""],
        },
        {
          name: "A1栋",
          uename: "A1dt",
          bname: "A1栋",
          list: [],
        },
        {
          name: "A2栋",
          bname: "A2栋",
          uename: "A2dt",
          list: [],
        },
        {
          name: "A3栋",
          uename: "A3dt",
          bname: "A3栋",
          list: [],
        },
        {
          name: "A4栋",
          bname: "A4栋",
          uename: "A4dt",
          list: [],
        },
        {
          name: "A5栋",
          uename: "A5dt",
          bname: "A5栋",
          list: [],
        },
        {
          name: "B1栋",
          bname: "B1栋",
          uename: "B1dt",
          list: [],
        },
        {
          name: "B2栋",
          bname: "B2栋",
          uename: "B2dt",
          list: [],
        },
        {
          name: "B3栋",
          bname: "B3栋",
          uename: "B3dt",
          list: [],
        },
        {
          name: "B4栋",
          bname: "B4栋",
          uename: "B4dt",
          list: [],
        },
        {
          name: "B5栋",
          bname: "B5栋",
          uename: "B5dt",
          list: [],
        },
        {
          name: "B6栋",
          bname: "B6栋",
          uename: "B6dt",
          list: [],
        },
      ],
      options: [
        {
          value: "整体场景",
          label: "整体场景",
        },
        {
          value: "B1栋",
          label: "B1栋",
        },
        {
          value: "B2栋",
          label: "B2栋",
        },
        {
          value: "B3栋",
          label: "B3栋",
        },
        {
          value: "B4栋",
          label: "B4栋",
        },
        {
          value: "W1栋",
          label: "W1栋",
        },
        {
          value: "W2栋",
          label: "W2栋",
        },
      ],

      botlist: [
        {
          name: "综合态势",
        },
        {
          name: "设备管理",
        },
        // {
        //   name: "用能管理",
        // },
        {
          name: "安全管理",
        },

        {
          name: "运维管理",
        },
        {
          name: "信息管理",
        },
      ],
      isdh: false,
      resourceId: "182",
      childData: "",
      opt: "",
      clickCounter: 0, // 添加一个计数器
      closeTimeout: null, // 用于延迟关闭的计时器
      buildId: "",
      floorId: "",
      getname: "无", //查询点位的name字段
      sblist: [],
      sbtitle: [],
      carstatuslist: [],
      isWatching: true, // 用来控制是否启用监听
      isclickdt: false,
      activeCollapse: "",
      alarmList: [],
      dtdatalist: [],
      closeTimer: null,
      isRecording: false, // 是否正在录音
      questionText: "", // 语音转文字的结果
      replyText: "", // AI 智能助手的回复
      mediaRecorder: null, // MediaRecorder 实例
      audioChunks: [], // 存储录音数据
      websocket: null, // WebSocket连接实例
    };
  },
  computed: {
    iframeSrc() {
      return (
        this.iframeUrl + `/#/card/deviceDetailCardBig?deviceId=${this.deviceId}`
      );
    },
  },
  created() {
    // 从路由查询参数中获取 parkId
    this.parkId = this.$route.query.parkId;
    console.log("从查询参数中获取到的 parkId:", this.parkId);

    // 从localStorage中获取上次选择的地块，如果有的话
    const savedBuilding = localStorage.getItem('curBuilding');
    if (savedBuilding) {
      this.curBuilding = parseInt(savedBuilding);
      console.log("从缓存中获取地块值:", this.curBuilding);
    }

    setInterval(() => {
      this.formatDate();
    }, 1000);
    setTimeout(() => {
      this.shouall = true;
    }, 10000);
    // this.shouall = true
  },
  mounted() {
    this.getsblist();
    // WebSocket连接现在由用户通过点击AI图标来控制
    // this.initWebSocket();
    // // 添加全局点击事件监听器
    // setTimeout(() => {
    //   document.addEventListener('click', this.handleOutsideClick);
    // }, 2000);
    // this.initVoiceHelper()
    // window.addEventListener('message', event => {
    //   const data = event.data.data?.data
    //   console.log(data, "data");

    //   if (!data) return;
    //   if (data?.includes?.('复位')) {
    //     this.changefloor('fuwei')
    //   } else {
    //     const match = data.match(/\d+/);
    //     console.log('changefloor', match[0])
    //     if (match[0]) {
    //       this.changefloor('JZ' + match[0])
    //     }
    //   }
    // })
    var that = this;
    window.addEventListener("message", function (event) {
      let data =
        event && event.data && event.data.message
          ? event.data.message.name
          : "";
      console.log(data, "ueue");
      if (data == "open") {
        that.iframeWidth = "1890px";
      } else if (data == "close") {
        that.iframeWidth = "372px";
      }
      // if (event && event.data && event.data.type == "alarmMessage") {
      //   console.log(event.data.param.deviceId, 142);
      //   that.deviceId = event.data.param.deviceId;
      // }
    });
    document.addEventListener("click", this.handleGlobalClick);
    setInterval(() => {
      const iframe = document.getElementById("myIframe");
      if (iframe) {
        iframe.src = iframe.src;
      }
    }, 60000);
    ue.interface.setSliderValue = (value) => {
      // 首先定义一个映射对象

      console.log(value, "ue点击拿到的值");
      if (!isNaN(Number(value.data))) {
        console.log(value, "ue点击拿到的值");
        let did;
        if (!isNaN(Number(value.data))) {
          // 如果是数字，直接使用
          did = value.data;
        } else {
          console.log("未找到对应的设备ID映射");
          return;
        }
        console.log(did, "did");

        this.deviceId = this.alarmdata.find((item) => item.id == did)
          ? this.alarmdata.find((item) => item.id == did).deviceId
          : "";
        // const result = this.alarmdata;
        //   .map((item, index) => ({ item, index })) // 将元素和其下标一起打包
        //   .filter((obj) => obj.item.id == did); // 过滤匹配的元素
        // console.log(result);
        // if (result.length > 0) {
        //   this.deviceId = result[0].item.deviceId;
        //   // this.changedevice(result[0].item.deviceId, result[0].index, false);
        //   console.log(result[0].item.deviceId, "ue点击拿到的id");
        // } else {
        //   console.log("未找到对应的设备");
        // }
      }
      // this.deid = JSON.parse(value.data) - 43846
      // console.log(this.deid);
      // if (!isNaN(parseInt(value.data, 10))) {
      //   var dtdata1 = JSON.parse(JSON.stringify(this.dtdata))
      //   console.log(dtdata1);
      //   this.showdet = false
      //   // this.did = dtdata1.find(item => item.id == value.data)?.deviceid;
      //   // console.log(this.did);
      //   var didata = JSON.parse(dtdata1.find(item => item.id == value.data).channelCode);
      //   let data1 = dtdata1.find(item => item.id == value.data)
      //   // this.details = didata
      //   this.bid = data1.bid
      //   this.fid = data1.fid
      //   // this.hlsurl
      //   // this.bm = data1.note
      //   console.log(data1, 1111111);
      //   // this.getCameraData(did)
      // }
    };
    var that = this;

    window.addEventListener("message", function (event) {
      console.log(event.data, 1569);
      //event.data获取传过来的数据
      if (event.data.type == "function") {
        console.log(event.data, "登录成功");
        let name = event.data.name;
        let param = event.data.param;
        // //console.log(param.name, "sssssssssssssssssssssssss");

        if (that.shownum === "none") {
          if (param.name == "cxfb2881_2" || param.name == "cxfb1880_1") {
            that.shownum = "block";
          }
        }
      } else if (event.data.type == "finished") {
        that.loading = false;
        that.iframeLoaded = true;
      } else if (event.data.type == "autoLogin") {
        // console.log(event.data, '登录成功');
        // that.componentTag = 'component1'
        that.iframeLoaded = true;
      }
      else if (event.data.type == "messageIntervalFresh") {
        if (event.data.message.code == "deviveList") {
          console.log(
            that.summaryDevice(event.data.message.data).total,
            "实时设备数据"
          );
          that.dtdatalist = that
            .summaryDevice(event.data.message.data)
            .total.map((item) => {
              // 提取基本字段
              const { id, name, deviceDataBase, position } = item;

              // 查找故障状态的 valStr
              const statusObj = deviceDataBase.find(
                (data) => data.dmName === "故障状态"
              );
              const deviceStatus = statusObj ? statusObj.valStr : null; // 如果没找到则返回 null
              const buildingid = that.reverseMapBuildingId(
                position.split("#")[0]
              );
              // 查找所在楼层的 valStr
              const floorObj = deviceDataBase.find(
                (data) => data.dmName === "所在楼层"
              );
              // console.log(floorObj, "floorObj");

              let floor =
                floorObj && floorObj.dVal
                  ? floorObj.dVal == "1000.0"
                    ? 1
                    : Number(floorObj.dVal)
                  : null; // 如果没找到则返回 null
              // 如果 floor 是 "1000"，赋值为 "1"
              if (floor == 1000) {
                floor = 1;
              }
              // 返回新的对象
              return {
                id,
                name,
                deviceStatus,
                floor,
                buildingid,
              };
            });
          console.log(that.dtdatalist, "电梯数据");
          that.sendToUE41("elevator", that.dtdatalist);
        } else if (event.data.message.code == "warningList") {
          console.log(event.data.message.data, "实时告警数据");
          that.alarmList = event.data.message.data.warningList;
          // const finalData = that.fetchDataWithUeid(that.alarmList);
          that
            .fetchDataWithUeid(that.alarmList)
            .then((finalData) => {
              console.log(
                finalData.map((item) => item.projectData),
                "finalData"
              );
              that.alarmdata = finalData.map((item) => item.projectData);
              console.log(that.alarmdata, 8975);
              // if (that.isdh) {
              //   that.sendToUE41("alarm", that.alarmdata);
              // }
            })
            .catch((error) => {
              console.error("加载数据失败:", error);
            });
        }
      }

      let type = event && event.data && event.data.type ? event.data.type : "";
      // console.log(event, type, "收到的数据aq");

      if (type == "freshBimDeviceData") {
        let carlist = event.data.param;
        // console.log(carlist, '车位状态');
        that.carstatuslist = carlist
          .map((item) => {
            // 提取每个数据对象中的 id 和 dVal 和 dmName
            return item.deviceDataBase.map((subItem) => ({
              id: item.id,
              dVal: subItem.dVal,
              name: item.name,
            }));
          })
          .flat(); // 使用 flat() 展开嵌套数组
        console.log(that.carstatuslist, "车位状态");
      }
    });
  },
  beforeDestroy() {
    document.removeEventListener("click", this.handleGlobalClick);
    // 如果WebSocket连接还在，则关闭
    if (this.isWebSocketConnected) {
      this.closeWebSocket();
    }
  },
  methods: {
    // 初始化WebSocket连接
    initWebSocket() {
      // 如果已经有连接，先关闭
      if (this.websocket) {
        this.closeWebSocket();
      }

      // 创建新的WebSocket连接
      this.websocket = new WebSocket('wss://ai-command-test.3dzhanting.cn:8086/socket/3dview');

      // 连接建立时的处理
      this.websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        this.isWebSocketConnected = true; // 更新连接状态
      };

      // 接收消息的处理
      this.websocket.onmessage = (event) => {
        console.log('收到WebSocket消息:', event.data);
        try {
          // 尝试解析接收到的JSON数据
          const data = JSON.parse(event.data);
          // 如果数据包含命令，处理命令
          if (data) {
            this.handleWebSocketCommand(data);
          }
        } catch (e) {
          console.error('解析WebSocket消息失败:', e);
        }
      };

      // 连接关闭时的处理
      this.websocket.onclose = () => {
        console.log('WebSocket连接已关闭');
        this.isWebSocketConnected = false; // 更新连接状态

        // 只有在用户希望保持连接时才尝试重新连接
        if (this.shouldKeepWebSocketConnected) {
          setTimeout(() => {
            this.initWebSocket();
          }, 3000);
        }
      };

      // 连接出错时的处理
      this.websocket.onerror = (error) => {
        console.error('WebSocket连接错误:', error);
      };
    },

    // 处理接收到的WebSocket命令
    handleWebSocketCommand(data) {
      console.log('处理WebSocket命令:', data);
      // 根据命令类型执行相应操作
      if (data.command == "视点复位") {
        this.sendToUE4("ParkView");
      } else if (data.command == "楼宇详情" || data.command == "查看楼宇") {
        console.log(data.params['楼号'], "AI 回复：");
        this.yyfloorindex = this.fllist.findIndex(item => item.name.includes(data.params['楼号']));
        this.selectbuild(this.fllist[this.yyfloorindex], this.yyfloorindex)
      } else if (data.command == "查看楼层") {
        console.log(data.params['楼层'], "AI 回复：");

        console.log(this.fllist[this.yyfloorindex], "fllist");

        //  const result = this.fllist.find(item => item.name .includes(params['楼号']));
        let resultindex = this.fllist[this.yyfloorindex].list.findIndex(item => item.includes(data.params['楼层']));
        console.log(resultindex, "fllist");
        if (resultindex) {
          this.selectfloor(
            this.fllist[this.yyfloorindex].list[resultindex],
            resultindex,
            this.fllist[this.yyfloorindex].list[resultindex].uename,
            this.fllist[this.yyfloorindex].list.length
          )
          // this.selectbuild(this.fllist[floorindex], floorindex)
        }
      }

    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
        this.isWebSocketConnected = false; // 更新连接状态
      }
    },

    initVoiceHelper() {
      let container = document.querySelector('.voice-container') //语音组件的容器
      let options = {
        audioWorkletModuleUrl: './volume-meter-processor.js', // 语音识别的音频处理模块
        wakeUpMessage: '你好，我是小源', // 唤醒时的语音词
        exitMessage: '稍后再见', // 退出时的语音词
        voiceThreshold: 0.1, // 语音阈值,用于判断是否当前的音量是否大于阈值，从而判断是否需要唤醒
        chatApi: 'https://test.ai.3dzhanting.cn/v1/chat/completions', // 聊天接口
        voiceToTextApi: 'https://ai-api-test.3dzhanting.cn/rgApi/recognition', // 语音识别接口
        voiceAssetsDir: '/voiceAssets', // 资源目录
        voiceBroadcast: {
          api: 'https://ai-api-test.3dzhanting.cn/ttsApi/tts', // 语音播报接口
          refAudioPath: '/home/<USER>/model/77.mp3', // 播报参考音频
          textLang: 'zh', // 文字合成语言 
          promptLang: 'zh', // 语音合成语言
          promptText: '在不了解的状况下喜欢上一个人，确发现那个人真实的一面比你想象的还要美好，这应该算的上幸运', // 语音提示文本
        }
      }
      voiceHelper = new VoiceHelper(container, options);
      // 监听聊天回复 
      voiceHelper.addEventListener('chatReply', (e) => {
        let { command, params, reply } = e
        console.log('command', command) //当前返回的命令  
        console.log('params', params) //当前返回命令的参数
        console.log('reply', reply) //当前回复的内容

        if (command == "视点复位") {
          this.sendToUE4("ParkView");
        } else if (command == "楼宇详情" || command == "查看楼宇") {
          console.log(params['楼号'], "AI 回复：");
          //  const result = this.fllist.find(item => item.name .includes(params['楼号']));
          this.yyfloorindex = this.fllist.findIndex(item => item.name.includes(params['楼号']));

          this.selectbuild(this.fllist[this.yyfloorindex], this.yyfloorindex)
        } else if (command == "查看楼层") {
          console.log(params['楼层'], "AI 回复：");
          console.log(this.fllist[this.yyfloorindex], "fllist");

          //  const result = this.fllist.find(item => item.name .includes(params['楼号']));
          let resultindex = this.fllist[this.yyfloorindex].list.findIndex(item => item.includes(params['楼层']));
          console.log(resultindex, "fllist");
          if (resultindex) {
            this.selectfloor(
              this.fllist[this.yyfloorindex].list[resultindex],
              resultindex,
              this.fllist[this.yyfloorindex].list[resultindex].uename,
              this.fllist[this.yyfloorindex].list.length
            )
            // this.selectbuild(this.fllist[floorindex], floorindex)
          }
        }

      })
    },
    reverseMapBuildingId(buildingid) {
      // 定义反向映射，根据图片数据
      const reverseBuildingMap = {
        "C-1": "B4",
        "C-2": "B5",
        "C-3": "B6",
        "C-4": "B3",
        "C-5": "B2",
        "C-6": "B1",
        "C-7": "B7",
      };

      // 检查 buildingid 是否在反向映射中存在
      if (buildingid in reverseBuildingMap) {
        return reverseBuildingMap[buildingid]; // 添加 "#" 以匹配格式
      } else {
        // 如果没有匹配，则返回原始 buildingid 并添加 "#"
        return buildingid;
      }
    },
    seedbj(data) {
      this.severity = data.severity;
      this.getname = "";
      console.log(data, "897");

      this.deviceId = data.ueid;
      this.fetchProjectSet(1, "_YShNh-ObjYY3Xv15seK0w==", 0, "", "", data.ueid)
        .then((jujiaodata) => {
          console.log(jujiaodata, "选中的报警设备");
          this.sendToUE41("jujiao", jujiaodata);
        })
        .catch((error) => {
          console.error("加载数据失败:", error);
        });
      // let jujiaodata = this.fetchProjectSet(
      //   1,
      //   "_YShNh-ObjYY3Xv15seK0w==",
      //   0,
      //   "",
      //   "",
      //   data.ueid
      // );
      // console.log(jujiaodata, "选中的报警设备");
      // this.sendToUE41("jujiao", jujiaodata);
    },
    // 设备统计逻辑
    summaryDevice(deviceList) {
      // 统计设备总数
      var res = {
        total: [],

        running: [],
        stop: [],

        offline: [],
        online: [],

        healthy: [],
        warning: [], // 报警
        fault: [], // 故障

        error: [],
        unknow: [],

        cool: [],
        hot: [],
        dehumidification: [],
      };
      let list = deviceList || this.deviceList;

      list.map((d) => {
        // 格式化单个设备数据
        d = this.initItemData(d);
        console.log(this.showImages, d.showImages);
        if (d) {
          d = this.initItemBaseData(d, {
            showImages: this.showImages || d.showImages || "0",
            hasLock: this.hasLock || d.hasLock || "0",
            hasEnergy: this.hasEnergy || d.hasEnergy || "0",
            hasEleProspection:
              this.hasEleProspection || d.hasEleProspection || "0",
            hasCOLink: this.hasCOLink || d.hasCOLink || "0",
            protoTypeDisplay: this.protoTypeDisplay || d.protoTypeDisplay || "",
            forceUpdate: this.forceUpdate || d.forceUpdate || "0",
            hasEditPosition: this.hasEditPosition || d.hasEditPosition || "0",
          });
          // 统计个数
          res.total.push(d);
          // 开启  关闭  无状态(传感器)
          if (
            d.workStatus.indexOf("停") >= 0 ||
            d.workStatus.indexOf("关") >= 0
          ) {
            res.stop.push(d);
          } else if (
            d.workStatus.indexOf("运") >= 0 ||
            d.workStatus.indexOf("开") >= 0 ||
            d.workStatus.indexOf("启") >= 0
          ) {
            res.running.push(d);
          }
          // 告警 故障 健康
          if (d.warningStatus == "告警") {
            res.warning.push(d);
          } else if (d.warningStatus == "故障") {
            res.fault.push(d);
          } else {
            res.healthy.push(d);
          }
          // 在线 离线
          if (d.communicateStatus == "离线") {
            res.offline.push(d);
          } else {
            res.online.push(d);
          }
        }
      });
      console.log("summaryDevice ==>", list, res);
      // 增加模式计数
      // this.deviceList.map(d => {
      //   if(d.mode == "cool") {
      //     res.cool.push(d);
      //   } else if(d.mode == "hot") {
      //     res.hot.push(d);
      //   } else if(d.mode == "dehumidification") {
      //     res.dehumidification.push(d);
      //   }
      // });
      return res;
    },
    // 格式化设备特殊信息
    initItemData(item) {
      return item;
    },
    // 格式化设备信息
    initItemBaseData(item, opts) {
      let that = this;
      let d = item;
      if (!d) {
        return false;
      }

      // 设备有效时长
      d.activePercentage = 100;
      d.activeColor = "#67C23A";
      if (d.scrapTime != "" && d.activationTime != "") {
        d.activePercentage = parseFloat(
          (
            (100 * this.$moment(d.scrapTime).diff(this.$moment(), "day")) /
            this.$moment(d.scrapTime).diff(
              this.$moment(d.activationTime),
              "day"
            )
          ).toFixed(2)
        );
        if (d.activePercentage > 50) {
          d.activeColor = "#67C23A";
        } else if (d.activePercentage > 30) {
          d.activeColor = "#E6A23C";
        } else {
          d.activeColor = "#F56C6C";
        }
      }

      // 原逻辑，优先判定在线，再判定告警，再判断开关
      // d.status = "healthy";
      // // 在线 离线
      // if(d.communicateStatus == '离线') {
      //   d.status = "offline";
      // } else {
      //   if(d.workStatus.indexOf('停') >= 0 || d.workStatus.indexOf('关') >= 0) {
      //     d.status = "stop";
      //   } else if(d.workStatus.indexOf('运') >= 0 || d.workStatus.indexOf('开') >= 0 ) {
      //     d.status = "running";
      //   }
      //   if(d.warningStatus != "健康") {
      //     d.status = "alert";
      //   }
      // }

      d.showImages = opts ? opts.showImages : this.showImages || "0"; // 绑定设备原理图是否显示
      d.hasEnergy = opts ? opts.hasEnergy : this.hasEnergy || "0"; // 绑定设备用能是否显示
      d.hasEleProspection = opts
        ? opts.hasEleProspection
        : this.hasEleProspection || "0"; // 电气设备是否显示
      d.hasCOLink = opts ? opts.hasCOLink : this.hasCOLink || "0"; // CO联动是否显示
      d.hasLock = opts ? opts.hasLock : this.hasLock || "0"; // 绑定设备是否有锁定控制
      d.protoTypeDisplay = opts
        ? opts.protoTypeDisplay
        : this.protoTypeDisplay || "";
      d.forceUpdate = opts ? opts.forceUpdate : this.forceUpdate || "0";
      d.strategyOpts = this.strategyOpts; // 绑定设备控制策略模型
      d.hasEditPosition = opts
        ? opts.hasEditPosition
        : this.hasEditPosition || "0"; // 位置编辑是否显示

      d.mode = "";
      // 默认未加锁
      d.isLock = false;
      // 是否有点位被锁定
      d.hasLocked = false;
      // 锁定点位列表
      d.lockedList = [];
      // 默认不可控制
      d.hasControl = false;
      // 默认无监测点
      d.hasMonitor = false;
      // 功率趋势控制显示 tag中包含monitor,power
      d.hasMonitorAndPower = false;
      // 电流趋势控制显示 tag中包含monitor,current
      d.hasMonitorAndCurrent = false;
      // // 电压趋势控制显示 tag中包含monitor,voltage
      d.hasMonitorAndVoltage = false;
      // 输出全局设置 globalSwichTypes
      if (d.deviceDataBase.length > 0) {
        d.deviceDataBase.map((dd, _i) => {
          d.hasLocked = d.hasLocked || dd.drLocked == 1;
          d.hasControl = d.hasControl || dd.drId > 0;
          d.hasMonitor = d.hasMonitor || dd.dmTag.indexOf("monitor") >= 0;
          d.hasMonitorAndPower =
            d.hasMonitorAndPower ||
            ["monitor", "power"].every((it) => dd.dmTag.indexOf(it) >= 0);
          d.hasMonitorAndCurrent =
            d.hasMonitorAndCurrent ||
            ["monitor", "current"].every((it) => dd.dmTag.indexOf(it) >= 0);
          d.hasMonitorAndVoltage =
            d.hasMonitorAndVoltage ||
            ["monitor", "voltage"].every((it) => dd.dmTag.indexOf(it) >= 0);
          d.isLock = d.isLock || dd.drLocked > 0;

          if (
            dd.dVal &&
            !isNaN(Number(dd.dVal)) &&
            dd.dVal.toString().includes(".") &&
            dd.dVal.toString().split(".")[1].length > 6
          ) {
            dd.dVal = Number(dd.dVal).toFixed(2);
          }
          if (
            dd.drVal &&
            !isNaN(Number(dd.drVal)) &&
            dd.drVal.toString().includes(".") &&
            dd.drVal.toString().split(".")[1].length > 6
          ) {
            dd.drVal = Number(dd.drVal).toFixed(2);
          }

          // 翻译所有状态成文本
          // if(dd.dDataType == "float") {
          //   dd.dVal = parseFloat(dd.dVal).toFixed(2);
          // }
          if (dd.drDataType == "float") {
            dd.editType = "inputNum";
            dd.valStr = dd.dVal + " " + dd.dDataUnit;
            dd.rValStr = dd.drVal + " " + dd.drDataUnit;
          } else {
            if (dd.dOtherData.trim() != "") {
              // 针对读写 note 不一样的情况，需要分开
              var ddMap = {}; // 读点
              var ddrMap = {}; // 写点
              var kl = dd.dOtherData.trim().split(";");
              var klr = dd.drOtherData.trim().split(";");
              for (var i in kl) {
                var s = kl[i].trim().split(":");
                if (s.length == 2) {
                  ddMap[s[0]] = s[1];
                }
              }
              for (var ir in klr) {
                var sr = klr[ir].trim().split(":");
                if (sr.length == 2) {
                  ddrMap[sr[0]] = sr[1];
                }
              }
              dd.otherDataMap = ddMap; // 读点
              dd.otherRDataMap = ddrMap; // 写点
              dd.valStr = ddMap[dd.dVal] || "";
              dd.rValStr = ddrMap[dd.drVal] || "";

              if (kl.length < 1) {
                dd.editType = "input";
              } else if (kl.length === 1) {
                dd.editType = "button";
              } else if (kl.length == 2) {
                dd.editType = "switch";
              } else if (kl.length > 2) {
                dd.editType = "select";
              }

              // 地图上的设备快速开关
              if (dd.dmTag.indexOf("status") >= 0) {
                d.swichObj = {
                  drId: dd.drId,
                  drVal: dd.drVal,
                  drDataType: dd.drDataType,
                  drRealVal: "",
                };
              }

              // 针对空调 统计空调运行模式个数
              // if(dd.dmName.indexOf("模式") >= 0) {
              //   if(dd.valStr.indexOf("制冷") >= 0) {
              //     d.mode = "cool";
              //   } else if(dd.valStr.indexOf("制热") >= 0) {
              //     d.mode = "hot";
              //   } else if(dd.valStr.indexOf("除湿") >= 0) {
              //     d.mode = "dehumidification";
              //   }
              // }
            } else {
              dd.editType = "input";
              dd.valStr = dd.dVal + " " + dd.dDataUnit;
              dd.rValStr = dd.drVal + " " + dd.drDataUnit;
            }
          }

          // 更新判定设备当前状态 (拿到最新开关状态)
          if (dd.dmTag.indexOf("status") >= 0) {
            if (
              dd.valStr.indexOf("开") >= 0 ||
              dd.valStr.indexOf("启") >= 0 ||
              dd.valStr.indexOf("运") >= 0
            ) {
              d.status = "running";
              d.workStatus = "运行";
              d.swichObj.drRealVal = 1;
            } else if (
              dd.valStr.indexOf("关") >= 0 ||
              dd.valStr.indexOf("停") >= 0
            ) {
              d.status = "stop";
              d.workStatus = "停止";
              d.swichObj.drRealVal = 0;
            }
          }

          // 补充锁定的值
          if (dd.drLocked == 1) {
            d.lockedList.push(dd.dmName + ": " + dd.rValStr);
          }
          // 处理摄像头的url
          if (
            dd.dmName &&
            dd.dmName.toLowerCase() == "fullpath" &&
            dd.dVal != ""
          ) {
            d.fullPath = dd.dVal;
          }
          if (dd.dmName && dd.dmName.toLowerCase() == "rtsp" && dd.dVal != "") {
            d.rtsp = dd.dVal;
          }
          // 容错，被其他组件 mixins 后，没有 this.globalVal 变量
          // if(typeof this.globalVal == "object") {
          //   let hasKey = Object.keys(this.globalVal).indexOf(dd.dmName) >= 0;
          //   if(hasKey) {
          //     this.globalSwichTypes[dd.dmName] = {
          //       dmName: dd.dmName,
          //       drDataUnit: dd.drDataUnit,
          //       editType: dd.editType,
          //       otherDataMap: dd.otherDataMap,
          //     }
          //   }
          // }
        });
      }

      // 输出全局设置 globalSwichTypes
      if (d.deviceDataWarn && d.deviceDataWarn.length > 0) {
        d.deviceDataWarn.map((dd, _i) => {
          if (dd.dOtherData.trim() != "") {
            // 针对读写 note 不一样的情况，需要分开
            var ddMap = {}; // 读点
            var ddrMap = {}; // 写点
            var kl = dd.dOtherData.trim().split(";");
            var klr = dd.drOtherData.trim().split(";");
            for (var i in kl) {
              var s = kl[i].trim().split(":");
              if (s.length == 2) {
                ddMap[s[0]] = s[1];
              }
            }
            for (var ir in klr) {
              var sr = klr[ir].trim().split(":");
              if (sr.length == 2) {
                ddrMap[sr[0]] = sr[1];
              }
            }
            dd.otherDataMap = ddMap; // 读点
            dd.otherRDataMap = ddrMap; // 写点
            dd.valStr = ddMap[dd.dVal] || "";
          } else {
            dd.editType = "input";
            dd.valStr = dd.dVal + " " + dd.dDataUnit;
          }
        });
      }

      if (d.deviceDataEnv.length > 0) {
        d.envStr = d.deviceDataEnv[0].dVal + d.deviceDataEnv[0].dDataUnit;
        d.deviceDataEnv.map((dd) => {
          dd.valStr = dd.dVal + " " + dd.dDataUnit;
        });
      }

      // 判定设备当前状态，影响卡片样式
      // d.status = "default";
      d.dRuning = "default";
      d.dStatus = "default";
      d.dOnline = "default";
      // 在线 离线
      if (d.communicateStatus.indexOf("离线") >= 0) {
        // d.status = "offline";
        d.dOnline = "offline";
      } else if (d.communicateStatus.indexOf("在线") >= 0) {
        // d.status = "online";
        d.dOnline = "online";
      }
      if (d.workStatus.indexOf("停") >= 0 || d.workStatus.indexOf("关") >= 0) {
        // d.status = "stop";
        d.dRuning = "stop";
      } else if (
        d.workStatus.indexOf("开") >= 0 ||
        d.workStatus.indexOf("启") >= 0 ||
        d.workStatus.indexOf("运") >= 0
      ) {
        // d.status = "running";
        d.dRuning = "running";
      }
      if (d.warningStatus == "故障") {
        // d.status = "fault";
        d.dStatus = "fault";
      } else if (d.warningStatus == "告警") {
        // d.status = "warning";
        d.dStatus = "warning";
      } else if (d.warningStatus == "健康") {
        // d.status = "healthy";
        // d.dStatus = "healthy";
      }

      // 优先判定在线，再判定告警，再判断开关
      d.status = "healthy";
      // 在线 离线
      if (d.communicateStatus == "离线") {
        d.status = "offline";
      } else {
        if (
          d.workStatus.indexOf("停") >= 0 ||
          d.workStatus.indexOf("关") >= 0
        ) {
          d.status = "stop";
        } else if (
          d.workStatus.indexOf("运") >= 0 ||
          d.workStatus.indexOf("开") >= 0 ||
          d.workStatus.indexOf("启") >= 0
        ) {
          d.status = "running";
        }
        if (d.warningStatus == "故障") {
          d.status = "fault";
        } else if (d.warningStatus == "告警") {
          d.status = "warning";
        }
      }

      // 设备2D地图信息
      // 2.5D 位置大小在 otherData 里面
      let _od = {
        left: d.xAxis,
        top: d.yAxis,
        width: 40, // 图标尺寸
        height: 40,
        padding: 2,
        imgWidth: 36, // 图片尺寸
        imgHeight: 36,
      };
      let _od2 = {};
      try {
        _od2 = JSON.parse(d.otherData);
      } catch (e) {
        // pass
      }
      d.pos = {
        ..._od,
        ..._od2,
      };
      d.icon = d.icon.replace("default", d.status);

      // 设备预警状态
      // if(d.warningRuleList.length > 0) {
      //   d.warningRuleList.map(r => {
      //     r.isWarning = false;
      //     r.warningColor = "#67C23A";
      //     if(d.warningList.length > 0) {
      //       d.warningList.map(w => {
      //         if(w.warningCategory == r.name) {
      //           r.isWarning = true;
      //           r.warningColor = "#F56C6C";
      //           //r.warningColor = r.severity == "严重" ? "#F56C6C" : "#E6A23C";
      //         }
      //       });
      //     }
      //   });
      // }

      // 设备预警记录
      // if(d.warningHistoryList.length > 0) {
      //   d.warningHistoryList.map(w => {
      //     w.hasFixedStr = that.gf.getWarningStr(w.hasFixed);
      //     w.flowStatusStr = that.gf.getWarningStr(w.flowStatus);
      //     return w;
      //   });
      // }

      // 维保记录
      // if(d.maintenanceList.length > 0) {
      //   d.maintenanceStr = d.maintenanceList[0].operator + '(' + d.maintenanceList[0].updatedAt.split(" ")[0] + ')';
      //   d.maintenanceList.map(dd => {
      //     if(dd.pictures.trim() != "") {
      //       try {
      //         dd.pictureList = JSON.parse(dd.pictures);
      //         dd.pictureUrlList = dd.pictureList.map( p => p.url );
      //       } catch(e) {
      //         dd.pictureList = [];
      //       }
      //     }
      //   });
      // }

      // 维保信息
      d.brand = d.brand || "";
      d.model = d.model || "";
      d.contact = d.contact || "";
      d.mobile = d.mobile || "";
      try {
        if (typeof d.maintenance == "string") {
          d.maintenance = JSON.parse(d.maintenance);
        }
      } catch (e) {
        d.maintenance = {
          contact: "",
          mobile: "",
          company: "",
        };
      }
      d.maintenanceTime = "";
      try {
        if (d.activationTime) {
          d.maintenanceTime = this.$moment(d.activationTime)
            .add(Math.max(0, d.maintenanceSpan), "day")
            .format("YYYY-MM-DD HH:mm:ss");
        }
      } catch (e) {
        // pass
      }
      // 基础信息
      try {
        d.noteObj = JSON.parse(d.note);
      } catch (e) {
        d.noteObj = null;
      }
      // 铭牌信息
      try {
        d.nameplateObj = JSON.parse(d.nameplate);
      } catch (e) {
        d.nameplateObj = null;
      }
      // 电路信息
      try {
        d.circuitObj = JSON.parse(d.circuit);
      } catch (e) {
        d.circuitObj = null;
      }
      // 设备图片
      try {
        d.pictureList = JSON.parse(d.pictures);
        d.pictureUrlList = d.pictureList.map((p) => p.url);
      } catch (e) {
        d.pictureList = [];
      }
      // 设备文件
      try {
        d.fileList = JSON.parse(d.files);
        //d.fileUrlList = d.fileList.map(p => p.url);
      } catch (e) {
        d.fileList = [];
      }
      d.fileListFlat = [];
      try {
        for (let tl in d.fileList) {
          if (Array.isArray(d.fileList[tl])) {
            for (let i = 0; i < d.fileList[tl].length; i++) {
              d.fileListFlat.push(d.fileList[tl][i]);
            }
          } else {
            d.fileListFlat.push(d.fileList[tl]);
          }
        }
      } catch (e) {
        // pass
      }

      d.activationTime = d.activationTime || "";
      d.scrapTime = d.scrapTime || "";

      // 设备控制策略
      d.strategy =
        d.strategyList.length > 0 ? d.strategyList.map((s) => s.id) : [];
      return d;
    },
    // 退出登录
    handleLogout() {

      // const iframe1 = document.getElementById("myIframe1");
      // const iframe = document.getElementById("myIframe");
      // iframe.contentWindow.postMessage(
      //   {
      //     type: "exit",
      //     data: "exit",
      //   },
      //   "*"
      // );
      // iframe1.contentWindow.postMessage(
      //   {
      //     type: "exit",
      //     data: "exit",
      //   },
      //   "*"
      // );
      console.log(iframe, 1276);
      // 清除浏览器缓存
      localStorage.clear();
      sessionStorage.clear();
      // 跳转到登录页

      this.$router.push({ name: "Login" });
      setTimeout(() => {
      }, 500);
    },
    changeidid(id) {
      this.resourceId = id;
    },
    closesbiframe() {
      this.issbiframe = false;
    },
    openai() {
      if (this.isWebSocketConnected) {
        // 如果WebSocket已连接，则关闭连接
        this.shouldKeepWebSocketConnected = false; // 用户不希望保持连接
        this.closeWebSocket();
        this.$message({
          message: 'AI助手已关闭',
          type: 'info',
          duration: 2000
        });
      } else {
        // 如果WebSocket未连接，则建立连接
        this.shouldKeepWebSocketConnected = true; // 用户希望保持连接
        this.initWebSocket();
        this.$message({
          message: 'AI助手已启动',
          type: 'success',
          duration: 2000
        });
      }
    },
    // 开始录音
    async startRecording() {
      try {
        // 获取麦克风权限并开始录音
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: true,
        });
        this.mediaRecorder = new MediaRecorder(stream);

        // 监听录音数据
        this.mediaRecorder.ondataavailable = (event) => {
          this.audioChunks.push(event.data);
        };

        // 录音结束时处理数据
        this.mediaRecorder.onstop = async () => {
          // 创建 Blob 对象，格式为 OGG/Opus
          const blob = new Blob(this.audioChunks, {
            type: "audio/ogg; codecs=opus",
          });
          this.audioChunks = []; // 清空录音数据

          // 调用语音转文字接口
          await this.speechToText(blob);
        };

        // 开始录音
        this.mediaRecorder.start();
        this.isRecording = true;
      } catch (error) {
        console.error("录音失败：", error);
      }
    },

    // 停止录音
    stopRecording() {
      if (this.mediaRecorder) {
        this.mediaRecorder.stop();
        this.isRecording = false;
      }
    },
    // 语音转文字
    async speechToText(blob) {
      console.log(blob, "blob");

      try {
        const formData = new FormData();
        formData.append("file", blob, "audio.ogg"); // 将 Blob 添加到 FormData

        const res = await axios.post(
          "https://ai-api-test.3dzhanting.cn/rgApi/recognition",
          formData,
          {
            headers: {
              "Content-Type": "multipart/form-data",
            },
          }
        );

        // 获取语音转文字结果
        this.questionText = res.data.data;
        console.log("语音转文字结果：", this.questionText);

        // 转文字成功后，向 AI 智能助手提问
        await this.askAI();
      } catch (error) {
        console.error("语音转文字失败：", error);
      }
    },
    // 向 AI 智能助手提问
    async askAI() {
      try {
        const res = await axios({
          method: "POST",
          url: "https://ai-api-test.3dzhanting.cn/voiceagent/v1/chat/completions",
          headers: {
            "Content-Type": "application/json",
          },
          data: JSON.stringify({
            messages: [{ role: "user", content: this.questionText }],
            // messages: [{ role: "user", content: "帮我切换到A6栋" }],
            model: "qwen2.5-instruct",
            tools: ["device_split"],
          }),
        });

        // 获取 AI 回复
        this.replyText = JSON.parse(res.data.choices[0].message.content);
        console.log("AI 回复：", this.replyText);
        if (this.replyText.command == "视点复位") {
          this.sendToUE4("ParkView");
        } else if (this.replyText.command == "楼宇详情") {
          console.log(this.replyText.params['楼号'], "AI 回复：");
          //  const result = this.fllist.find(item => item.name .includes(this.replyText.params['楼号']));
          const resultindex = this.fllist.findIndex(item => item.name.includes(this.replyText.params['楼号']));
          if (resultindex) {
            this.selectbuild(this.fllist[resultindex], resultindex)
          }

        }
        // 播放回复语音
        await this.playReply(res);
      } catch (error) {
        console.error("AI 提问失败：", error);
      }
    },
    // 播放回复语音
    playReply(res) {
      try {
        // 构造 TTS 接口 URL，添加所有必要参数
        const url = `https://ai-api-test.3dzhanting.cn/ttsApi/tts?text=${JSON.parse(res.data.choices[0].message.content).reply
          }&text_lang=zh&ref_audio_path=/home/<USER>/model/77.mp3&prompt_lang=zh&prompt_text=在不了解的状况下喜欢上一个人，确发现那个人真实的一面比你想象的还要美好，这应该算的上幸运&text_split_method=cut5&batch_size=1&media_type=wav&streaming_mode=true`;

        // 创建新的 audio 元素而不是使用 ref
        const createAudio = document.createElement("audio");
        createAudio.src = url;
        createAudio.play();
      } catch (error) {
        console.error("播放回复语音失败：", error);
      }
    },
    returnbuild() {
      // this.showfloor = false
    },
    //选择楼栋
    selectbuild(item, index) {
      console.log(item, index, '建筑');

      this.floorindex2 = 0;
      this.buildId = item.name;
      this.floorId = "";

      // 如果选择的是"整体建筑"，切换 hideBuildings 状态
      if (item.name === "整体建筑") {
        // this.hideBuildings = !this.hideBuildings; // 切换显示和隐藏
        this.buildId = "";
        this.floorId = "";
      } else if (!this.isshowfloor) {
        this.hideBuildings = false; // 点击其他楼栋时始终显示所有楼栋
        this.showfloor = false; // 显示楼层部分
      } else {
        this.hideBuildings = false; // 点击其他楼栋时始终显示所有楼栋
        this.showfloor = true; // 显示楼层部分
      }
      this.$nextTick(() => {
        if (this.$refs.childComponent) {
          if (this.buildId && this.floorId) {
            console.log(this.buildId, this.floorId, "buildId, floorId");
            this.$refs.childComponent.updateChildValue(this.floorId);
          } else {
            this.$refs.childComponent.updateChildValue(this.floorId);
          }
        }
      });
      if (this.isseed) {
        if (item.name === "整体建筑" && this.sbtitle == "视频监控设备列表") {
          this.fetchProjectSet(
            1,
            "_YShNh-ObjYY3Xv15seK0w==",
            0,
            "室外",
            "室外"
          );
        } else {
          this.fetchProjectSet(
            1,
            "_YShNh-ObjYY3Xv15seK0w==",
            0,
            this.buildId,
            this.floorId
          );
        }
      }

      let name =
        item.name === "A6地下室"
          ? "A6地下室栋"
          : item.name === "B7地下室"
            ? "B7地下室栋"
            : item.uename; //楼
      this.sendToUE4(
        item.uename == "ParkView"
          ? item.uename
          : `${item.uename}_BuildingView`
      );
      // this.lastClickedTitle = name;
      if (index) {
        // this.showfloor = !this.showfloor;
        this.floorindex = index;
      } else {
        this.showfloor = false;
      }
      this.floorindex1 = index;
    },
    async selectfloor(item, index, name, length) {
      console.log(item, index, name, 128527);

      this.$nextTick(() => {
        if (this.$refs.childComponent) {
          this.$refs.childComponent.updateChildValue(true);
          if (this.buildId.includes("A6")) {
            this.$refs.childComponent.changeresourceId(index ? 182 : 183);
            this.changeidid(index ? 182 : 181);
          } else if (this.buildId.includes("B7")) {
            this.$refs.childComponent.changeresourceId(index ? 449 : 450);
            this.changeidid(index ? 449 : 450);
          }
        }
      });

      if (index == 1) {
        item = length - 3 + "F";
      } else if (index == 0) {
        item = "";
      }
      console.log(item, index, name);
      let fid = item;
      if ((index == 0 && name == "A6") || (index == 0 && name == "B7")) {
        item = "2F";
        fid = "-2F";
      } else if ((index == 1 && name == "A6") || (index == 1 && name == "B7")) {
        item = "1F";
        fid = "-1F";
      }
      this.floorId = fid;
      console.log(this.floorId, "当前楼层");
      this.floorindex2 = index;
      await this.fetchProjectSet(
        1,
        "_YShNh-ObjYY3Xv15seK0w==",
        0,
        this.buildId,
        this.floorId,
        ''
      );

      // this.sendToUE4(this.lastClickedTitle);
      // setTimeout(() => {
      //   this.sendToUE4(name + item);
      // // }, 1000);
      // if (
      //   this.lastClickedTitle.includes("-2F") ||
      //   this.lastClickedTitle.includes("-1F")
      // ) {
      console.log(item, "894");
      if (item) {
        this.sendToUE4(`${name}_Floor${item.split("F").join("")}`);
      } else {
        this.sendToUE4(`${name}_BuildingView`);
      }

      //陈修改的
      // this.sendToUE4(
      //   (name == 'A6地下室'
      //     ? 'A6栋' + (item == "-1F" ? '1F' : '2F')
      //     : name === 'B7地下室'
      //       ? 'B7栋' + (item == "-1F" ? '1F' : '2F')
      //       : name + item)
      // );

      // this.sendToUE4(name + item);
      // } else {
      //   this.sendToUE4(
      //     name == "A6地下室"
      //       ? "A6地下室" + item + "栋"
      //       : name === "B7地下室"
      //       ? "B7地下室" + item + "栋"
      //       : name + item
      //   );
      //陈修改的
      // this.sendToUE4(
      //   (name == 'A6地下室'
      //     ? 'A6栋' + (item == "-1F" ? '1F' : '2F')
      //     : name === 'B7地下室'
      //       ? 'B7栋' + (item == "-1F" ? '1F' : '2F')
      //       : name + item)
      // );
      // }

      //陈修改的
      // this.lastClickedTitle = name == 'A6地下室'
      //   ? 'A6栋' + (item == "-1F" ? '1F' : '2F')
      //   : name === 'B7地下室'
      //     ? 'B7栋' + (item == "-1F" ? '1F' : '2F')
      //     : name + item;
    },

    seedsb(item) {
      // this.sendToUE4(this.lastClickedTitle);
      // if (this.lastClickedTitle.includes('-2F') || this.lastClickedTitle.includes('-1F')) {
      //   setTimeout(() => {
      //     this.sendToUE4(item);
      //   }, 1000);
      // } else {
      //   this.sendToUE4(item);
      // }
      // this.lastClickedTitle = item;
    },
    setoptions(name) {
      if (name == "漫游管理") {
        this.componentTag = "";
      }

      this.isshowfloorcd = false;
      // this.sendToUE4(this.opt);
      this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4(name);
      this.lastClickedTitle = "漫游管理";
      this.opt = name;
    },
    handleCheckboxChange: function () {
      // 复选框状态发生改变时触发的方法getlist
      if (this.isChecked) {
        console.log("复选框被选中");
        // this.dsweather = setInterval(() => {
        //   this.getweather()
        // }, 180000);
        this.sendToUE4("TurnOnTimeSync");
      } else {
        console.log("复选框未被选中");
        // clearInterval(this.dsweather)
        // this.dsweather = ''
        this.sendToUE4("TurnOffTimeSync");
      }
    },
    tqchange(index, name) {
      this.tq2 = index;
      this.sendToUE4(name);
    },
    tqchange1(index, name) {
      this.tq1 = index;
      if (name == "7:00") {
        this.sendToUE4("Morning");
      } else if (name == "12:00") {
        this.sendToUE4("Noon");
      } else if (name == "17:00") {
        this.sendToUE4("Evening");
      } else if (name == "22:00") {
        this.sendToUE4("Night");
      }
    },
    changetqlist() {
      this.showtq = !this.showtq;
    },
    handleGlobalClick(event) {
      // 检查点击的目标是否在按钮或 content1 内
      const button = this.$el.querySelector(".btt1");
      const content = this.$el.querySelector(".content1");

      if (
        !button.contains(event.target) && // 不在按钮内
        (!content || !content.contains(event.target)) // 不在 content1 内
      ) {
        this.showtq = false; // 隐藏 content1
      }
    },
    seedchild(index) {
      this.isdh = false;
      console.log(this.isclickdt, "isclickdt");
      // this.fllist = this.bflist;
      if (index != 0) {
        if (this.isclickdt) {
          this.sendToUE4("fuwei");
        }
      }
      // this.sendToUE4('fuwei');
      console.log(index, "设备");
      if (index == 1) {
        this.sbtitle = "给排水设备列表";
        this.getname =
          "隔油装置,消防水泵,一用一备,生活水泵（变频）,生活水泵变频";
        this.isseed = true;
      }
      // else if (index == 2) {
      //   this.sbtitle = "电力监控设备列表";
      //   this.getname = "电力监控";
      //   this.isseed = true;
      // }
      else if (index == 9) {
        this.sbtitle = "照明系统设备列表";
        this.getname = "照明";
        this.isseed = true;
      } else if (index == 0) {
        this.sbtitle = "电梯系统设备列表";
        this.getname = "直梯";
        this.isseed = true;
        // this.fllist = this.dtfllist;
        this.sendToUE4(this.lastClickedTitle);
        this.sendToUE4("diantishijian");
        this.lastClickedTitle = "diantishijian";
        this.isclickdt = true;
      }

      // else if (index == 0) {
      //   // this.sbtitle = '无线对讲设备列表'
      //   // this.getname = '无线对讲'
      // }
      else if (index == 2) {
        this.sbtitle = "通风机设备列表";
        this.getname =
          "排油烟风机直启,平时风机,平时兼消防风机,通用双速风机,消防风机,排风风机,排烟兼排风风机,送风风机,补风兼送风风机";
        this.isseed = true;
      } else if (index == 3) {
        this.sbtitle = "新风空调设备列表";
        this.getname = "室内机,多联式室内机";
        this.isseed = true;
      }

      if (this.isseed && index != 9) {
        this.fetchProjectSet(
          1,
          "_YShNh-ObjYY3Xv15seK0w==",
          0,
          this.buildId,
          this.floorId
        );
      }

      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.shebei) {
          this.$refs.childComponent.shebei(index);
        }
      });
    },
    seednengyuan(index) {
      this.isdh = false;
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.nengyuan) {
          this.$refs.childComponent.nengyuan(index);
        }
      });
    },
    sendxs() {
      this.isdh = false;
      if (this.modelname == "虚化") {
        this.sendToUE4("xuhua");
        this.modelname = "虚化";
        let alarmdata = [
          {
            buildId: "A6地下室",
            type: 1,
            deviceId: "1211015",
            roomId: "EAF-B2-01-03",
            parkId: "0",
            floorId: "-2F",
            v3DbuildId: null,
            pzjson:
              '{"modelId":"wFR6wTmeC61pAjqJRUOttA==","position":[869.9063608176652,-24.182948066398254,225.7219261093062],"ue_position":[86990.63608176653,22572.19261093062,-2418.2948066398253],"scale":[1,1,1],"rotation":[0,0,0],"id":5083,"floorNum":2,"name":"平时风机"}',
            name: "平时风机",
            json: "",
            id: 525268,
            sceneJson: null,
            projectId: 673,
            title: "平时风机-A6#B2F-015",
          },
        ];
        this.sendToUE41("alarm", this.alarmdata);
        this.isdh = true;
      }
    },

    seedanquan(index) {
      this.isdh = false;
      console.log(index, "安全");
      if (index == 3) {
        this.sbtitle = "视频监控设备列表";
        this.getname =
          "半球型摄像机,枪型摄像机,电梯半球,高清半球型摄像机,高清枪型摄像机";
        this.isseed = true;
      } else if (index == 2) {
        this.sbtitle = "门禁管理设备列表";
        this.getname = "门禁";
        this.isseed = true;
      } else if (index == 1) {
        this.sbtitle = "入侵报警设备列表";
        this.getname = "被动红外探测器,紧急按钮";
        this.isseed = true;
      } else if (index == 4) {
        this.sbtitle = "电子巡更设备列表";
        this.getname = "电子巡更";
        this.isseed = true;
      }
      // else if (index == 1) {
      //   this.sbtitle = "无线对讲设备列表";
      //   this.getname = "无线对讲";
      //   this.isseed = true;
      // }
      else if (index == 0) {
        this.sbtitle = "停车场车位列表";
        this.getname = "车位";
        this.isseed = true;
      }

      if (index == 0) {
        this.isWatching = false;
      } else {
        this.isWatching = true;
      }

      if (this.isseed && index != 9) {
        this.fetchProjectSet(
          1,
          "_YShNh-ObjYY3Xv15seK0w==",
          0,
          this.buildId,
          this.floorId,
          ''
        );
      }
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.anquan) {
          this.$refs.childComponent.anquan(index);
        }
      });
    },
    seedyunwei(index) {
      this.isdh = false;
      this.$nextTick(() => {
        if (this.$refs.childComponent && this.$refs.childComponent.yunwei) {
          this.$refs.childComponent.yunwei(index);
        }
      });
    },
    sendToUE4(data) {
      this.isclickdt = false;
      // 调用UE4的相关函数，传递数据
      ue4(data);
      console.log(data, "UE收到的");
    },
    changela() {
      this.lablevalue = !this.lablevalue;
      this.seed1(this.lablevalue);
    },
    loucxuanz(index, data) {
      console.log(index, data, "楼层数据");
      this.xuanzindex = index;
      this.seed(data);
      this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4(data);
      this.lastClickedTitle = data;
      // this.fetchProjectSet(1, '_YShNh-ObjYY3Xv15seK0w==', 0, 'A1#', '1F')
      if (this.isseed) {
        this.fetchProjectSet(1, "_YShNh-ObjYY3Xv15seK0w==", 0, "", "");
      }
    },
    sendTofloor(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    sendToUE41(st, data) {
      console.log();
      // 调用UE4的相关函数，传递数据
      ue4(st, data);
      console.log(st, data, "UE收到的");
    },
    sendlou(value) {
      this.seed(value);
      this.selectvalue = value;
      this.showlist = false;
    },
    toggleContent() {
      this.showlist = !this.showlist;
    },
    beforeEnter(el) {
      el.style.height = "0"; // 设置初始高度
    },
    enter(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 应用过渡样式
        el.style.height = "255px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.3s ease"; // 再次确认过渡样式
        el.style.height = "0"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    toggleContent1() {
      this.fwshow1 = !this.fwshow1;
    },
    beforeEnter1(el) {
      el.style.height = "20px"; // 设置初始高度
    },
    enter1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 应用过渡样式
        el.style.height = "102px"; // 目标高度
        el.addEventListener("transitionend", done);
      });
    },
    beforeLeave1(el) {
      el.style.height = `${el.offsetHeight}px`; // 确保从当前高度开始过渡
    },
    leave1(el, done) {
      requestAnimationFrame(() => {
        el.style.transition = "height 0.1s ease"; // 再次确认过渡样式
        el.style.height = "20px"; // 收缩目标高度
        el.addEventListener("transitionend", done);
      });
    },
    handleChange(value) {
      this.seed(value);
      console.log(value);
    },
    toggleBot() {
      this.isExpanded = !this.isExpanded;
      console.log(this.isExpanded);
    },
    //https://rest{api.amap.com/v3/weather/weatherInfo?city=320200&key=1c046ae4b42c14be43fb7966539e744e
    getweather() {
      const apiUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
      const cityCode = "320200"; // 你的城市代码
      const apiKey = "1c046ae4b42c14be43fb7966539e744e";
      const params = {
        city: cityCode,
        key: apiKey,
      };
      axios
        .get(apiUrl, { params })
        .then((response) => {
          // 请求成功处理
          console.log("响应数据:", response.data.lives[0]);
          const weatherCondition = response.data.lives[0].weather;
          if (weatherCondition.includes("晴")) {
            this.weather = "晴朗";
          } else if (weatherCondition.includes("雨")) {
            this.weather = "下雨";
          } else if (weatherCondition.includes("雪")) {
            this.weather = "下雪";
          } else {
            this.weather = "阴天";
          }
          console.log(this.weather);
          this.sendToUE4(this.weather);
        })
        .catch((error) => {
          // 请求失败处理
          console.error("请求失败:", error);
        });
    },
    seed(item) {
      // const frame = document.getElementById("ifram");
      // frame.contentWindow.postMessage(
      //   {
      //     type: "function",
      //     name: "exec3d",
      //     param: { type: 1, data: item },
      //   },
      //   "*"
      // );
    },
    seed1(item) {
      console.log(item);

      const frame = document.getElementById("ifram");
      frame.contentWindow.postMessage(
        {
          type: "function",
          name: "exec3d",
          param: { type: 2, data: item },
        },
        "*"
      );
    },
    slideLeft() {
      const container = this.$refs.bot1Container;
      const scrollAmount = -900; // 调整滑动距离，负值表示向左滑动
      container.scrollLeft += scrollAmount;
    },
    slideRight() {
      const container = this.$refs.bot1Container;
      const scrollAmount = 900; // 调整滑动距离，正值表示向右滑动
      container.scrollLeft += scrollAmount;
    },

    // 新增的关闭方法
    closeBot() {
      // console.log('closeBot');
      // 延迟关闭菜单，给用户时间点击菜单项
      this.closeTimeout = setTimeout(() => {
        // this.selectedIndex = null;
        this.fenyeTag = null;
      }, 100); // 延迟300ms关闭
    },
    closeBot1() {
      // console.log('closeBot111111');
      // 延迟关闭菜单，给用户时间点击菜单项
      this.closeTimeout = setTimeout(() => {
        // this.selectedIndex = null;
        this.fenyeTag = null;
      }, 100); // 延迟300ms关闭
    },
    openBot(index) {
      if (this.selectedIndex === index) {
        clearTimeout(this.closeTimeout); // 清除关闭菜单的计时器
        // this.selectedIndex = index;
        this.clickCounter++;
        // console.log(parseInt(index + 1), "1231232");
        // 根据计数器的值设置 this.fenyeTag

        this.fenyeTag = "component" + index;
      }
    },
    cancelClose() {
      clearTimeout(this.closeTimeout); // 鼠标移入菜单时取消关闭
    },
    seedfw() {
      // if (this.modelname == "写实") {
      //   this.modelname = "虚化";
      //   this.sendToUE4("xieshi");
      // }
      // this.sendToUE4(this.lastClickedTitle);
      this.sendToUE4("ParkView");

      this.lastClickedTitle = "";
    },

    selectBot(index, value) {
      this.componentKey += 1;
      if (index == 0) {
        this.shouxl = true;
      } else {
        this.shouxl = false;
      }
      this.isdh = false;
      this.deviceId = "";
      // this.fllist = this.bflist;
      this.isWatching = false;
      if (this.modelname == "虚化") {
        this.modelname = "虚化";
        this.sendToUE4("xieshi");
      }
      // this.sendToUE4(this.lastClickedTitle);
      this.isshowfloorcd = true;
      this.sendToUE41("shebei", []);

      this.sendToUE4("ParkView");
      // this.sendToUE4("fuwei");

      // this.sendToUE4("漫游");.
      this.floorindex1 = 0;
      this.showfloor = false;
      this.opt = "";
      // this.lastClickedTitle = "";
      this.clickCounter++;
      if (index == 3) {
        this.setlou = true;
      } else {
        this.setlou = false;
      }

      // if (index == 1 || index == 3) {
      //   this.sendToUE4('fuwei');
      //   // this.fetchProjectSet(1, 'qfoWkYs0IL-mp1iJOOWGzw==', '', 0, '研发', '4F')
      // }
      console.log(value, 1261);
      this.seed(value);
      this.selectedIndex = index;
      this.openBot(index);
      console.log(parseInt(index + 1), "1231232");

      this.componentTag = "component" + parseInt(index + 1);
      (this.buildId = ""), (this.floorId = ""), (this.getname = "无");
      //操作菜单
      if (index == 9) {
        // this.seednengyuan(19);
      } else if (index == 2) {
        this.seedanquan(9, "close");
      } else if (index == 3) {
        this.seedyunwei(19);
      } else if (index == 1) {
        this.seedchild(9, "close");
      }

      this.isseed = false;
    },
    handleDataFromChild(data) {
      // 接收子组件传来的数据
      this.childData = data;
      console.log(data, "接受车位");
    },
    // 获取设备列表并生成新数据
    async getsblist() {
      try {
        // 先尝试从 localStorage 获取缓存数据
        const cachedData = localStorage.getItem("deviceListCache");
        if (cachedData) {
          console.log("设备列表从浏览器缓存获取");
          // 如果缓存存在，直接解析并使用它
          const parsedData = JSON.parse(cachedData);
          this.deviceListCache = new Map(parsedData);
        } else {
          console.log("缓存不存在，正在从接口获取数据");
          // 如果缓存不存在，发起请求获取设备数据
          const response = await axios.get(
            this.iframeUrl + "/api/base/api/deviceNameList"
          );
          const dataList = response.data.data;
          console.log("获取到设备列表:", dataList);

          // 将设备数据存入 Map 中
          const deviceMap = new Map();
          dataList.forEach((item) => {
            deviceMap.set(item.id, item.name); // 使用设备 id 作为键，设备 name 作为值
          });

          // 将 Map 转换为数组并存入 localStorage 中缓存数据
          localStorage.setItem(
            "deviceListCache",
            JSON.stringify(Array.from(deviceMap.entries()))
          );

          // 更新缓存
          this.deviceListCache = deviceMap;
        }
        console.log(this.deviceListCache, "deviceListCache");
      } catch (error) {
        console.error("获取设备列表失败:", error);
        throw error; // 重新抛出错误
      }
    },

    async fetchDataWithUeid(mergedRows) {
      console.log(mergedRows, "mergedRows");

      const promises = mergedRows.map(async (row) => {
        try {
          if (row.ueid) {
            const projectData = await this.fetchProjectSet(
              1,
              "_YShNh-ObjYY3Xv15seK0w==",
              0,
              "",
              "",
              row.ueid
            );
            console.log(projectData, 258);
            return { ...row, projectData };
          }

          return row;
        } catch (error) {
          console.error(`请求 ueid=${row.ueid} 失败:`, error);
          return row;
        }
      });

      return await Promise.all(promises);
    },
    async fetchProjectSet(type, projectId, parkId, buildId, floorId, deviceId) {
      console.log(
        this.getname,
        type,
        projectId,
        parkId,
        buildId,
        floorId,
        deviceId,
        "获取设备列表"
      );
      //从接口拿数据
      try {
        const response = await axios.get(
          "https://api-dh3d-public.3dzhanting.cn:8081/projectSet/all",
          {
            params: {
              type: type,
              projectId: projectId,
              deviceId: deviceId, // 如果不需要 deviceId，可以将其删除或保留为空字符串
              parkId: parkId,
              buildId: buildId,
              floorId: floorId,
              name: deviceId ? "" : this.getname,
              roomId: "",
            },
          }
        );
        if (deviceId) {
          console.log(deviceId, response.data.data[0], 859);

          let bjuedata = { ...response.data.data[0], severity: this.severity };
          console.log(bjuedata, "单个设备列表");
          return bjuedata;
        }
        if (
          this.getname == "照明" ||
          this.getname ==
          "半球型摄像机,枪型摄像机"
        ) {
          this.sblist = response.data.data.filter(
            (item) => item.pzjson !== null
          );
        } else {
          this.sblist = response.data.data;
        }
        this.sblist.forEach((item) => {
          console.log(item, this.deviceListCache, "设备列表");
          
          // 使用设备的deviceId去查找缓存中对应的name
          const deviceName = this.deviceListCache.get(parseInt(item.deviceId));
          console.log(item, "deviceName");
          // 如果找到了对应的设备名称，添加到title字段
          if (deviceName) {
            item.title = deviceName;
          } else {
            item.title =
              item.buildId.replace(/栋/g, "#") + item.floorId + item.deviceId; // 如果没有找到对应的name，则设置默认值
          }
        });

        console.log("更新后的设备列表:", this.sblist);
        if (this.getname == "车位") {
          const result = [];
          if (this.carstatuslist) {
            console.log(this.carstatuslist, "chewei");
            // 筛选出 filterData 中 dVal 为 "1" 的项
            this.carstatuslist.forEach((filterItem) => {
              if (filterItem.dVal == "1") {
                console.log(this.carstatuslist, "过滤完的数组");
                // 在第二个数组中找到对应的 id，将其推到新的数组中
                const matchingItem = this.sblist.find(
                  (item) => item.deviceId == filterItem.id.toString()
                );
                if (matchingItem) {
                  result.push(matchingItem);
                }
              }
            });
            // this.sblist=result
            console.log(result, "过滤完的数组");
            this.sendToUE41("chewei", result);
          }
        } else if (this.getname != "直梯") {
          this.sendToUE41("shebei", this.sblist);
        }

        console.log("Response data:", response.data);

        return response.data;
      } catch (error) {
        console.error("Error fetching project set:", error);
        throw error; // 重新抛出错误以便调用者处理
      }

      //从config拿数据
      // 将 name 字符串按逗号分隔为数组
      // const nameArray = this.getname.split(',').map(item => item.trim());
      // console.log(this.alldeviceList, nameArray, parkId, buildId, floorId, 'alldeviceList');
      // // 过滤设备列表，返回符合条件的设备
      // this.sblist = this.alldeviceList.filter(device => {
      //   const buildMatch = buildId == '' || device.buildId == buildId;
      //   const floorMatch = floorId == '' || device.floorId == floorId;
      //   const nameMatch = nameArray.includes(device.name);
      //   return device.parkId == parkId && buildMatch && floorMatch && nameMatch;
      // });

      // console.log('Response data:', this.sblist);
      // this.sendToUE41('shebei', this.sblist);
    },

    formatDate() {
      let now = new Date();
      let year = now.getFullYear();
      let month = (now.getMonth() + 1).toString().padStart(2, "0");
      let date = now.getDate().toString().padStart(2, "0");
      let hh = now.getHours().toString().padStart(2, "0");
      let mm = now.getMinutes().toString().padStart(2, "0");
      let ss = now.getSeconds().toString().padStart(2, "0");
      this.timeStr = `${year}-${month}-${date}  ${hh}:${mm}:${ss}`;
    },
    handleCollapse() {
      this.activeCollapse = this.activeCollapse === "1" ? "" : "1";
      console.log(this.alarmList, "alarmList");
      this.sendxs();
    },
    expandList() {
      this.activeCollapse = "1";
      if (this.closeTimer) {
        clearTimeout(this.closeTimer);
        this.closeTimer = null;
      }
      // this.sendxs();
    },
    startCloseTimer() {
      if (this.closeTimer) {
        clearTimeout(this.closeTimer);
      }
      this.closeTimer = setTimeout(() => {
        this.activeCollapse = "";
      }, 200);
    },
    cancelCloseTimer() {
      if (this.closeTimer) {
        clearTimeout(this.closeTimer);
        this.closeTimer = null;
      }
    },
    handleBuildingChange(value) {
      console.log('地块已切换，当前选择：', value);
      // 将当前选择的地块ID存入localStorage
      localStorage.setItem('curBuilding', value);

      // 增加componentKey值，触发组件刷新
      this.componentKey += 1;
      // // 如果需要，可以执行其他刷新逻辑
      // if (this.$refs.childComponent && typeof this.$refs.childComponent.refreshData === 'function') {
      //   this.$refs.childComponent.refreshData();
      // }
      const iframe1 = document.getElementById("myIframe1");
      const iframe = document.getElementById("myIframe");
      if (iframe) {
        iframe.contentWindow.postMessage(
          {
            type: "buildingChange",
            data: value,
          },
          "*"
        );
      }
      if (iframe1) {
        iframe1.contentWindow.postMessage(
          {
            type: "buildingChange",
            data: value,
          },
          "*"
        );
      }
    },
  },
  watch: {
    carstatuslist: {
      handler(newVal, oldVal) {
        // 如果开关关闭，则直接返回，不做处理
        console.log(newVal, this.sblist, "监听的chewei1`");
        if (this.isWatching) {
          return;
        }
        console.log(newVal, this.sblist, "监听的chewei2`");

        // 如果数据没有变化，则直接返回
        if (
          newVal === oldVal ||
          JSON.stringify(newVal) === JSON.stringify(oldVal)
        ) {
          return;
        }

        const result = [];
        console.log(newVal, this.sblist, "监听的chewei3");

        newVal.forEach((filterItem) => {
          if (filterItem.dVal == "1") {
            // 在第二个数组中找到对应的 id，将其推到新的数组中
            const matchingItem = this.sblist.find(
              (item) => item.deviceId == filterItem.id.toString()
            );
            if (matchingItem) {
              result.push(matchingItem);
            }
          }
        });

        console.log(result, "监听的chewei");
        // 仅在需要时更新 sblist，避免不必要的更新
        if (JSON.stringify(this.sblist) !== JSON.stringify(result)) {
          this.sblist = result;
        }

        // 这里可以传递更新后的数据
        this.sendToUE41("chewei", result);
      },
      immediate: false, // 避免初始化时立即调用
      deep: false, // 去掉 deep 避免监听数组内部变化
    },
  },
};
</script>

<style lang="less" scoped>
.item1 {
  // width: 336px;
  height: 340px;
  overflow: hidden;
  margin-bottom: 20px;
}

.right1 {
  height: 626px !important;
  width: 714px !important;
  overflow: hidden;
}

.right2 {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
  height: 884px !important;
  // width: 1356px !important;
  overflow: hidden;
}

.sbiframe {
  position: fixed;
  top: 14%;
  width: 62%;
  height: 71%;
  left: 19%;
  z-index: 9999;
  border-radius: 1%;
}

.close {
  position: fixed;
  z-index: 10000;
  top: 14.3%;
  right: 19.2%;
  cursor: pointer;
  width: 30px;
  height: 30px;
}

.ai {
  position: fixed;
  z-index: 10000;
  bottom: 2.3%;
  right: 18.2%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.ai-active {
  filter: drop-shadow(0 0 8px rgba(0, 255, 255, 0.8));
  transform: scale(1.1);
}

.switch-btn {
  cursor: pointer;
  width: 37.2px;
  height: 18.8px;
  position: relative;
  border: 1px solid #dfdfdf;
  background-color: #fdfdfd;
  box-shadow: #dfdfdf 0 0 0 0 inset;
  border-radius: 15px;
  background-clip: content-box;
  display: inline-block;
  -webkit-appearance: none;
  user-select: none;
  outline: none;
}

.switch-btn:before {
  content: "";
  width: 18px;
  height: 18px;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 20px;
  background-color: #fff;
  box-shadow: 0 0.0125rem 0.0375rem rgba(0, 0, 0, 0.4);
}

.switch-btn:checked {
  border-color: #56b0d4;
  box-shadow: #56b0d4 0 0 0 0.2rem inset;
  background-color: #56b0d4;
}

.switch-btn:checked:before {
  left: 18px;
}

.switch-btn.switch-btn-animbg {
  transition: background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:before {
  transition: left 0.3s;
}

.switch-btn.switch-btn-animbg:checked {
  box-shadow: #dfdfdf 0 0 0 0 inset;
  background-color: #56b0d4;
  transition: border-color 0.4s, background-color ease 0.4s;
}

.switch-btn.switch-btn-animbg:checked:before {
  transition: left 0.3s;
}

/* 动画的名称 */
.content-toggle-enter-active,
.content-toggle-leave-active {
  transition: all 0.3s ease;
  /* 动画时间与缓动效果 */
}

.content-toggle-enter-from,
.content-toggle-leave-to {
  opacity: 0;
  /* 初始和结束时的透明度 */
  transform: scale(0.9);
  /* 缩小状态 */
}

.content-toggle-enter-to,
.content-toggle-leave-from {
  opacity: 1;
  /* 完全显示 */
  transform: scale(1);
  /* 正常大小 */
}

.content1 {
  /* 设置滚动条的样式 */
  padding-bottom: 10px;
  // background: url("../assets/img/tqbg.png");
  background-color: #1f3151;
  background-size: 100% 100%;
  position: fixed;
  top: 52px;
  right: 24.4px;
  width: 282px;
  height: 255px;

  z-index: 99999999999999999;

  .xuanzeqi {
    height: 54px;
    display: flex;
    align-items: center;
    cursor: pointer;

    .pp {
      margin-left: 10px;
      margin-right: 10px;
      font-size: 20px;
      color: #fff;
    }
  }

  .tianjiandtime {
    cursor: pointer;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    text-align: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime2 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #fff;
        margin-top: 10px;
      }
    }
  }

  .tianjiandtime1 {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    opacity: 0.5;
    pointer-events: none;

    .tianqi {
      .img {
        width: 36px;
        height: 36px;
      }

      .img1 {
        width: 36px;
        height: 36px;
      }

      .time {
        color: #686868;
        margin-top: 10px;
      }
    }
  }
}

.red-text {
  color: red;
}

.boxt {
  position: fixed;
  bottom: 121px;
  width: 128px;
  z-index: 100;
  background: url("../assets/image/boxtapbj2.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  display: flex;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 18px;

  .tapli {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 2px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 17px;
    color: #e1f6ff;
    cursor: pointer;

    // 103px x 26px 宽高
    width: 103px;
    height: 26px;
    background: url("../assets/image/hangneiico.png");
    background-size: 100% 100%;
  }
}

.boxtap {
  left: 561px;
}

.boxtap1 {
  left: 645px;
}

.boxtap2 {
  left: 778px;
}

.boxtap3 {
  left: 913px;
}

.boxtap4 {
  left: 1041px;
}

.boxtap5 {
  left: 1039px;
}

.boxtap6 {
  left: 1221px;
}

.iframe {
  width: 100%;
  height: 100%;
  position: absolute;
  border: 0;
  z-index: 0;
}

.caidan {
  position: fixed;
  z-index: 12;
  top: 5.4%;
  left: 24.6%;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;

  .cdimg {
    position: absolute;
    top: 10px;
    z-index: 10;
  }

  .cd {
    position: absolute;
    top: 10px;
    z-index: 1;
    padding-top: 22.5px;
    border-radius: 5px;
    width: 100px;
    height: 100px;
    background: #1a284d;
    background-size: 100% 100%;
    pointer-events: none;
    display: flex;
    flex-direction: column;
    align-items: center;

    img {
      pointer-events: auto;
      margin-top: 10px;
    }
  }
}

.fuwei {
  position: fixed;
  z-index: 2;
  top: 69px;
  left: 424px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  img {
    width: 57px;
    height: 57px;
  }

  p {
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 18px;
    color: rgba(30, 227, 253, 0.64);
    background: linear-gradient(0deg,
        rgba(255, 255, 255, 0.98) 1.8798828125%,
        rgba(51, 156, 237, 0.64) 35.7177734375%);
    -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
  }
}

.container {
  // display: flex;
  //flex-wrap: wrap;
  // justify-content: space-between;
  // align-items: stretch;
  // height: 100%;
  // background: url('../assets/bgg.png');
  // background-size: 100% 100%;

  .bg {
    position: fixed;
    height: 1080px;
    width: 1920px;
    // background: url("../assets/image/all.png");
    background-size: 100% 100%;
    z-index: 1;
    pointer-events: none;
  }

  .bot {
    bottom: 20px;
    left: 645px;
    position: fixed;
    display: flex;
    flex-direction: row;
    justify-content: center;
    z-index: 2;

    .bot1 {
      // margin-left: 15px;
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .activeimg {
        margin-left: 22px;
        margin-right: 22px;
        width: 88px;
        height: 78px;
        // background: url('../assets/img/bot-a.png');
        background-size: 100% 100%;
        padding-top: 0.25rem;
        opacity: 1.85;

        img {
          width: 88px;
          height: 78px;
        }
      }

      .img {
        opacity: 0.85;
        margin-left: 22px;
        margin-right: 22px;
        width: 88px;
        height: 78px;
        // background: url('../assets/img/bot.png');
        background-size: 100% 100%;
        padding-top: 0.25rem;

        img {
          width: 88px;
          height: 78px;
        }
      }

      .p1 {
        margin-top: 11px;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 18px;
        color: #aee2e5;
        opacity: 0.55;
        background: linear-gradient(180deg,
            #455e7c 1.8798828125%,
            #455e7c 35.7177734375%);
        // border-radius: 5px;
      }

      .p2 {
        margin-top: 11px;
        font-family: DingTalk JinBuTi;
        font-weight: 400;
        font-size: 20px;
        color: #7dffd9;
        background: linear-gradient(180deg,
            #455e7c 1.8798828125%,
            #455e7c 35.7177734375%);
      }
    }
  }

  // /* 定义动画效果 */
  // .expand-enter-active,
  // .expand-leave-active {
  //   transition: height 0.3s ease;
  //   height: 255px;
  // }

  // .expand-enter,
  // .expand-leave-to

  // /* 初始和结束状态 */
  //   {
  //   height: 0;
  // }

  .select {
    text-align: center;
    z-index: 999;
    position: fixed;
    top: 8px;
    right: 210.864px;
    z-index: 2;

    .el-select {
      width: 142.8px;
      height: 26.9px;
      background: url("../assets/image/select.png");
      background-size: 100% 100%;
      font-family: Adobe Heiti Std;
      font-weight: normal;
      font-size: 13px;
      // color: #FEFEFE;
      text-align: center;

      cursor: pointer;
      line-height: 26px;
      z-index: 20;

      .sp {
        font-size: 15px;
        color: #e4f3ff;
        position: absolute;
        right: 10px;
        z-index: 999;
      }

      .pp {
        font-family: Adobe Heiti Std;
        font-weight: normal;
        font-size: 14px;
        color: #fefefe;
        // line-height: 113px;
      }
    }

    /* 定义动画 */

    .content::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .warnlist::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    .warnlist::-webkit-scrollbar {
      width: 4px;
      /* 设置滚动条的宽度 */
    }

    /* 设置滚动条滑块的样式 */
    .content::-webkit-scrollbar-thumb {
      background-color: #628091;
      /* 设置滚动条滑块的背景色 */
    }

    /* 鼠标悬停在滚动条上时的样式 */
    .content {
      overflow: hidden; // 隐藏过渡中的溢出内容
      /* 设置滚动条的样式 */
      padding-bottom: 10px;
      overflow-y: auto;
      background: rgb(19, 44, 75);
      background-size: 100% 100%;
      position: fixed;
      top: 40px;
      right: 210px;
      width: 145px;
      height: 255px;
      cursor: pointer;
      display: flex;
      flex-wrap: wrap;
      z-index: 999;
      border: 0.5px solid rgb(19, 44, 75);
      border-radius: 8px;

      .butn {
        background: url("../assets/image/lou.png");
        background-size: 100% 100%;
        width: 70px;
        height: 21px;
        text-align: center;
        line-height: 21px;
        font-size: 12px;
        font-family: HYQiHei;
        font-weight: normal;
        color: #ffffff;
        margin-left: 10px;
        margin-top: 11px;
        // margin-right: 80px;
      }

      .btnbtn {
        margin-top: 10px;
        float: left;

        .btn1:hover {
          // color: aqua;
          background-color: aqua;
          // background-color: rgb(119, 119, 119);
        }

        .btn1 {
          border: none;
          // float: left;
          margin-left: 11px;
          width: 51px;
          height: 18px;
          color: #ffffff;
          text-align: center;
          line-height: 18px;
          background: rgba(87, 174, 235, 0.18);
          font-size: 12px;
          border-radius: 2px;
        }
      }
    }

    img {
      position: absolute;
      right: 20px;
      top: 4.2px;
      cursor: pointer;
    }
  }

  .btt {
    z-index: 999;
    cursor: pointer;
    position: fixed;
    top: 8.8px;
    right: 24px;
    // width: 160px;
    height: 36px;
    display: flex;

    .btt1 {
      margin-left: 24px;
      flex: 1;
      align-items: center;
      justify-content: center;
      display: flex;
      flex-direction: column;
      background: transparent;
      border: none;
      cursor: pointer;

      img {
        width: 30px;
        height: 30px;
        margin-top: -15px;
        // width: 38.8px;
        // height: 38.8px;
      }

      // .imgg {
      //   width: 26px;
      //   height: 26px;
      // }

      p {
        margin-top: 5px;
        font-size: 16px;
        font-family: Source Han Sans CN;
        font-weight: 300;
        color: #6896b3;
      }
    }
  }

  .btt2 {
    z-index: 999;
    cursor: pointer;
    position: fixed;
    top: 8px;
    right: 370px;
    margin-left: 0px;
    width: 68px;
    height: 30px;
    background: url("../assets/image/baojing.png");
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding-right: 5px;

    .bjw {
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: 400;
      font-size: 18px;
      color: #bd2d2d;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }

  .ttqq {
    width: 26px;
    height: 26px;
  }

  .collapsed {
    height: 0 !important;
    // visibility: hidden;
    /* 当collapsed类被应用时，设置高度为0 */
  }

  // .bot {
  //   bottom: 0;
  //   left: 0.5%;
  //   position: fixed;
  //   pointer-events: none;
  //   z-index: 2;
  //   width: 99%;
  //   background: url("../assets/image/homebot.png");
  //   background-size: 100% 100%;
  //   height: 93.6px;
  //   transition: height 0.3s ease-out;
  //   /* 调整动画持续时间和缓动函数 */
  //   // overflow: hidden;

  //   /* 避免内容在动画过程中溢出 */
  //   .opt {
  //     width: 35px;
  //     height: 25px;
  //     position: fixed;
  //     bottom: 50px;
  //     left: 0;
  //     right: 0;
  //     margin: auto;
  //     cursor: pointer;
  //     pointer-events: auto;
  //     transition: transform 0.3s ease-out;
  //   }

  //   .opt-up {
  //     transform: translateY(0px);
  //     /* 当容器展开时，图标向上移动 */
  //   }

  //   .opt-down {
  //     transform: translateY(30px);
  //     /* 当容器收起时，图标向下移动 */
  //   }

  //   .bot1-container {
  //     margin-top: 40px;
  //     // margin-left: .25rem;

  //     // width: 100vw;
  //     // overflow-x: auto;
  //     display: flex;
  //     pointer-events: auto;

  //     // overflow: hidden;
  //     .bot1 {
  //       margin-left: 0.2px;
  //       margin-right: 13.8px;
  //       cursor: pointer;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         padding-left: 10px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         padding-left: 10px;
  //         // padding-top: 6px;
  //       }

  //       .icon {
  //         //  width: 23.4px;
  //         // height: 20.2px;
  //         margin-left: 5px;
  //         width: 19%;
  //         height: 55%;
  //       }

  //       .p1 {
  //         width: 94.2px;
  //         text-align: center;
  //         margin-left: -5.2px;

  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         width: 94.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }

  //     .bot2 {
  //       margin-left: 13.8px;
  //       margin-right: 13.8px;
  //       cursor: pointer;
  //       position: absolute;
  //       z-index: 20;
  //       top: -25%;
  //       left: 41.7%;
  //       right: 0;
  //       margin: auto;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         // padding-left: 10px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145.2px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         // padding-left: 10px;
  //         // padding-top: 6px;
  //       }

  //       .p1 {
  //         width: 145.2px;
  //         text-align: center;
  //         margin-left: 1.8px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         text-align: center;
  //         width: 145.2px;
  //         margin-left: 1.8px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }

  //     .bot3 {
  //       z-index: 20;
  //       margin-left: 13.8px;
  //       margin-right: 13.8px;
  //       cursor: pointer;
  //       position: absolute;
  //       top: -25%;
  //       left: 50.5%;
  //       right: 0;
  //       margin: auto;

  //       .activeimg {
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot-a.png");
  //         background-size: 100% 100%;
  //         padding-left: 18px;
  //         // padding-top: 5.2px;
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //       }

  //       .img {
  //         display: flex;
  //         flex-direction: row;
  //         align-items: center;
  //         display: flex;
  //         width: 145px;
  //         height: 38.4px;
  //         background: url("../assets/image/bot.png");
  //         background-size: 100% 100%;
  //         padding-left: 18px;
  //         // padding-top: 6px;
  //       }

  //       .p1 {
  //         width: 116.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #ffffff;
  //         opacity: 0.6;
  //         text-shadow: 0px 4px 27px #0072ff;
  //         background: linear-gradient(0deg,
  //             rgba(113, 200, 255, 0.91) 0%,
  //             rgba(255, 255, 255, 0.91) 100%);
  //         -webkit-background-clip: text;
  //         -webkit-text-fill-color: transparent;
  //       }

  //       .p2 {
  //         width: 116.2px;
  //         text-align: center;
  //         margin-left: -5.2px;
  //         font-size: 18px;
  //         font-family: DingTalk JinBuTi;
  //         font-weight: 400;
  //         color: #bfecec;
  //         text-shadow: 0px 0px 1px rgba(1, 17, 32, 0.35);
  //         background: linear-gradient(0deg, #b7ecff 0%, #ffffff 99.31640625%);
  //         -webkit-background-clip: text;
  //         // -webkit-text-fill-color: transparent;
  //       }
  //     }
  //   }
  // }

  .huangse {
    color: #ff831f;
  }

  .head {
    top: -1px;
    position: fixed;
    // pointer-events: none;
    z-index: 2;

    .img {
      overflow: hidden;
      width: 100%;
      height: 125px;
    }

    .title {
      background: url("../assets/image/head.png");
      background-size: 100% 100%;
      width: 1920px;
      height: 126px;
      text-align: center;
      font-family: FZZongYi-M05S;
      font-weight: 400;
      font-size: 37px;
      color: #ffffff;
      line-height: 72.2px;
      letter-spacing: 1.6px;
      // color: rgba(255, 249, 249, 1);
      text-shadow: 0px 3px 7px rgba(0, 0, 0, 0.35);
    }
  }

  .xiala {
    position: absolute;
    top: 12.96px;
    left: 380px;
    // height: 25px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 14px;
    color: #e4f3ff;
    display: flex;
    z-index: 2;

    .xiala-item {
      display: flex;
      // align-items: center;

      p {
        margin-right: 8px;
      }

      .el-select {
        width: 120px;
      }
    }
  }

  .now-time {
    position: absolute;
    top: 7.96px;
    left: 10px;
    // height: 25px;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 19px;
    color: #e4f3ff;
    display: flex;
    z-index: 2;

    img {
      width: 144px;
      height: 31px;
      margin-top: 0px;
    }

    span {
      margin-left: 42px;
    }
  }

  .chart {
    margin-top: 24px;
    margin-bottom: 24px;
  }
}

.caidanlist {
  position: fixed;
  top: 6.3%;
  right: 22.1%;
  z-index: 10;

  .caidanimg1 {
    background: url("../assets/image/caidanimg1.png");
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;

    .caidanicon1 {
      width: 14px;
      height: 16px;
    }

    .caifonsiez {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #ffffff;
    }

    .caixiangxia {
      width: 12px;
      height: 7px;
      cursor: pointer;
    }
  }

  .list {
    // 108px x 30px
    width: 111px;
    height: 29px;
    background-color: rgba(113, 155, 224, 0.2);

    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-top: 1px solid #719bdf;
  }

  .list:hover {
    background-color: rgba(113, 155, 224, 0.5);
    background-repeat: no-repeat;
    width: 111px;
    height: 29px;
    background-size: 100% 100%;
  }

  //
}

.active {
  background-color: rgba(113, 155, 224, 0.7) !important;
}

.xuhua {
  background: url("../assets/image/floor1.png");
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
  position: fixed;
  top: 294px;
  left: 370px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 26.3px;
  text-align: center;
  cursor: pointer;
}

.floorcd {
  cursor: pointer;
  // z-index: 999;
  position: fixed;
  top: 320px;
  left: 369.864px;

  .return {
    position: absolute;
    top: -200px;
    left: 10px;
    width: 34px;
    height: 35px;
  }

  .flist {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url("../assets/image/floor1.png");
    background-size: 100% 100%;
    width: 87.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 13px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }
}

.flist1 {
  margin-top: 2px;
  margin-bottom: 2px;
  background: url("../assets/image/floor.png");
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 15px;
  color: #7dffd9;
  line-height: 26.3px;
  text-align: center;
}

.flist:hover {
  background: url("../assets/image/floor.png");
  background-size: 100% 100%;
  width: 87.5px;
  height: 26.3px;
}

.tit {
  position: absolute;
  top: -26px;
  left: 7px;
  width: 95px;
  height: 45px;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 23px;
  color: #ffffff;
  // line-height: 21px;
  background: linear-gradient(0deg, #ffffff 0%, #0e96fa 98.6328125%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
}

.floorcd1 {
  cursor: pointer;
  // z-index: 88;
  position: fixed;
  top: 322px;
  left: 452.864px;

  .tit {
    line-height: 21px;
    position: absolute;
    top: -26px;
    left: -13px;
    width: 102px;
    height: 45px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 23px;
    color: #ffffff;
    // line-height: 52px;
    // z-index: 1;
    background: linear-gradient(0deg, #ffffff 0%, #0e96fa 98.6328125%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
  }

  .return {
    position: absolute;
    top: -244px;
    left: 11px;
    width: 34px;
    height: 35px;
  }

  .flist {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url("../assets/image/floor1.png");
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 26.3px;
    text-align: center;
  }

  .flist1 {
    margin-top: 2px;
    margin-bottom: 2px;
    background: url("../assets/image/floor.png");
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 15px;
    color: #7dffd9;
    line-height: 26.3px;
    text-align: center;
  }

  .flist:hover {
    background: url("../assets/image/floor.png");
    background: url("../assets/image/floor.png");
    background-size: 100% 100%;
    width: 79.5px;
    height: 26.3px;
  }
}

.groups {
  cursor: pointer;
  z-index: 999;
  position: fixed;
  top: 43px;
  right: 423.864px;

  .subitem {
    margin-left: 3px;
    margin-right: -12px;
  }
}

/deep/.el-sub-menu .el-sub-menu__icon-arrow {
  margin-right: -12px !important;
}

/deep/.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-menu-item-group__title,
.el-menu--vertical:not(.el-menu--collapse):not(.el-menu--popup-container) .el-sub-menu__title {
  padding-left: 27px !important;
}

/deep/ .el-menu-item-group__title {
  display: none !important;
}

/deep/ .el-sub-menu__title {
  background: url("../assets/image/caidanimg1.png");
  background-size: 100% 100%;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
  height: 32px;
}

/deep/ .el-menu-item {
  height: 32px !important;
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #ccc;
}

/deep/ .el-menu {
  border: none;
}

.loading_page {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  display: flex;
  flex-direction: column;
  /* Stack items vertically */
  justify-content: center;
  /* Center items vertically */
  align-items: center;
  /* Center items horizontally */

  background-color: rgb(33, 33, 33);
  margin: 0;

  .inner-box {
    margin-left: 65px;
    position: relative;
    width: 72px;
    height: 72px;
    transform-style: preserve-3d;
    transform-origin: center;
    animation: 3s ctn infinite;
    transform-origin: 0 0;
    transform: rotateX(-30deg) rotateY(45deg) translate(0, 0);
  }

  .inner {
    position: absolute;
    width: 72px;
    height: 72px;
    text-align: center;
    line-height: 72px;
    color: #fff;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transform-origin: center;
  }

  .inner:nth-child(1) {
    transform: rotateX(90deg) translateZ(36px);
    animation: 3s top infinite;
  }

  .inner:nth-child(2) {
    transform: rotateX(-90deg) translateZ(36px);
    animation: 3s bottom infinite;
  }

  .inner:nth-child(3) {
    transform: rotateY(90deg) translateZ(36px);
    animation: 3s left infinite;
  }

  .inner:nth-child(4) {
    transform: rotateY(-90deg) translateZ(36px);
    animation: 3s right infinite;
  }

  .inner:nth-child(5) {
    transform: translateZ(36px);
    animation: 3s front infinite;
  }

  .inner:nth-child(6) {
    transform: rotateY(180deg) translateZ(36px);
    animation: 3s back infinite;
  }

  @keyframes ctn {
    from {
      transform: rotateX(-35deg) rotateY(45deg) translate(-50%, -50%);
    }

    50% {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }

    to {
      transform: rotateX(-35deg) rotateY(405deg) translate(-50%, -50%);
    }
  }

  @keyframes top {
    from {
      transform: rotateX(90deg) translateZ(36px);
    }

    50% {
      transform: rotateX(90deg) translateZ(36px);
    }

    75% {
      transform: rotateX(90deg) translateZ(72px);
    }

    to {
      transform: rotateX(90deg) translateZ(36px);
    }
  }

  @keyframes bottom {
    from {
      transform: rotateX(-90deg) translateZ(36px);
    }

    50% {
      transform: rotateX(-90deg) translateZ(36px);
    }

    75% {
      transform: rotateX(-90deg) translateZ(72px);
    }

    to {
      transform: rotateX(-90deg) translateZ(36px);
    }
  }

  @keyframes left {
    from {
      transform: rotateY(90deg) translateZ(36px);
    }

    50% {
      transform: rotateY(90deg) translateZ(36px);
    }

    75% {
      transform: rotateY(90deg) translateZ(72px);
    }

    to {
      transform: rotateY(90deg) translateZ(36px);
    }
  }

  @keyframes right {
    from {
      transform: rotateY(-90deg) translateZ(36px);
    }

    50% {
      transform: rotateY(-90deg) translateZ(36px);
    }

    75% {
      transform: rotateY(-90deg) translateZ(72px);
    }

    to {
      transform: rotateY(-90deg) translateZ(36px);
    }
  }

  @keyframes front {
    from {
      transform: translateZ(36px);
    }

    50% {
      transform: translateZ(36px);
    }

    75% {
      transform: translateZ(72px);
    }

    to {
      transform: translateZ(36px);
    }
  }

  @keyframes back {
    from {
      transform: rotateY(180deg) translateZ(36px);
    }

    50% {
      transform: rotateY(180deg) translateZ(36px);
    }

    75% {
      transform: rotateY(180deg) translateZ(72px);
    }

    to {
      transform: rotateY(180deg) translateZ(36px);
    }
  }

  .loading-text {
    z-index: 9999;
    color: #fff;
    /* Text color */
    margin-top: 35px;
    /* Space between the cube and text */
    font-size: 18px;
    /* Text size */
    letter-spacing: 1px;
    /* Letter spacing */
    text-align: center;
  }

  /* Reset default margin */
}

.expand-leave-active {
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-duration: 0.8s;
  animation-fill-mode: forwards;
}

.expand-enter-active {
  animation-name: expandIn;
}

.expand-leave-active {
  animation-name: shrinkAndFade;
}

@keyframes expandIn {
  0% {
    transform: scale(0.4);
    opacity: 0;
    transform-origin: center;
  }

  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shrinkAndFade {
  0% {
    transform: scale(1);
    opacity: 1;
    transform-origin: center;
  }

  100% {
    transform: scale(0.4);
    opacity: 0;
  }
}

.bjlist {
  position: fixed;
  top: 39px;
  right: 365px;
  width: 306px;
  height: 308px;
  z-index: 999;
  transition: opacity 0.2s ease, transform 0.2s ease;
  transform-origin: top right;

  .alarm-list {
    background: #14253a;
    border-radius: 8px 8px 8px 8px;
    border: 1px solid #326f94;
    height: 300px;
    overflow-y: auto;
    padding-top: 5px;
  }

  /* 设置滚动条的样式 */
  .alarm-list::-webkit-scrollbar {
    width: 5px;
    /* 设置滚动条的宽度 */
  }

  /* 设置滚动条轨道的样式 */
  // .alarm-list::-webkit-scrollbar-track {
  //   background-color: #f1f1f1; /* 设置滚动条轨道的背景色 */
  // }

  /* 设置滚动条滑块的样式 */
  .alarm-list::-webkit-scrollbar-thumb {
    background-color: #9da5b3;
    /* 设置滚动条滑块的背景色 */
  }

  .alarm-item {
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(27, 74, 107, 0.5);

    &:last-child {
      border-bottom: none;
    }
  }

  .alarm-content {
    flex: 1;
  }

  .alarm-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
  }

  .alarm-name {
    color: #ffffff;
    font-size: 14px;
  }

  .alarm-level1 {
    width: 34px;
    height: 18px;
    background: rgba(236, 58, 58, 0.2);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #ec3a3a;
    line-height: 18px;
    text-align: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 12px;
    color: #ec3a3a;
    line-height: 14px;
    font-style: normal;
    text-transform: none;
  }

  .alarm-level2 {
    width: 34px;
    height: 18px;
    background: rgba(255, 134, 73, 0.2);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #ff8649;
    line-height: 18px;
    text-align: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 12px;
    color: #ff8649;
    line-height: 14px;
    font-style: normal;
    text-transform: none;
  }

  .alarm-level3 {
    width: 34px;
    height: 18px;
    background: rgba(255, 179, 26, 0.2);
    border-radius: 2px 2px 2px 2px;
    border: 1px solid #ffb31a;
    line-height: 18px;
    text-align: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 12px;
    color: #ffb31a;
    line-height: 14px;
    font-style: normal;
    text-transform: none;
  }

  .alarm-time {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
  }

  .alarm-detail {
    margin-left: 10px;
  }

  .detail-text {
    color: rgba(58, 197, 242, 0.9);
    font-size: 12px;
    cursor: pointer;
  }
}

/deep/ .el-collapse {
  border: none;
  background: transparent;
}

/deep/ .el-collapse-item__header {
  display: none;
}

/deep/ .el-collapse-item__wrap {
  background: transparent;
  border: none;
}

/deep/ .el-collapse-item__content {
  padding: 0;
  color: #ffffff;
}

.voice-container {
  width: 100px;
  height: 100px;
  position: fixed;
  right: 21%;
  top: 88%;
  z-index: 18898;



  // transform: translate(-50%, -50%);
}

.alll {
  width: 100%;
  height: 100%;
  // background: url('../assets/imge/w5 (6).png') no-repeat center center;
  // background-size: 100% 100%;
}
</style>
