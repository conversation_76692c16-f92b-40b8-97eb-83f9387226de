<template>
  <div>

    <img class="ss" src="../../assets/ss.png" @click="togglePanels(isshousuo)" alt="">
    <!-- <div v-if="!animateLeft" class="zhankai" @click="togglePanels(1)"> 》</div>
    <div v-if="animateLeft" class="shousuo" @click="togglePanels(0)">《</div> -->
    <div v-if="showLeftPanel || animateLeft" class="left-panel"
      :class="{ 'animate-left': animateLeft, 'animate-left-out': !animateLeft, 'isleftbg': showLeftbg }">
      <slot name="left"></slot>
    </div>
    <div v-if="showRightPanel || animateRight" class="right-panel"
      :class="{ 'animate-right': animateRight, 'animate-right-out': !animateRight, 'isrightbg': showRightbg }">
      <slot name="right"></slot>
    </div>
  </div>
</template>


<script>
export default {
  name: 'SlidingPanel',
  props: ['showLeftPanel', 'showRightPanel', 'showRightbg', 'showLeftbg'],
  data() {
    return {
      animateLeft: false,
      animateRight: false,
      isClosing: false,
      isshousuo: false,
    };
  },
  mounted() {
    // 在组件加载时，启用动画
    this.$nextTick(() => {
      setTimeout(() => {
        this.animateLeft = this.showLeftPanel;
        this.animateRight = this.showRightPanel;
      }, 1000);

    });
  },
  methods: {
    togglePanels(is) {
      this.isshousuo = !this.isshousuo
      // 开始关闭动画
      this.animateLeft = is;
      this.animateRight = is;

      // 延迟一段时间后再关闭面板
      setTimeout(() => {
        this.$emit('update:showLeftPanel', is);
        this.$emit('update:showRightPanel', is);
      }, 1000); // 1秒的动画时长与CSS中的匹配
    }
  }
};

</script>

<style scoped>
.ss {
  width: 28px;
  height: 28px;
  position: fixed;
  z-index: 22;
  top: 9.5px;
  right: 236px;
  cursor: pointer;
}

.shousuo {
  position: fixed;
  z-index: 2;
  font-size: 32px;
  /* width: 336px; */

  cursor: pointer;
  color: aqua;
}

.zhankai {
  position: fixed;
  z-index: 2;
  font-size: 32px;
  /* width: 336px; */
  top: 525px;
  left: 15px;
  cursor: pointer;
  color: aqua;
}

.left-panel {
  overflow: hidden;
  position: fixed;
  z-index: 1;
  width: 336px;
  top: 70px;
  left: 15px;
  /* height: 990px; */
  /* background: url("../../assets/image/left.png") no-repeat center center; */
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  /* backdrop-filter: blur(4px); */
  transform: translateX(-100%);

}

.isleftbg {
  /* background: linear-gradient(90deg, #0b274a -100%, rgba(11, 39, 74, .6) 78%, rgba(11, 39, 74, 0) 110%); */
  background: #0B274AE0;
  border-radius: 8px;
}

.isrightbg {
  background: url("../../assets/image/right.png") no-repeat center center;

}

.right-panel {
  overflow: hidden;
  position: fixed;
  z-index: 1;
  right: 15px;
  /* width: 336px; */
  top: 70px;
  /* height: 990px; */
  /* background: url("../../assets/image/right.png") no-repeat center center; */
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  /* justify-content: space-around; */
  /* backdrop-filter: blur(4px); */
  transform: translateX(100%);
}

/* 动画样式 */
/* 动画样式 */
@keyframes slide-left {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slide-left-out {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-105%);
  }
}

@keyframes slide-right {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(0);
  }
}

@keyframes slide-right-out {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(105%);
  }
}

/* 应用动画 */
.animate-left {
  animation: slide-left 1s ease forwards;
}

.animate-left-out {
  animation: slide-left-out 1s ease forwards;
}

.animate-right {
  animation: slide-right 1s ease forwards;
}

.animate-right-out {
  animation: slide-right-out 1s ease forwards;
}
</style>
