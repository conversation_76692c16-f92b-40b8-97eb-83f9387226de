<template>
  <div class="right-card-box">
    <div class="right-card-title" v-text="title"></div>
    <div class="select_form pull-right">
      <el-select class="co2_select" v-model="carbonLineValue" placeholder="请选择" size="small" @change="handleCarbonLine">
        <el-option v-for="item in carbonLineSelectOption" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>

    <div class="page_nav2 flex mb10">
      <span class="flex-1">实现碳中和缺口</span>
      <span><b style="margin-padding: 30px;" v-text="parseInt(carbonTotal / 1000)"></b> tco2</span>
    </div>
    <div class="">
      <BaseChart :optionData="carbonLineOption" :height="height" />
    </div>

    <div class="co2_carbon_results" style="display: none;">
      <div class="page_nav2">
        <img src="@/assets/image/co2_icon_1.png" /> 等效植树
        <span><b v-text="reduceTree"></b>棵</span>
      </div>
      <p class="clearfix"></p>
      <div class="co2_progress" v-for="(item, index) in carbonResults" :key="index">
        <div class="text_cont">
          <span v-text="item.name"></span>
          <i>{{ item.value || 0 }} <b v-text="item.unit"></b></i>
        </div>
        <div class="progress_bar">
          <span :class="`progress_fill_${index}`" :style="{ width: `${item.percent}%` }"></span>
        </div>
      </div>

    </div>
  </div>


</template>

<script type="text/javascript">
import {
  buildingEnergyDataList,
  buildingEnergyAvgDataList,
} from "@/api/energy/apis";

import BaseChart from "@/views/ScreenCommBase/components/common/BaseChart.vue";

import {
  carbonLineOption,
} from "@/views/components/cO2View/components/common/co2Charts"

export default {
  props: {
    "title": {
      type: String,
      default: "碳排放分析预测",
    },
    "height": {
      type: String,
      default: "215px",
    },
  },
  components: {
    BaseChart,
  },
  data() {
    return {
      loading: false,
      curBuilding: this.gf.getCurBuilding(),

      carbonElectricityRate: 0.5581, // 电占碳比例
      // 一度电 = 0.551 kg Co2   注：碳排放度电系数，火电 0.82，平均 0.551
      electricityCarbonRate: 0.86, // 电碳系数， 1度电 = 0.86kg
      // 碳排放分析选项
      carbonRates: [
        0.95, 0.95, 0.95,
        0.90, 0.90, 0.85,
        0.80, 0.85, 0.90,
        0.90, 0.95, 0.95
      ], // 12个月，目标值比例

      electricityTotal: 0,
      carbonTotal: 0,
      reduceTree: 0,    // 等效植树

      carbonResults: [],

      // 碳排放预测
      carbonLineValue: '年度累计',
      carbonLineSelectOption: [
        {
          label: '月度累计',
          value: '月度累计',
          display: 'month',
          from: this.$moment().startOf('year').format("YYYY-MM-DD"),
          to: this.$moment().format("YYYY-MM-DD"),
        },
        {
          label: '年度累计',
          value: '年度累计',
          display: 'year',
          from: this.$moment().add(-2, "year").startOf('year').format("YYYY-MM-DD"),
          to: this.$moment().add(3, "year").format("YYYY-MM-DD"),
        },
      ],
      // 年度用电数据折线图数据
      carbonLineOption: {},

      // 能耗概览
      energyRankingValue: '年度累计',
      // 能耗概览选项
      energyRankingSelectOption: [],
      energyRanking: {
        "区域用电": {
          total: 0,
          unit: "kWh",
          data: [],
        },
        "区域用水": {
          total: 0,
          unit: "t",
          data: [],
        },
        "分项用电": {
          total: 0,
          unit: "kWh",
          data: [],
        },
        "光伏发电": {
          total: 0,
          unit: "kWh",
          data: [],
        }
      }, // 用能统计排行
      settings: this.gf.projectSettings(),
    }
  },
  computed: {
  },
  created() {
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: "electricity",
        type: "electricity",
        displayType: "year",
        from: this.$moment().startOf("year").format("YYYY-MM-DD"),
        to: this.$moment().format("YYYY-MM-DD"),
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        res.data.datas.map(d => {
          let electricityTotal = parseInt(d.totalVal);
          // 总碳排量
          this.carbonTotal = electricityTotal * (this.settings.co2ElectricityRate || 1);
        });
      }).then(() => {
        this.carbonAnalysisPrediction();
      });
    },
    // 右边下面 碳排放分析预测 实际，目标，预测
    handleCarbonLine() {
      this.carbonAnalysisPrediction();
    },
    // 碳排放分析预测 实际，目标，预测
    carbonAnalysisPrediction() {
      let carbonLineData = {
        // xAxis: ['12-14', '12-15', '12-16', '12-17', '12-18', '12-19', '12-20', '12-21', '12-22', '12-23', '12-24'],
        data: [
          {
            name: '实际',
            // data: [200, 210, 220, 150, 255, 188, 280, 280, 255, 210, 210],
            data: []
          },
          {
            name: '目标',
            data: []
          },
          {
            name: '预测',
            data: []
          },
        ],
        xAxis: [],
      };
      // 实际值 + 预测值
      let curSelect = this.carbonLineSelectOption.filter(d => {
        return d.value == this.carbonLineValue;
      }).pop();

      if (curSelect.display == "month") {
        carbonLineData.xAxis = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"];
      } else if (curSelect.display == "year") {
        for (let i = this.$moment(curSelect.from).format("YYYY"); i < this.$moment(curSelect.to).format("YYYY"); i++) {
          carbonLineData.xAxis.push(i);
        }
      }
      buildingEnergyDataList({
        buildingId: this.curBuilding.id,
        deviceType: "electricity",
        type: "electricity",
        displayType: curSelect.display,
        from: curSelect.from,
        to: curSelect.to,
        year: this.$moment().format("YYYY"),
        compareYear: this.$moment().format("YYYY"),
      }).then(res => {
        let total = 0;
        // 实际值
        res.data.datas.map((d, i) => {
          // carbonLineData.xAxis.push(d.recordedAt);
          let actualVal = d.totalVal / this.carbonElectricityRate * this.electricityCarbonRate;
          total += actualVal;
          carbonLineData.data[0].data.push([d.recordedAt, actualVal.toFixed(2)]);
        });
        // 目标值
        let avgValue = total / res.data.datas.length;
        res.data.datas.map((d, i) => {
          let targetVal = (avgValue * this.carbonRates[i]).toFixed(2);
          carbonLineData.data[1].data.push([d.recordedAt, targetVal]); // 目标值
        });
      }).then(() => {
        buildingEnergyAvgDataList({
          buildingId: this.curBuilding.id,
          deviceType: "electricity",
          type: "electricity",
          displayType: curSelect.display,
          from: curSelect.from,
          to: curSelect.to,
          year: this.$moment().format("YYYY"),
          compareYear: this.$moment().format("YYYY"),
        }).then(res => {
          res.data.datas.map((d, i) => {
            let actualVal = d.totalVal / this.carbonElectricityRate * this.electricityCarbonRate;
            carbonLineData.data[2].data.push([d.recordedAt, actualVal.toFixed(2)]); // 预测值
          })
        }).then(() => {
          this.carbonLineOption = carbonLineOption(carbonLineData);
        })
      });

    },
  }
}
</script>

<style lang="scss" scoped>
@import "~@/assets/styles/bimCard.scss";

.pull-right {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
  position: absolute;
  top: 10px;
  left: 221px;
  z-index: 1200;
}

::v-deep .el-input {
  width: 100px !important;
}
</style>
