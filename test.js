
/* 天津大学，定制开发
  解决预约的信息(卡号和门禁号)，可以在规定时间内，打开对应的门禁

  服务启动:  node src/dataSyncTjdx_menjin.js [port] [db] [debug]
  db 目前没用
  访问接口获取当前数据：
    1. 所有门禁记录 http://localhost:[port]/doorList  
    2. 3日内预约记录 http://localhost:[port]/reservationList
    3. 预约缓存(可判定执行开门) http://localhost:[port]/reservationCache
	4. 查询用户信息 http://localhost:[port]/cardInfo?userId=XXX

 */

    const schedule = require('node-schedule');
    const http = require('http');
    const util = require('util');
    const EventEmitter = require('events');
    const crypto = require('crypto');
    const moment = require('moment');
    const https = require('https');
    const request = require('request');
    const querystring = require('querystring');
    const axios = require('axios');
    const Stomp = require('stompjs');
    const SockJS = require('sockjs-client-node');
    
    const helper = require('../src/helper');
    
    const sysConfig = require('../src/conf/sysConfig').sysConfig();
    const mjConfig = require('../src/conf/config_tjdx_menjin').config()
    const port = process.argv[2] > 0 ? process.argv[2] : sysConfig.localServer.port;
    const dbName = process.argv[3] && process.argv[3] != "debug" ? process.argv[3] : null;
    global.isDebug = process.argv[4] == "debug" ? true : false;
    
    helper.debug("DOOR_PAIRS", mjConfig.DOOR_MAPPING_RULES);
    
    let doorList = [];
    let reservationList = [];
    let reservationCache = {};
    let doorMsgList = [];  // 最近50条刷卡消息
    
    // 参数替换 覆盖之前的配置
    if(dbName) {
        sysConfig.mysql.database = dbName;
    }
    if(port > 0) {
      sysConfig.localServer.port = port;
    }
    
    function AppServer(options) {
      this.options = options;
      this.cacheData = {};
      this._createServer();
    }
    util.inherits(AppServer, EventEmitter);
    
    let _success = {
        code: 0,
        status: 200, // -- 针对余姚电力 拍照水表，需要返回【status:200】表示接收成功
        msg: "",
        result: [],
    };
    
    AppServer.prototype._createServer = function() {
      this.server = http.createServer(function(req,res){
        if(req.method == "GET") {
          // get 参数
          var queryObj = {};
          try {
            var query = url.parse(req.url).query;
            var queryObj = qs.parse(query);
          } catch(e) {
    
            // pass
          }
          helper.debug("get: ", JSON.stringify(queryObj));
          app._handleGetReq(req, res, queryObj);
        } else if(req.method == "POST") {
          var body = {};
          req.setEncoding('utf8');
          req.on('data', function (chunk) {
              body += chunk;
          });
          req.on('end', function () {
            // post 参数
            try {
              // 去掉body前面的字符串
              if(body.startsWith("[object Object]")) {
                body = body.replace("[object Object]", "");
              }
              // 尝试解析 Content-Type：application/json 数据
              if(typeof body == "string") {
                try{
                  body = JSON.parse(body);
                } catch(e) {
                  // pass
                }
              }
              // 尝试解析 Content-Type：application/x-www-form-urlencoded 数据
              if(typeof body == "string") {
                try{
                  body = qs.parse(body);
                  body = JSON.parse(JSON.stringify(body));
                } catch(e) {
                  // pass
                }
              }
            } catch (e) {
              // pass
              helper.log(e.message);
            }
            helper.debug("post: ", JSON.stringify(body));
            app._handlePostReq(req, res, body);
          });
        }
      });
      this.server.listen(this.options.localServer.port, this.options.localServer.host, () => {
          helper.log('服务器启动成功，可以访问: ' + this.options.localServer.host + ':' + this.options.localServer.port);
      });
    }
    
    AppServer.prototype._handleGetReq = async function(req, res, queryObj) {
      let result = JSON.parse(JSON.stringify(_success));
      if(req.url.indexOf('/update') == 0) {
        // 刷新能耗数据
        res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
        result.result = await syncData();
        res.write(JSON.stringify(result));
        res.end();
      } else if(req.url.indexOf('/reservationList') == 0) {
        res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
        result.result = reservationList;
        res.write(JSON.stringify(result));
        res.end();
      } else if(req.url.indexOf('/doorList') == 0) {
        res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
        result.result = doorList;
        res.write(JSON.stringify(result));
        res.end();
      } else if(req.url.indexOf('/reservationCache') == 0) {
        res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
        result.result = reservationCache;
        res.write(JSON.stringify(result));
        res.end();
      } else if(req.url.indexOf('/cardInfo') == 0) {
        res.writeHead(200,{"Content-Type":"application/json;charset=utf-8"});
        result.result = await getCardNo2(queryObj.userId);
        res.write(JSON.stringify(result));
        res.end();
      } else {
        res.writeHead(404, { 'Content-Type': 'text/plain', 'Access-Control-Allow-Methods': 'GET,POST', 'Access-Control-Allow-Origin': '*' });
        res.end('404 not found');
      }
    }
    
    AppServer.prototype._handlePostReq = function(req, res, data) {
      let result = JSON.parse(JSON.stringify(_success));
      res.writeHead(403, { 'Content-Type': 'text/plain', 'Access-Control-Allow-Methods': 'GET,POST', 'Access-Control-Allow-Origin': '*' });
      res.end('403 FORBIDDEN');
    }
    
    
    
    // 公共 call api 函数
    function callApi(requestUrl, params, timeout) {
      return new Promise(function(resolve,reject){
        let query = {
          url: requestUrl,
          method: params.method.toUpperCase(),
          headers: {
            ...params.headers
          },
          timeout: timeout || 10*1000,
        };
        if(params.hasOwnProperty("data") && params.data) {
          query.body = JSON.stringify(params.data);
        }
        if(params.hasOwnProperty("form") && params.form) {
          query.form = params.form;
        }
        let t = moment().unix();
        helper.debug("callApi", t, requestUrl, "\nparams", params, "\nquery", query);
        request(query, function(error, response, body) {
          if(error) {
              helper.log("callApi error", error);
              reject(error);
          }
          // 成功后执行
          helper.debug("callApi", t, "response body", body);
          resolve(body);
        });
      });
    }
    
  
    
    // 同步数据
    async function syncData() {
      // 1. 获取最新门禁设备列表信息
      doorList = await getDoorList();
    
      // 2. 获取当天预约信息
      reservationList = await getReservationList();
    
      // 3. 遍历预约信息，获取一卡通卡号，缓存到记录
      reservationCache = {}; // 清空旧数据
      if(reservationList && reservationList.length > 0) {
        for(let i=0; i<reservationList.length; i++) {
          let re = reservationList[i];
          try {
            let cardNo = await getCardNo(re.user_ref_no);
            if(cardNo) {
              // 分割equipment_location2为数组
              let locations = re.equipment_location2.split(',');
              
              for(let j=0; j<locations.length; j++) {
                let location = locations[j];
                let door = getDevice(doorList, location.trim());
                if(door) {
                  let key = door.deviceId + "_" + cardNo;
                  reservationCache[key] = reservationCache[key] || [];
                  reservationCache[key].push(re);
                }
              }
            }
          } catch(e) {
            helper.debug("getDevice and getCardNo error", re, e.message);
          }
        }
      }
    
      // helper.debug("--> doorList", doorList);
      // helper.debug("--> reservationList", reservationList);
      helper.debug("--> reservationCache", reservationCache);
    
      return "success";
    }
    
    async function getDoorList() {
      let res = await callApi("http://localhost:7203/v1/door-service/door/selectPageList", {
        method: "post",
        headers: {
          "Authorization": "123456",
          "Content-Type": "application/json",
        },
        data: {
          "pageNo": 1,
          "pageSize": 500,
          "item": {
            "groupNo": "",
            "no": ""
          }
        }
      }, 10000);
      // let res = JSON.stringify(doorRes);
      if(res) {
        try {
          let {data} = JSON.parse(res);
          let list = data.list//.filter( d => { return d.enable })
            .map( d => {
            // helper.debug(d.name, d.location, d.enable, d.config.inReader, d.config.outReader, d.config.doorNo, d.deviceId);
            return {
              deviceId: d.deviceId,
              name: d.name,
              location: d.location,
              enable: d.enable,
              inReader: d.config.inReader,
              outReader: d.config.outReader,
              doorNo: d.config.doorNo,
            }
          });
          return list;
        } catch(e) {
          helper.debug("e", e);
        }
      }
      return [];
    }
    
    async function getReservationList() {
      // 预约系统
      let res = await callApi("http://yiqi.tju.edu.cn/lims/api", {
        method: "post",
        headers: {
          "clientid": "5a298e93-158d-4e22-83cf-6ceb62e9b4f1",
          "clientsecret": "2c8ec39e-9887-482a-b28b-e64c496b601c"
        },
        data: {
          "method": "gpui/eq_reserv/reservList",
          "params": {
              "dtstart": moment().subtract(1, 'hour').unix(),
              "dtend": moment().add(1,'day').unix(),
              "params": {
                  "location": "58",
                  "limit": [
                      0,
                      10000
                  ]
              }
          }
       }
      }, 10000);
    
      if(res) {
        try {
          let {response} = JSON.parse(res);
          let list = response.map( d=> {
            // 在原始开始时间前加半小时，结束时间后加半小时
            const bufferMinutes = 30 * 60; // 1800 秒
            const startWithBuffer = Number(d.start) - bufferMinutes; 
            const endWithBuffer = Number(d.end) + bufferMinutes;    
            // 直接修改equipment_location2字段
            let locations = d.equipment_location2 ? [d.equipment_location2] : [];
            locations.push('前门'); // 添加前门
            locations.push('门'); // 添加后门
            // 特殊规则：如果包含则添加
            // 定义门禁关联映射表
            const DOOR_PAIRS = mjConfig.DOOR_MAPPING_RULES;
            // 直接操作原数组 locations
            const originalLength = locations.length; // 先存储原始长度，避免无限循环
            for (let i = 0; i < originalLength; i++) {
              const location = locations[i];
              if (DOOR_PAIRS[location]) {
                DOOR_PAIRS[location].forEach(pair => {
                  if (!locations.includes(pair)) {
                    locations.push(pair); // 直接添加到原数组
                  }
                });
              }
            }
    
            // helper.debug(d.user_ref_no, d.user_name, d.equipment_location, d.equipment_location2, moment.unix(d.start).format('YYYY-MM-DD HH:mm:ss'), moment.unix(d.end).format('YYYY-MM-DD HH:mm:ss'));
            return {
              user_ref_no: d.user_ref_no,
              user_name: d.user_name,
              equipment_location: d.equipment_location,
              equipment_location2: locations.join(','), // 将数组转为逗号分隔字符串,
              start: d.start,
              end: d.end,
              startWithBuffer: startWithBuffer,  // 带缓冲的开始时间
              endWithBuffer: endWithBuffer,      // 带缓冲的结束时间
              startStr: moment.unix(d.start).format('YYYY-MM-DD HH:mm:ss'),
              endStr: moment.unix(d.end).format('YYYY-MM-DD HH:mm:ss'),
              startWithBufferStr: moment.unix(startWithBuffer).format('YYYY-MM-DD HH:mm:ss'),
              endWithBufferStr: moment.unix(endWithBuffer).format('YYYY-MM-DD HH:mm:ss'),
            }
          })
          return list;
        } catch(e) {
          helper.debug("e", e);
        }
      }
      return [];
    }
    
    async function getCardNo(userId) {
      let requestUrl = "https://ecard.tju.edu.cn/epayapi/services/thirdquery/batch/custcardinfo";
      const secretKey = '718bce9eed924e0b881ddc3daf7298e5';
      const params = {
        cardtype: 1,
        timestamp: moment().format("YYYYMMDDHHmmss"), //"20250512161449",
        pageno: 1,
        pagesize: 10,
        partner_id: "100139",
        endtime: "9",
        starttime: "0",
        sign_method: "HMAC",
        stuempno: userId,
        sign: "",
      };
      const sign = helper.hmacSha1(helper.paramSort(params, ["sign"]), secretKey);
      params.sign = sign;
    
      // 一卡通系统
      let res = await callApi(requestUrl, {
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        form: params
      });
    
      try {
        let resObj = JSON.parse(res);
        helper.debug("getCardNostart",  resObj,"getCardNoend")
        let cardNo = resObj.page.list[0].cardphyid;
        helper.debug("cardphyid21",cardNo,)
        return cardNo;
      } catch(e) {
        console.trace(e)
        helper.debug("getCardNo error", params, "\n" , res);
        helper.error("getCardNo error", params, "\n" , res);
      }
      return null;
    }
    
    async function getCardNo2(userId) {
      let requestUrl = "https://ecard.tju.edu.cn/epayapi/services/thirdquery/batch/custcardinfo";
      const secretKey = '718bce9eed924e0b881ddc3daf7298e5';
      const params = {
        cardtype: 1,
        timestamp: moment().format("YYYYMMDDHHmmss"), //"20250512161449",
        pageno: 1,
        pagesize: 10,
        partner_id: "100139",
        endtime: "9",
        starttime: "0",
        sign_method: "HMAC",
        stuempno: userId,
        sign: "",
      };
      const sign = helper.hmacSha1(helper.paramSort(params, ["sign"]), secretKey);
      params.sign = sign;
    
      // 一卡通系统
      let res = await callApi(requestUrl, {
        method: "post",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        },
        form: params
      });
      return res;
    }
    
    // 作废
    async function sendDoorReservation(door, reservation, cardNo) {
      let res = await callApi("http://localhost:7203/v1/door-service/auth/person", {
        method: "post",
        headers: {
          "Authorization": "123456",
        },
        data: {
          "method": "gpui/eq_reserv/reservList",
          "params": {
            "task": true,
            "deviceId": door.deviceId,
            "perId": reservation.user_ref_no,
            "cardNo": cardNo,
            "timeNo": "01",
            "endDate": moment(reservation.endStr).format('YYYY-MM-DDTHH:mm:ss.SSSZ'),
          }
       }
      }, 10000);
    
      if(res) {
        try {
          let {data} = JSON.parse(res);
          let list = data.list.map( d=> {
            helper.debug(d.name, d.location, d.enable, d.config.inReader, d.config.outReader, d.config.doorNo, d.deviceId);
            return {
              deviceId: d.deviceId,
              name: d.name,
              location: d.location,
              enable: d.enable,
              inReader: d.config.inReader,
              outReader: d.config.outReader,
              doorNo: d.config.doorNo,
            }
          })
        } catch(e) {
          helper.debug("e", e);
        }
      }
      return [];
    }
    
    async function openDoor(deviceId) {
      let res = await callApi("http://localhost:7203/v1/door-service/open/"+deviceId, {
        method: "post",
        headers: {
          "Authorization": "123456",
        }
      }, 10000);
      // let res = JSON.stringify(doorRes);
      if(res) {
        try {
          let resObj = JSON.parse(res);
          return resObj.resCode;
        } catch(e) {
          helper.debug("e", e);
        }
      }
      return [];
    }
    
    function getDevice(list, key) {
      let f = [];
      if(key) {
        f = list.filter( d => {
          return d.name == key || d.location == key;
        });
      }
      if(f && f.length > 0) {
        return f[0];
      }
    }
    
    function initWs() {
      let stompClient = null;
    
      function connectAll() {
        // 构建订阅路径
        const warnUrl = '/topic/door/warn/all';
        const cardUrl = '/topic/door/card/all';
    
        // 创建 WebSocket 连接（替换原 SockJS）
        const socket = new SockJS('http://127.0.0.1:8220/websocket');
        stompClient = Stomp.over(socket);
    
        // 设置心跳检测
        stompClient.heartbeat.outgoing = 10000;
        stompClient.heartbeat.incoming = 0;
    
        // 建立连接
        stompClient.connect({}, (frame) => {
          console.log('initWs Connected:', frame);
    
          // 订阅警告信息
          stompClient.subscribe(warnUrl, (message) => {
            helper.debug('Received warning:', message.body);
            dealWithMsg(warnUrl, message.body);
          });
          // 订阅卡片信息
          stompClient.subscribe(cardUrl, (message) => {
            helper.debug('Received card event:', message.body);
            dealWithMsg(warnUrl, message.body);
          });
        });
    
        socket.onerror = (error) => {
          console.error('WebSocket Error:', error);
        };
    
        // 断开重连逻辑
        socket.onclose = () => {
          console.log('Connection closed, attempting reconnect...');
          setTimeout(connectAll, 3000);
        };
      }
      // 启动连接
      connectAll();
    }
    
    function dealWithMsg(topic, msg) {
      helper.debug("dealWithMsg", topic, '\n', typeof msg, msg);
      try {
        let data = JSON.parse(msg);
        helper.debug("datadatakey", data);
        if(data.hasOwnProperty(type) && data.type == "DOOR_OPEN") {
          helper.debug("datatype", data.type);
          // 判断是否有 door id 和 cardNo
          if(data.door.id && data.data.cardNo) {
            // 判断是否在预约时间段
            let key = data.door.id + "_" + data.data.cardNo.replace(/(..)(..)(..)(..)/, '$4$3$2$1');
            helper.debug("datakey", key);
            if(reservationCache.hasOwnProperty(key)) {
              let canOpen = false;
    
              for(let i =0; i<reservationCache[key].length; i++) {
                let re = reservationCache[key][i];
                let now = moment().unix();
                if(now >= re.startWithBuffer && now <= re.endWithBuffer) {
                  canOpen = true;
                  break;
                }
              }
              // 触发远程开门动作
              if(canOpen) {
                helper.debug("dataidid", data.door.id);
                openDoor(data.door.id);
              }
            }
          }
        }
      } catch(e) {
        console.trace(e)
        helper.info("dealWithMsg error", e, topic, msg);
      }
    }
    
    async function start() {
      // 1小时重启一次
      schedule.scheduleJob('0 1 * * * *',() => {
          helper.log('Auto restart server');
          process.exit(1);
      });
      // 20秒执行一次
      schedule.scheduleJob('*/20 * * * * *',()=>{
          syncData();
          //helper.log('syncLightDataToBeacool success');
      });
    
      // 启动监听服务
      app = new AppServer(sysConfig);
      helper.debug("AppServer start finished");
    
      // mqtt连接，系统互通各种消息通道
      helper.connectMqtt("mqtt");
      helper.info("connectMqtt success");
    
      // 日志服务连接至远程logstash
      helper.connectLogstash(sysConfig);
      helper.info("connectLogstash success");
    
      initWs();
      await syncData();
      helper.info("syncData run success");
    
      helper.info("start success");
    }
    
    start();
    
    async function test() {
    }
    
    // test()
    // getDoorList();
    
    // {"type":"DOOR_OPEN","door":{"id":7,"no":"","deviceName":"A106","inReader":8,"outReader":0,"boardIp":"************","boardPort":5768,"boardId":19,"mapId":null,"groupNo":"01","deviceType":0,"doorNumber":8,"boardDoorNo":4},"data":{"cardNo":"804DB873","personName":"开门","personId":"0000000002","personNo":"开门","idCard":"","openFlag":16,"openResult":"1","time":1747127853000}}
    
    // {"type":"DOOR_OPEN","door":{"id":7,"no":"","deviceName":"A106","inReader":8,"outReader":0,"boardIp":"************","boardPort":5768,"boardId":19,"mapId":null,"groupNo":"01","deviceType":0,"doorNumber":8,"boardDoorNo":4},"data":{"cardNo":"FEFB18A8","personName":"","personId":null,"personNo":null,"idCard":null,"openFlag":16,"openResult":"0","time":1747127856000}}
    
    // {"type":"DOOR_OPEN","door":{"id":7,"no":"","deviceName":"A106","inReader":8,"outReader":0,"boardIp":"************","boardPort":5768,"boardId":19,"mapId":null,"groupNo":"01","deviceType":0,"doorNumber":8,"boardDoorNo":4},"data":{"cardNo":"FEFB18A8","personName":"","personId":null,"personNo":null,"idCard":null,"openFlag":16,"openResult":"0","time":1747127860000}}
    