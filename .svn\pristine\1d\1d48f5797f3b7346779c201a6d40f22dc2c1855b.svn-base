<template>
  <div>
    <div class="left">
      <Title tit="系统设备状态">
        <el-input placeholder="请输入内容" v-model="input" class="input-with-select">
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>

        <div class="select">
          <input type="checkbox" id="selectAll" v-model="allSelected" />
          <label for="selectAll" class="selectAll">全部设备</label>
        </div>
        <ul class="listul">
          <li v-for="(item, index) in items" :key="index" class="listli" @click="changedevice(item.id)">
            <input class="listliinput1" type="checkbox" :id="'checkbox' + index" v-model="selectedItems"
              :value="item.name" />
            <label class="listliinput2" :for="'checkbox' + index">
              {{ item.name }}
            </label>
            <div class="yuanqiu">{{ iframeSrc }}</div>
          </li>
        </ul>
        <!-- <div class="zongshu">
          当前: <span class="dangq">{{ selectedItems.length }}</span> / 总数:
          <span class="dangqs">{{ items.length }}</span>
        </div> -->
      </Title>
    </div>
    <div class="right-panel"></div>
    <iframe class="right" :width="iframeWidth" :src="iframeSrc" frameborder="0"></iframe>
  </div>
</template>

<script>
import axios from "axios";
import Title from "@/components/common/Title.vue";
export default {
  components: {
    Title,
  },
  data() {
    return {
      input: "",
      iframeWidth: '350px',
      selectedItems: [], // 用于存储被选中的项
      allSelected: false, // 全选状态
      items: [
        { name: "冷却塔/B3-CAD-1", checked: false },
        { name: "冷却塔/B3-CAD-2", checked: false },
        // 省略其他重复项...
      ],
      deviceId: ''
    };
  },
  computed: {
    iframeSrc() {
      return `https://hailuo.yuankong.org.cn:8060/#/card/deviceDetails?deviceId=${this.deviceId}`;
    }
  },
  mounted() {
    this.fetchDevices(602, 'DT');
    var that = this;
    window.addEventListener("message", function (event) {
      let data = event ? event.data.message.name : ''
      console.log(data);
      if (data == "open") {
        that.iframeWidth = "1335px"
      } else {
        that.iframeWidth = "338px"
      }
    })
  },
  methods: {
    changedevice(id) {
      console.log(id, 112);
      this.deviceId = id
    },
    childMethod(param1, param2) {
      console.log(param1, param2, 112);
      this.fetchDevices(param1, param2)
    },
    getsblist() {
      const token = localStorage.getItem('token');
      console.log(token, 112);
    },
    async fetchDevices(resourceId, deviceTypes) {
      try {
        // 从 localStorage 中获取 token
        const token = localStorage.getItem('token');
        console.log(token, 112);
        // 如果没有 token，直接返回
        if (!token) {
          console.error('Token not found in localStorage');
          return;
        }

        const response = await axios.get('https://hailuo.yuankong.org.cn:8060/api/device/api/resourceDeviceList', {
          params: {
            buildingId: 1,
            resourceId: resourceId,
            deviceTypes: deviceTypes
          },
          // 将 token 放入请求头中
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        console.log(response, 112);
        this.items = response.data.data;

      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    },
    toggleAllSelection() {
      if (this.allSelected) {
        this.selectedItems = this.items.map((item) => item.name);
      } else {
        this.selectedItems = [];
      }
    },
    checkItemSelection() {
      this.allSelected = this.selectedItems.length === this.items.length;
    },
  },
  watch: {
    allSelected(newVal) {
      this.toggleAllSelection();
    },
    selectedItems(newVal) {
      this.checkItemSelection();
    },
  },
};
</script>

<style lang="less" scoped>
.left {
  padding-left: 16px;
  padding-top: 14px;
  background: url("../assets/image/beijingzhe.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 336px;
  height: 980px;
  position: fixed;
  top: 70px;
  left: 10px;
  z-index: 2;
  text-align: center;

  .input-with-select {
    width: 305px;
    height: 43px;
  }

  .select {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 305px;
    height: 42px;
    margin-left: 8px;
    margin-top: 8px;
    padding-left: 33px;
    background: url("../assets/image/selectbgc.png") !important;

    .selectAll {
      font-family: DingTalk JinBuTi;
      font-weight: 400;
      font-size: 14px;
      color: #7dffd9;
      padding-left: 13px;
    }
  }

  .listul {
    height: 780px;
    overflow-y: scroll;
    width: 100%;

    &::-webkit-scrollbar {
      width: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f1f1f1;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #0f2459;
    }

    &::-webkit-scrollbar-thumb:hover {
      background-color: #555;
    }
  }

  .listli {
    display: flex;
    align-items: center;
    margin-left: 8px;
    margin-top: 1px;
    width: 305px;
    height: 42px;
    background: url("../assets/image/selectbgc.png") !important;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #7dffd9;
    padding-left: 33px;

    .listliinput2 {
      padding-left: 14px;
    }

    .yuanqiu {
      width: 11px;
      height: 11px;
      background-color: #a0fdda;
      border-radius: 50%;
      margin-left: 17px;
    }
  }

  .zongshu {
    margin-top: 12px;
    font-family: DingTalk JinBuTi;
    font-weight: 400;
    font-size: 14px;
    color: #abe3f4;

    .dangq {
      color: #98f1d2 !important;
    }

    .dangqs {
      color: #fff !important;
    }
  }
}

.right {
  position: fixed;
  right: 5px;
  // width: 1338px;
  height: 997px;
  overflow: hidden;
  top: 66px;
  z-index: 3;
}

.right-panel {
  overflow: hidden;
  position: fixed;
  z-index: 1;
  right: 10px;
  width: 350px;
  top: 70px;
  height: 980px;
  // background: url("../assets/image/right.png");
  background-size: 100% 100%;
}

/deep/ .el-input__wrapper {
  background: url("../assets/image/inputbeij.png") !important;
}

/deep/ .el-input__inner {
  font-family: DingTalk JinBuTi;
  font-weight: 400;
  font-size: 14px;
  color: #b5eff7 !important;
}
</style>
