{"name": "gmgj", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@easydarwin/easyplayer": "^5.1.1", "@fit-screen/vue": "^1.0.1", "@kjgl77/datav-vue3": "^1.4.2", "@vue/composition-api": "^1.7.2", "autofit.js": "^3.1.0", "axios": "^1.5.1", "core-js": "^3.6.5", "dplayer": "^1.27.1", "easy-player": "^2.2.7", "echarts": "^5.4.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "element-plus": "^2.6.2", "hls.js": "^1.4.10", "jsencrypt": "^3.3.2", "lib-flexible": "^0.3.2", "plyr": "^3.7.8", "px2rem-loader": "^0.1.9", "sass-loader": "^13.3.2", "video.js": "^8.5.2", "videojs-contrib-hls": "^5.15.0", "vue": "^3.0.0", "vue-h265-player": "0.0.24", "vue-router": "^4.0.0-0", "vue-video-player": "^6.0.0", "vuex": "^4.0.0-0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/compiler-sfc": "^3.0.0", "copy-webpack-plugin": "^5.1.2", "less": "^3.0.4", "less-loader": "^5.0.0", "mockjs": "^1.1.0", "sass-loader": "^13.3.2"}}