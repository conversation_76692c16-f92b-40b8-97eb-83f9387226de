class so {
  static aiChat(v) {
    return fetch(window.voiceHelperOptions.chatApi, {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(v)
    }).then((o) => o.json());
  }
  static voiceToText(v) {
    const o = new FormData();
    return o.append("file", v), console.log("voiceToText formData", o), fetch(window.voiceHelperOptions.voiceToTextApi, {
      method: "POST",
      headers: {
        // "Content-Type": "multipart/form-data",
      },
      body: o
    }).then((m) => m.json());
  }
}
function l1(z, v, o) {
  let m = document.createElement(z);
  return m.className = v, o && (m.innerHTML = o), m;
}
function ho(z, v) {
  if (typeof z == "object" && z !== null) {
    let o = { ...v };
    return Object.keys(z).forEach((m) => {
      o[m] = ho(z[m], v[m]);
    }), o;
  }
  return z ?? v;
}
function lo(z, v) {
  v._tempEvent || (v._tempEvent = []), v._tempEventBind || (v._tempEventBind = []);
  let o = v._tempEvent.indexOf(z), m = null;
  return o === -1 ? (m = z.bind(v), v._tempEvent.push(z), v._tempEventBind.push(m)) : m = v._tempEventBind[o], m;
}
let a1 = '<svg t="1727596900380" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="8774" width="200" height="200"><path d="M93.37191 57.154573 930.62809 540.635608 93.37191 1024Z" fill="__fillColor__" p-id="8775"></path></svg>', c1 = '<svg t="1727598818918" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9098" width="200" height="200"><path d="M535.466667 812.8l450.133333-563.2c14.933333-19.2 2.133333-49.066667-23.466667-49.066667H61.866667c-25.6 0-38.4 29.866667-23.466667 49.066667l450.133333 563.2c12.8 14.933333 34.133333 14.933333 46.933334 0z" fill="__fillColor__" p-id="9099"></path></svg>', h1 = '<svg t="1727605464995" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4317" width="200" height="200"><path d="M512 192c156.448 0 296.021333 98.730667 418.410667 291.605333a52.938667 52.938667 0 0 1 0 56.789334C808.021333 733.269333 668.448 832 512 832c-156.448 0-296.021333-98.730667-418.410667-291.605333a52.938667 52.938667 0 0 1 0-56.789334C215.978667 290.730667 355.552 192 512 192z m0 128c-106.037333 0-192 85.962667-192 192s85.962667 192 192 192 192-85.962667 192-192-85.962667-192-192-192z m0 320c70.688 0 128-57.312 128-128s-57.312-128-128-128-128 57.312-128 128 57.312 128 128 128z" fill="__fillColor__" p-id="4318"></path></svg>', g1 = '<svg t="1727605539486" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6200" width="200" height="200"><path d="M512 266.723556c-19.996444 0-39.196444 1.422222-57.628444 4.124444-23.324444 3.356444-45.056-12.088889-48.554667-34.531556-3.527111-22.442667 12.515556-43.377778 35.84-46.734222 22.613333-3.299556 46.08-5.034667 70.343111-5.034666 175.388444 0 303.36 91.022222 385.308444 177.777777a761.059556 761.059556 0 0 1 114.858667 159.175111c2.56 4.864 5.063111 9.756444 7.480889 14.705778l0.426667 0.938667 0.170666 0.341333v0.085334l0.028445 0.028444-38.968889 16.725333 39.025778 16.725334-0.085334 0.113777-0.085333 0.227556-0.341333 0.739556a624.355556 624.355556 0 0 1-22.613334 41.642666 770.389333 770.389333 0 0 1-67.84 96.227556 43.804444 43.804444 0 0 1-59.591111 6.144 40.078222 40.078222 0 0 1-7.196444-57.287111 686.478222 686.478222 0 0 0 71.168-104.533334 678.968889 678.968889 0 0 0-99.555556-136.675555C760.945778 340.053333 654.165333 266.723556 512 266.723556z m469.333333 287.601777l38.968889 16.725334a39.822222 39.822222 0 0 0 0-33.450667l-38.968889 16.725333zM186.965333 359.594667c8.391111 7.338667 13.368889 17.578667 13.909334 28.444444 0.512 10.894222-3.470222 21.532444-11.093334 29.582222a679.168 679.168 0 0 0-99.555555 136.704 679.367111 679.367111 0 0 0 99.555555 136.704C263.111111 768.568889 369.834667 841.927111 512 841.927111a394.808889 394.808889 0 0 0 134.4-23.239111c21.902222-7.395556 45.909333 3.470222 54.044444 24.405333 8.106667 20.935111-2.616889 44.259556-24.177777 52.536889A483.185778 483.185778 0 0 1 512 924.103111c-175.388444 0-303.36-91.022222-385.308444-177.777778a760.917333 760.917333 0 0 1-114.858667-159.175111c-2.56-4.835556-5.063111-9.756444-7.480889-14.705778l-0.426667-0.938666-0.170666-0.341334-0.028445-0.056888v-0.056889l38.968889-16.725334L3.640889 537.6l0.056889-0.028444 0.028444-0.085334 0.142222-0.341333 0.426667-0.938667 1.621333-3.185778c8.817778-17.464889 18.488889-34.531556 28.842667-51.171555a763.505778 763.505778 0 0 1 91.875556-119.523556 43.349333 43.349333 0 0 1 29.582222-13.368889c11.292444-0.540444 22.357333 3.299556 30.72 10.638223zM42.666667 554.325333L3.697778 537.6a39.822222 39.822222 0 0 0 0 33.450667l38.968889-16.725334z" fill="__fillColor__" p-id="6201"></path><path d="M469.816889 364.544c0-22.784 19.000889-41.272889 42.439111-41.272889 128.113778 0 229.233778 104.391111 229.233778 229.916445 0 14.734222-8.106667 28.359111-21.219556 35.754666a43.52 43.52 0 0 1-42.467555 0 41.073778 41.073778 0 0 1-21.219556-35.754666c0-82.915556-66.133333-147.370667-144.327111-147.370667-23.438222 0-42.439111-18.488889-42.439111-41.272889z m-130.929778 107.008c22.926222 4.807111 37.489778 26.737778 32.568889 49.038222a151.04 151.04 0 0 0-3.527111 32.597334c0 82.915556 66.133333 147.370667 144.327111 147.370666 11.207111 0 22.072889-1.28 32.483556-3.697778 22.755556-5.376 45.710222 8.248889 51.2 30.407112 5.546667 22.158222-8.476444 44.458667-31.260445 49.806222a229.063111 229.063111 0 0 1-52.423111 5.973333c-128.113778 0-229.233778-104.391111-229.233778-229.916444 0-17.066667 1.877333-33.848889 5.432889-49.92 2.360889-10.695111 9.016889-20.053333 18.488889-25.998223 9.443556-5.973333 20.935111-7.992889 31.943111-5.688888v0.028444zM100.209778 111.303111a43.320889 43.320889 0 0 1 60.017778 0L924.302222 854.186667c16.099556 16.213333 15.872 41.955556-0.512 57.856a43.320889 43.320889 0 0 1-59.505778 0.512L100.209778 169.671111a40.476444 40.476444 0 0 1 0-58.368z" fill="__fillColor__" p-id="6202"></path></svg>', p1 = '<svg t="1730874050746" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="30192" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M686.933333 469.333333c8.533333-8.533333 8.533333-21.333333 4.266667-29.866666-8.533333-17.066667-17.066667-29.866667-25.6-42.666667-8.533333-8.533333-17.066667-12.8-29.866667-12.8-17.066667 4.266667-34.133333 0-46.933333-8.533333-12.8-8.533333-25.6-21.333333-29.866667-38.4-4.266667-8.533333-12.8-17.066667-21.333333-21.333334h-55.466667c-12.8 0-17.066667 8.533333-21.333333 21.333334-4.266667 17.066667-17.066667 29.866667-29.866667 38.4-8.533333 8.533333-25.6 12.8-42.666666 8.533333-12.8-4.266667-21.333333 0-29.866667 8.533333-12.8 12.8-17.066667 29.866667-25.6 42.666667-4.266667 8.533333 0 21.333333 4.266667 29.866667 21.333333 25.6 21.333333 59.733333 0 85.333333-8.533333 8.533333-8.533333 21.333333-4.266667 29.866667 8.533333 17.066667 17.066667 34.133333 25.6 46.933333 8.533333 8.533333 17.066667 12.8 29.866667 12.8 34.133333-8.533333 64 12.8 76.8 42.666667 4.266667 8.533333 12.8 17.066667 21.333333 21.333333h51.2c12.8 0 17.066667-8.533333 21.333333-21.333333 4.266667-17.066667 17.066667-29.866667 29.866667-38.4 12.8-8.533333 29.866667-8.533333 46.933333-8.533334 8.533333 0 21.333333 0 29.866667-8.533333 12.8-12.8 17.066667-29.866667 25.6-46.933333 4.266667-8.533333 0-21.333333-4.266667-29.866667-25.6-21.333333-25.6-59.733333 0-81.066667zM512 584.533333c-38.4 0-72.533333-34.133333-72.533333-72.533333 0-38.4 34.133333-72.533333 72.533333-72.533333 38.4 0 72.533333 34.133333 72.533333 72.533333 0 38.4-34.133333 72.533333-72.533333 72.533333zM490.666667 789.333333V853.333333c-170.666667-8.533333-307.2-149.333333-320-320h64c12.8 136.533333 119.466667 243.2 256 256z m42.666666 64v-64c136.533333-8.533333 243.2-119.466667 256-256H853.333333c-12.8 170.666667-149.333333 307.2-320 320z m320-362.666666h-64c-8.533333-136.533333-119.466667-243.2-256-256V170.666667c170.666667 12.8 307.2 149.333333 320 320zM490.666667 170.666667v64c-136.533333 8.533333-243.2 119.466667-256 256H170.666667c12.8-170.666667 149.333333-307.2 320-320z" p-id="30193" fill="__fillColor__"></path></svg>', d1 = '<svg t="1744551493835" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1517" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M512 330.666667c14.933333 0 29.866667 4.266667 40.533333 14.933333l277.33333399 234.666667c27.733333 23.466667 29.866667 64 8.53333301 89.6-23.466667 27.733333-64 29.866667-89.6 8.53333299L512 477.866667l-236.8 200.53333299c-27.733333 23.466667-68.266667 19.19999999-89.6-8.53333299-23.466667-27.733333-19.19999999-68.266667 8.53333301-89.6l277.33333399-234.666667c10.666667-10.666667 25.6-14.933333 40.533333-14.933333z" fill="__fillColor__" p-id="1518"></path></svg>', _1 = '<svg t="1744590715048" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2682" xmlns:xlink="http://www.w3.org/1999/xlink" width="200" height="200"><path d="M512 608 512 608c88 0 160-72 160-160L672 256c0-88-72-160-160-160l0 0c-88 0-160 72-160 160l0 192C352 536 424 608 512 608z" p-id="2683" fill="__fillColor__"></path><path d="M796.6 492.4c2.7-17.5-9.2-33.8-26.7-36.5-17.5-2.6-33.8 9.3-36.5 26.7C716.6 590.6 621.5 672 512 672c-109.5 0-204.7-81.5-221.4-189.5-2.7-17.5-19.1-29.4-36.5-26.7-17.5 2.7-29.4 19-26.7 36.5 20.2 130.5 124 227.8 252.6 241.8L480 832l-96 0c-17.7 0-32 14.3-32 32s14.3 32 32 32l256 0c17.7 0 32-14.3 32-32s-14.3-32-32-32l-96 0 0-97.9C672.5 720.1 776.4 622.9 796.6 492.4z" p-id="2684" fill="__fillColor__"></path></svg>', v1 = '<svg t="1744696456331" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10556" width="200" height="200"><path d="M266.24 267.52a248.32 244.48 0 1 0 496.64 0 248.32 244.48 0 1 0-496.64 0zM628.48 593.28H421.76a320 320 0 0 0-320 315.52v20.48c0 71.04 143.36 71.04 320 71.04h206.72c177.28 0 320 0 320-71.04V908.8a320 320 0 0 0-320-315.52z" fill="__fillColor__" p-id="10557"></path></svg>';
const w1 = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  arrowTop: d1,
  eyeFilled: h1,
  eyeInvisibleFilled: g1,
  settingAndCircle: p1,
  triangleBottom: c1,
  triangleRight: a1,
  user: v1,
  voice: _1
}, Symbol.toStringTag, { value: "Module" }));
function ir(z, v = "black") {
  let o = w1[z];
  if (!o) return;
  let m = encodeURI(o), b = v.replaceAll("#", "%23");
  return `data:image/svg+xml;charset=UTF-8,${m.replaceAll("__fillColor__", b)}`;
}
class x1 {
  addEventListener(v, o) {
    this._listeners === void 0 && (this._listeners = {});
    const m = this._listeners;
    m[v] === void 0 && (m[v] = []), m[v].indexOf(o) === -1 && m[v].push(o), this.eventListenersAddComplete && this.eventListenersAddComplete(v, o);
  }
  hasEventListener(v, o) {
    if (this._listeners === void 0) return !1;
    const m = this._listeners;
    return m[v] !== void 0 && m[v].indexOf(o) !== -1;
  }
  removeEventListener(v, o) {
    if (this._listeners === void 0) return;
    const b = this._listeners[v];
    if (b !== void 0) {
      const G = b.indexOf(o);
      G !== -1 && b.splice(G, 1);
    }
    this.eventListenersRemoveComplete && this.eventListenersRemoveComplete(v, o);
  }
  dispatchEvent(v) {
    if (this._listeners === void 0) return;
    const m = this._listeners[v.type];
    if (m !== void 0) {
      const b = m.slice(0);
      for (let G = 0, T = b.length; G < T; G++)
        b[G].call(this, v);
    }
  }
}
var ur = typeof globalThis < "u" ? globalThis : typeof window < "u" ? window : typeof global < "u" ? global : typeof self < "u" ? self : {}, oe = { exports: {} };
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */
var m1 = oe.exports, ao;
function A1() {
  return ao || (ao = 1, function(z, v) {
    (function() {
      var o, m = "4.17.21", b = 200, G = "Unsupported core-js use. Try https://npms.io/search?q=ponyfill.", T = "Expected a function", dn = "Invalid `variable` option passed into `_.template`", Xn = "__lodash_hash_undefined__", ut = 500, dt = "__lodash_placeholder__", Mn = 1, se = 2, _t = 4, vt = 1, le = 2, Cn = 1, wt = 2, Ii = 4, Un = 8, Ut = 16, Pn = 32, Pt = 64, Fn = 128, Ft = 256, fr = 512, go = 30, po = "...", _o = 800, vo = 16, Ri = 1, wo = 2, xo = 3, ae = 1 / 0, xt = 9007199254740991, mo = 17976931348623157e292, ce = NaN, bn = **********, Ao = bn - 1, So = bn >>> 1, yo = [
        ["ary", Fn],
        ["bind", Cn],
        ["bindKey", wt],
        ["curry", Un],
        ["curryRight", Ut],
        ["flip", fr],
        ["partial", Pn],
        ["partialRight", Pt],
        ["rearg", Ft]
      ], mt = "[object Arguments]", he = "[object Array]", Co = "[object AsyncFunction]", Ht = "[object Boolean]", Nt = "[object Date]", To = "[object DOMException]", ge = "[object Error]", pe = "[object Function]", Oi = "[object GeneratorFunction]", Tn = "[object Map]", zt = "[object Number]", Eo = "[object Null]", Hn = "[object Object]", Mi = "[object Promise]", Lo = "[object Proxy]", Gt = "[object RegExp]", En = "[object Set]", $t = "[object String]", de = "[object Symbol]", Io = "[object Undefined]", qt = "[object WeakMap]", Ro = "[object WeakSet]", Kt = "[object ArrayBuffer]", At = "[object DataView]", or = "[object Float32Array]", sr = "[object Float64Array]", lr = "[object Int8Array]", ar = "[object Int16Array]", cr = "[object Int32Array]", hr = "[object Uint8Array]", gr = "[object Uint8ClampedArray]", pr = "[object Uint16Array]", dr = "[object Uint32Array]", Oo = /\b__p \+= '';/g, Mo = /\b(__p \+=) '' \+/g, bo = /(__e\(.*?\)|\b__t\)) \+\n'';/g, bi = /&(?:amp|lt|gt|quot|#39);/g, Wi = /[&<>"']/g, Wo = RegExp(bi.source), Do = RegExp(Wi.source), Bo = /<%-([\s\S]+?)%>/g, Uo = /<%([\s\S]+?)%>/g, Di = /<%=([\s\S]+?)%>/g, Po = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/, Fo = /^\w*$/, Ho = /[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g, _r = /[\\^$.*+?()[\]{}|]/g, No = RegExp(_r.source), vr = /^\s+/, zo = /\s/, Go = /\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/, $o = /\{\n\/\* \[wrapped with (.+)\] \*/, qo = /,? & /, Ko = /[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g, ko = /[()=,{}\[\]\/\s]/, Zo = /\\(\\)?/g, Yo = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g, Bi = /\w*$/, Xo = /^[-+]0x[0-9a-f]+$/i, Jo = /^0b[01]+$/i, Vo = /^\[object .+?Constructor\]$/, Qo = /^0o[0-7]+$/i, jo = /^(?:0|[1-9]\d*)$/, ns = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g, _e = /($^)/, ts = /['\n\r\u2028\u2029\\]/g, ve = "\\ud800-\\udfff", es = "\\u0300-\\u036f", rs = "\\ufe20-\\ufe2f", is = "\\u20d0-\\u20ff", Ui = es + rs + is, Pi = "\\u2700-\\u27bf", Fi = "a-z\\xdf-\\xf6\\xf8-\\xff", us = "\\xac\\xb1\\xd7\\xf7", fs = "\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf", os = "\\u2000-\\u206f", ss = " \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000", Hi = "A-Z\\xc0-\\xd6\\xd8-\\xde", Ni = "\\ufe0e\\ufe0f", zi = us + fs + os + ss, wr = "['’]", ls = "[" + ve + "]", Gi = "[" + zi + "]", we = "[" + Ui + "]", $i = "\\d+", as = "[" + Pi + "]", qi = "[" + Fi + "]", Ki = "[^" + ve + zi + $i + Pi + Fi + Hi + "]", xr = "\\ud83c[\\udffb-\\udfff]", cs = "(?:" + we + "|" + xr + ")", ki = "[^" + ve + "]", mr = "(?:\\ud83c[\\udde6-\\uddff]){2}", Ar = "[\\ud800-\\udbff][\\udc00-\\udfff]", St = "[" + Hi + "]", Zi = "\\u200d", Yi = "(?:" + qi + "|" + Ki + ")", hs = "(?:" + St + "|" + Ki + ")", Xi = "(?:" + wr + "(?:d|ll|m|re|s|t|ve))?", Ji = "(?:" + wr + "(?:D|LL|M|RE|S|T|VE))?", Vi = cs + "?", Qi = "[" + Ni + "]?", gs = "(?:" + Zi + "(?:" + [ki, mr, Ar].join("|") + ")" + Qi + Vi + ")*", ps = "\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])", ds = "\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])", ji = Qi + Vi + gs, _s = "(?:" + [as, mr, Ar].join("|") + ")" + ji, vs = "(?:" + [ki + we + "?", we, mr, Ar, ls].join("|") + ")", ws = RegExp(wr, "g"), xs = RegExp(we, "g"), Sr = RegExp(xr + "(?=" + xr + ")|" + vs + ji, "g"), ms = RegExp([
        St + "?" + qi + "+" + Xi + "(?=" + [Gi, St, "$"].join("|") + ")",
        hs + "+" + Ji + "(?=" + [Gi, St + Yi, "$"].join("|") + ")",
        St + "?" + Yi + "+" + Xi,
        St + "+" + Ji,
        ds,
        ps,
        $i,
        _s
      ].join("|"), "g"), As = RegExp("[" + Zi + ve + Ui + Ni + "]"), Ss = /[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/, ys = [
        "Array",
        "Buffer",
        "DataView",
        "Date",
        "Error",
        "Float32Array",
        "Float64Array",
        "Function",
        "Int8Array",
        "Int16Array",
        "Int32Array",
        "Map",
        "Math",
        "Object",
        "Promise",
        "RegExp",
        "Set",
        "String",
        "Symbol",
        "TypeError",
        "Uint8Array",
        "Uint8ClampedArray",
        "Uint16Array",
        "Uint32Array",
        "WeakMap",
        "_",
        "clearTimeout",
        "isFinite",
        "parseInt",
        "setTimeout"
      ], Cs = -1, N = {};
      N[or] = N[sr] = N[lr] = N[ar] = N[cr] = N[hr] = N[gr] = N[pr] = N[dr] = !0, N[mt] = N[he] = N[Kt] = N[Ht] = N[At] = N[Nt] = N[ge] = N[pe] = N[Tn] = N[zt] = N[Hn] = N[Gt] = N[En] = N[$t] = N[qt] = !1;
      var H = {};
      H[mt] = H[he] = H[Kt] = H[At] = H[Ht] = H[Nt] = H[or] = H[sr] = H[lr] = H[ar] = H[cr] = H[Tn] = H[zt] = H[Hn] = H[Gt] = H[En] = H[$t] = H[de] = H[hr] = H[gr] = H[pr] = H[dr] = !0, H[ge] = H[pe] = H[qt] = !1;
      var Ts = {
        // Latin-1 Supplement block.
        À: "A",
        Á: "A",
        Â: "A",
        Ã: "A",
        Ä: "A",
        Å: "A",
        à: "a",
        á: "a",
        â: "a",
        ã: "a",
        ä: "a",
        å: "a",
        Ç: "C",
        ç: "c",
        Ð: "D",
        ð: "d",
        È: "E",
        É: "E",
        Ê: "E",
        Ë: "E",
        è: "e",
        é: "e",
        ê: "e",
        ë: "e",
        Ì: "I",
        Í: "I",
        Î: "I",
        Ï: "I",
        ì: "i",
        í: "i",
        î: "i",
        ï: "i",
        Ñ: "N",
        ñ: "n",
        Ò: "O",
        Ó: "O",
        Ô: "O",
        Õ: "O",
        Ö: "O",
        Ø: "O",
        ò: "o",
        ó: "o",
        ô: "o",
        õ: "o",
        ö: "o",
        ø: "o",
        Ù: "U",
        Ú: "U",
        Û: "U",
        Ü: "U",
        ù: "u",
        ú: "u",
        û: "u",
        ü: "u",
        Ý: "Y",
        ý: "y",
        ÿ: "y",
        Æ: "Ae",
        æ: "ae",
        Þ: "Th",
        þ: "th",
        ß: "ss",
        // Latin Extended-A block.
        Ā: "A",
        Ă: "A",
        Ą: "A",
        ā: "a",
        ă: "a",
        ą: "a",
        Ć: "C",
        Ĉ: "C",
        Ċ: "C",
        Č: "C",
        ć: "c",
        ĉ: "c",
        ċ: "c",
        č: "c",
        Ď: "D",
        Đ: "D",
        ď: "d",
        đ: "d",
        Ē: "E",
        Ĕ: "E",
        Ė: "E",
        Ę: "E",
        Ě: "E",
        ē: "e",
        ĕ: "e",
        ė: "e",
        ę: "e",
        ě: "e",
        Ĝ: "G",
        Ğ: "G",
        Ġ: "G",
        Ģ: "G",
        ĝ: "g",
        ğ: "g",
        ġ: "g",
        ģ: "g",
        Ĥ: "H",
        Ħ: "H",
        ĥ: "h",
        ħ: "h",
        Ĩ: "I",
        Ī: "I",
        Ĭ: "I",
        Į: "I",
        İ: "I",
        ĩ: "i",
        ī: "i",
        ĭ: "i",
        į: "i",
        ı: "i",
        Ĵ: "J",
        ĵ: "j",
        Ķ: "K",
        ķ: "k",
        ĸ: "k",
        Ĺ: "L",
        Ļ: "L",
        Ľ: "L",
        Ŀ: "L",
        Ł: "L",
        ĺ: "l",
        ļ: "l",
        ľ: "l",
        ŀ: "l",
        ł: "l",
        Ń: "N",
        Ņ: "N",
        Ň: "N",
        Ŋ: "N",
        ń: "n",
        ņ: "n",
        ň: "n",
        ŋ: "n",
        Ō: "O",
        Ŏ: "O",
        Ő: "O",
        ō: "o",
        ŏ: "o",
        ő: "o",
        Ŕ: "R",
        Ŗ: "R",
        Ř: "R",
        ŕ: "r",
        ŗ: "r",
        ř: "r",
        Ś: "S",
        Ŝ: "S",
        Ş: "S",
        Š: "S",
        ś: "s",
        ŝ: "s",
        ş: "s",
        š: "s",
        Ţ: "T",
        Ť: "T",
        Ŧ: "T",
        ţ: "t",
        ť: "t",
        ŧ: "t",
        Ũ: "U",
        Ū: "U",
        Ŭ: "U",
        Ů: "U",
        Ű: "U",
        Ų: "U",
        ũ: "u",
        ū: "u",
        ŭ: "u",
        ů: "u",
        ű: "u",
        ų: "u",
        Ŵ: "W",
        ŵ: "w",
        Ŷ: "Y",
        ŷ: "y",
        Ÿ: "Y",
        Ź: "Z",
        Ż: "Z",
        Ž: "Z",
        ź: "z",
        ż: "z",
        ž: "z",
        Ĳ: "IJ",
        ĳ: "ij",
        Œ: "Oe",
        œ: "oe",
        ŉ: "'n",
        ſ: "s"
      }, Es = {
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#39;"
      }, Ls = {
        "&amp;": "&",
        "&lt;": "<",
        "&gt;": ">",
        "&quot;": '"',
        "&#39;": "'"
      }, Is = {
        "\\": "\\",
        "'": "'",
        "\n": "n",
        "\r": "r",
        "\u2028": "u2028",
        "\u2029": "u2029"
      }, Rs = parseFloat, Os = parseInt, nu = typeof ur == "object" && ur && ur.Object === Object && ur, Ms = typeof self == "object" && self && self.Object === Object && self, V = nu || Ms || Function("return this")(), yr = v && !v.nodeType && v, ft = yr && !0 && z && !z.nodeType && z, tu = ft && ft.exports === yr, Cr = tu && nu.process, _n = function() {
        try {
          var a = ft && ft.require && ft.require("util").types;
          return a || Cr && Cr.binding && Cr.binding("util");
        } catch {
        }
      }(), eu = _n && _n.isArrayBuffer, ru = _n && _n.isDate, iu = _n && _n.isMap, uu = _n && _n.isRegExp, fu = _n && _n.isSet, ou = _n && _n.isTypedArray;
      function ln(a, g, h) {
        switch (h.length) {
          case 0:
            return a.call(g);
          case 1:
            return a.call(g, h[0]);
          case 2:
            return a.call(g, h[0], h[1]);
          case 3:
            return a.call(g, h[0], h[1], h[2]);
        }
        return a.apply(g, h);
      }
      function bs(a, g, h, x) {
        for (var E = -1, B = a == null ? 0 : a.length; ++E < B; ) {
          var Y = a[E];
          g(x, Y, h(Y), a);
        }
        return x;
      }
      function vn(a, g) {
        for (var h = -1, x = a == null ? 0 : a.length; ++h < x && g(a[h], h, a) !== !1; )
          ;
        return a;
      }
      function Ws(a, g) {
        for (var h = a == null ? 0 : a.length; h-- && g(a[h], h, a) !== !1; )
          ;
        return a;
      }
      function su(a, g) {
        for (var h = -1, x = a == null ? 0 : a.length; ++h < x; )
          if (!g(a[h], h, a))
            return !1;
        return !0;
      }
      function Jn(a, g) {
        for (var h = -1, x = a == null ? 0 : a.length, E = 0, B = []; ++h < x; ) {
          var Y = a[h];
          g(Y, h, a) && (B[E++] = Y);
        }
        return B;
      }
      function xe(a, g) {
        var h = a == null ? 0 : a.length;
        return !!h && yt(a, g, 0) > -1;
      }
      function Tr(a, g, h) {
        for (var x = -1, E = a == null ? 0 : a.length; ++x < E; )
          if (h(g, a[x]))
            return !0;
        return !1;
      }
      function $(a, g) {
        for (var h = -1, x = a == null ? 0 : a.length, E = Array(x); ++h < x; )
          E[h] = g(a[h], h, a);
        return E;
      }
      function Vn(a, g) {
        for (var h = -1, x = g.length, E = a.length; ++h < x; )
          a[E + h] = g[h];
        return a;
      }
      function Er(a, g, h, x) {
        var E = -1, B = a == null ? 0 : a.length;
        for (x && B && (h = a[++E]); ++E < B; )
          h = g(h, a[E], E, a);
        return h;
      }
      function Ds(a, g, h, x) {
        var E = a == null ? 0 : a.length;
        for (x && E && (h = a[--E]); E--; )
          h = g(h, a[E], E, a);
        return h;
      }
      function Lr(a, g) {
        for (var h = -1, x = a == null ? 0 : a.length; ++h < x; )
          if (g(a[h], h, a))
            return !0;
        return !1;
      }
      var Bs = Ir("length");
      function Us(a) {
        return a.split("");
      }
      function Ps(a) {
        return a.match(Ko) || [];
      }
      function lu(a, g, h) {
        var x;
        return h(a, function(E, B, Y) {
          if (g(E, B, Y))
            return x = B, !1;
        }), x;
      }
      function me(a, g, h, x) {
        for (var E = a.length, B = h + (x ? 1 : -1); x ? B-- : ++B < E; )
          if (g(a[B], B, a))
            return B;
        return -1;
      }
      function yt(a, g, h) {
        return g === g ? Xs(a, g, h) : me(a, au, h);
      }
      function Fs(a, g, h, x) {
        for (var E = h - 1, B = a.length; ++E < B; )
          if (x(a[E], g))
            return E;
        return -1;
      }
      function au(a) {
        return a !== a;
      }
      function cu(a, g) {
        var h = a == null ? 0 : a.length;
        return h ? Or(a, g) / h : ce;
      }
      function Ir(a) {
        return function(g) {
          return g == null ? o : g[a];
        };
      }
      function Rr(a) {
        return function(g) {
          return a == null ? o : a[g];
        };
      }
      function hu(a, g, h, x, E) {
        return E(a, function(B, Y, F) {
          h = x ? (x = !1, B) : g(h, B, Y, F);
        }), h;
      }
      function Hs(a, g) {
        var h = a.length;
        for (a.sort(g); h--; )
          a[h] = a[h].value;
        return a;
      }
      function Or(a, g) {
        for (var h, x = -1, E = a.length; ++x < E; ) {
          var B = g(a[x]);
          B !== o && (h = h === o ? B : h + B);
        }
        return h;
      }
      function Mr(a, g) {
        for (var h = -1, x = Array(a); ++h < a; )
          x[h] = g(h);
        return x;
      }
      function Ns(a, g) {
        return $(g, function(h) {
          return [h, a[h]];
        });
      }
      function gu(a) {
        return a && a.slice(0, vu(a) + 1).replace(vr, "");
      }
      function an(a) {
        return function(g) {
          return a(g);
        };
      }
      function br(a, g) {
        return $(g, function(h) {
          return a[h];
        });
      }
      function kt(a, g) {
        return a.has(g);
      }
      function pu(a, g) {
        for (var h = -1, x = a.length; ++h < x && yt(g, a[h], 0) > -1; )
          ;
        return h;
      }
      function du(a, g) {
        for (var h = a.length; h-- && yt(g, a[h], 0) > -1; )
          ;
        return h;
      }
      function zs(a, g) {
        for (var h = a.length, x = 0; h--; )
          a[h] === g && ++x;
        return x;
      }
      var Gs = Rr(Ts), $s = Rr(Es);
      function qs(a) {
        return "\\" + Is[a];
      }
      function Ks(a, g) {
        return a == null ? o : a[g];
      }
      function Ct(a) {
        return As.test(a);
      }
      function ks(a) {
        return Ss.test(a);
      }
      function Zs(a) {
        for (var g, h = []; !(g = a.next()).done; )
          h.push(g.value);
        return h;
      }
      function Wr(a) {
        var g = -1, h = Array(a.size);
        return a.forEach(function(x, E) {
          h[++g] = [E, x];
        }), h;
      }
      function _u(a, g) {
        return function(h) {
          return a(g(h));
        };
      }
      function Qn(a, g) {
        for (var h = -1, x = a.length, E = 0, B = []; ++h < x; ) {
          var Y = a[h];
          (Y === g || Y === dt) && (a[h] = dt, B[E++] = h);
        }
        return B;
      }
      function Ae(a) {
        var g = -1, h = Array(a.size);
        return a.forEach(function(x) {
          h[++g] = x;
        }), h;
      }
      function Ys(a) {
        var g = -1, h = Array(a.size);
        return a.forEach(function(x) {
          h[++g] = [x, x];
        }), h;
      }
      function Xs(a, g, h) {
        for (var x = h - 1, E = a.length; ++x < E; )
          if (a[x] === g)
            return x;
        return -1;
      }
      function Js(a, g, h) {
        for (var x = h + 1; x--; )
          if (a[x] === g)
            return x;
        return x;
      }
      function Tt(a) {
        return Ct(a) ? Qs(a) : Bs(a);
      }
      function Ln(a) {
        return Ct(a) ? js(a) : Us(a);
      }
      function vu(a) {
        for (var g = a.length; g-- && zo.test(a.charAt(g)); )
          ;
        return g;
      }
      var Vs = Rr(Ls);
      function Qs(a) {
        for (var g = Sr.lastIndex = 0; Sr.test(a); )
          ++g;
        return g;
      }
      function js(a) {
        return a.match(Sr) || [];
      }
      function nl(a) {
        return a.match(ms) || [];
      }
      var tl = function a(g) {
        g = g == null ? V : Et.defaults(V.Object(), g, Et.pick(V, ys));
        var h = g.Array, x = g.Date, E = g.Error, B = g.Function, Y = g.Math, F = g.Object, Dr = g.RegExp, el = g.String, wn = g.TypeError, Se = h.prototype, rl = B.prototype, Lt = F.prototype, ye = g["__core-js_shared__"], Ce = rl.toString, P = Lt.hasOwnProperty, il = 0, wu = function() {
          var n = /[^.]+$/.exec(ye && ye.keys && ye.keys.IE_PROTO || "");
          return n ? "Symbol(src)_1." + n : "";
        }(), Te = Lt.toString, ul = Ce.call(F), fl = V._, ol = Dr(
          "^" + Ce.call(P).replace(_r, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
        ), Ee = tu ? g.Buffer : o, jn = g.Symbol, Le = g.Uint8Array, xu = Ee ? Ee.allocUnsafe : o, Ie = _u(F.getPrototypeOf, F), mu = F.create, Au = Lt.propertyIsEnumerable, Re = Se.splice, Su = jn ? jn.isConcatSpreadable : o, Zt = jn ? jn.iterator : o, ot = jn ? jn.toStringTag : o, Oe = function() {
          try {
            var n = ht(F, "defineProperty");
            return n({}, "", {}), n;
          } catch {
          }
        }(), sl = g.clearTimeout !== V.clearTimeout && g.clearTimeout, ll = x && x.now !== V.Date.now && x.now, al = g.setTimeout !== V.setTimeout && g.setTimeout, Me = Y.ceil, be = Y.floor, Br = F.getOwnPropertySymbols, cl = Ee ? Ee.isBuffer : o, yu = g.isFinite, hl = Se.join, gl = _u(F.keys, F), X = Y.max, j = Y.min, pl = x.now, dl = g.parseInt, Cu = Y.random, _l = Se.reverse, Ur = ht(g, "DataView"), Yt = ht(g, "Map"), Pr = ht(g, "Promise"), It = ht(g, "Set"), Xt = ht(g, "WeakMap"), Jt = ht(F, "create"), We = Xt && new Xt(), Rt = {}, vl = gt(Ur), wl = gt(Yt), xl = gt(Pr), ml = gt(It), Al = gt(Xt), De = jn ? jn.prototype : o, Vt = De ? De.valueOf : o, Tu = De ? De.toString : o;
        function u(n) {
          if (K(n) && !L(n) && !(n instanceof W)) {
            if (n instanceof xn)
              return n;
            if (P.call(n, "__wrapped__"))
              return Lf(n);
          }
          return new xn(n);
        }
        var Ot = /* @__PURE__ */ function() {
          function n() {
          }
          return function(t) {
            if (!q(t))
              return {};
            if (mu)
              return mu(t);
            n.prototype = t;
            var e = new n();
            return n.prototype = o, e;
          };
        }();
        function Be() {
        }
        function xn(n, t) {
          this.__wrapped__ = n, this.__actions__ = [], this.__chain__ = !!t, this.__index__ = 0, this.__values__ = o;
        }
        u.templateSettings = {
          /**
           * Used to detect `data` property values to be HTML-escaped.
           *
           * @memberOf _.templateSettings
           * @type {RegExp}
           */
          escape: Bo,
          /**
           * Used to detect code to be evaluated.
           *
           * @memberOf _.templateSettings
           * @type {RegExp}
           */
          evaluate: Uo,
          /**
           * Used to detect `data` property values to inject.
           *
           * @memberOf _.templateSettings
           * @type {RegExp}
           */
          interpolate: Di,
          /**
           * Used to reference the data object in the template text.
           *
           * @memberOf _.templateSettings
           * @type {string}
           */
          variable: "",
          /**
           * Used to import variables into the compiled template.
           *
           * @memberOf _.templateSettings
           * @type {Object}
           */
          imports: {
            /**
             * A reference to the `lodash` function.
             *
             * @memberOf _.templateSettings.imports
             * @type {Function}
             */
            _: u
          }
        }, u.prototype = Be.prototype, u.prototype.constructor = u, xn.prototype = Ot(Be.prototype), xn.prototype.constructor = xn;
        function W(n) {
          this.__wrapped__ = n, this.__actions__ = [], this.__dir__ = 1, this.__filtered__ = !1, this.__iteratees__ = [], this.__takeCount__ = bn, this.__views__ = [];
        }
        function Sl() {
          var n = new W(this.__wrapped__);
          return n.__actions__ = un(this.__actions__), n.__dir__ = this.__dir__, n.__filtered__ = this.__filtered__, n.__iteratees__ = un(this.__iteratees__), n.__takeCount__ = this.__takeCount__, n.__views__ = un(this.__views__), n;
        }
        function yl() {
          if (this.__filtered__) {
            var n = new W(this);
            n.__dir__ = -1, n.__filtered__ = !0;
          } else
            n = this.clone(), n.__dir__ *= -1;
          return n;
        }
        function Cl() {
          var n = this.__wrapped__.value(), t = this.__dir__, e = L(n), r = t < 0, i = e ? n.length : 0, f = Ua(0, i, this.__views__), s = f.start, l = f.end, c = l - s, p = r ? l : s - 1, d = this.__iteratees__, _ = d.length, w = 0, A = j(c, this.__takeCount__);
          if (!e || !r && i == c && A == c)
            return Xu(n, this.__actions__);
          var y = [];
          n:
            for (; c-- && w < A; ) {
              p += t;
              for (var R = -1, C = n[p]; ++R < _; ) {
                var M = d[R], D = M.iteratee, gn = M.type, rn = D(C);
                if (gn == wo)
                  C = rn;
                else if (!rn) {
                  if (gn == Ri)
                    continue n;
                  break n;
                }
              }
              y[w++] = C;
            }
          return y;
        }
        W.prototype = Ot(Be.prototype), W.prototype.constructor = W;
        function st(n) {
          var t = -1, e = n == null ? 0 : n.length;
          for (this.clear(); ++t < e; ) {
            var r = n[t];
            this.set(r[0], r[1]);
          }
        }
        function Tl() {
          this.__data__ = Jt ? Jt(null) : {}, this.size = 0;
        }
        function El(n) {
          var t = this.has(n) && delete this.__data__[n];
          return this.size -= t ? 1 : 0, t;
        }
        function Ll(n) {
          var t = this.__data__;
          if (Jt) {
            var e = t[n];
            return e === Xn ? o : e;
          }
          return P.call(t, n) ? t[n] : o;
        }
        function Il(n) {
          var t = this.__data__;
          return Jt ? t[n] !== o : P.call(t, n);
        }
        function Rl(n, t) {
          var e = this.__data__;
          return this.size += this.has(n) ? 0 : 1, e[n] = Jt && t === o ? Xn : t, this;
        }
        st.prototype.clear = Tl, st.prototype.delete = El, st.prototype.get = Ll, st.prototype.has = Il, st.prototype.set = Rl;
        function Nn(n) {
          var t = -1, e = n == null ? 0 : n.length;
          for (this.clear(); ++t < e; ) {
            var r = n[t];
            this.set(r[0], r[1]);
          }
        }
        function Ol() {
          this.__data__ = [], this.size = 0;
        }
        function Ml(n) {
          var t = this.__data__, e = Ue(t, n);
          if (e < 0)
            return !1;
          var r = t.length - 1;
          return e == r ? t.pop() : Re.call(t, e, 1), --this.size, !0;
        }
        function bl(n) {
          var t = this.__data__, e = Ue(t, n);
          return e < 0 ? o : t[e][1];
        }
        function Wl(n) {
          return Ue(this.__data__, n) > -1;
        }
        function Dl(n, t) {
          var e = this.__data__, r = Ue(e, n);
          return r < 0 ? (++this.size, e.push([n, t])) : e[r][1] = t, this;
        }
        Nn.prototype.clear = Ol, Nn.prototype.delete = Ml, Nn.prototype.get = bl, Nn.prototype.has = Wl, Nn.prototype.set = Dl;
        function zn(n) {
          var t = -1, e = n == null ? 0 : n.length;
          for (this.clear(); ++t < e; ) {
            var r = n[t];
            this.set(r[0], r[1]);
          }
        }
        function Bl() {
          this.size = 0, this.__data__ = {
            hash: new st(),
            map: new (Yt || Nn)(),
            string: new st()
          };
        }
        function Ul(n) {
          var t = Ye(this, n).delete(n);
          return this.size -= t ? 1 : 0, t;
        }
        function Pl(n) {
          return Ye(this, n).get(n);
        }
        function Fl(n) {
          return Ye(this, n).has(n);
        }
        function Hl(n, t) {
          var e = Ye(this, n), r = e.size;
          return e.set(n, t), this.size += e.size == r ? 0 : 1, this;
        }
        zn.prototype.clear = Bl, zn.prototype.delete = Ul, zn.prototype.get = Pl, zn.prototype.has = Fl, zn.prototype.set = Hl;
        function lt(n) {
          var t = -1, e = n == null ? 0 : n.length;
          for (this.__data__ = new zn(); ++t < e; )
            this.add(n[t]);
        }
        function Nl(n) {
          return this.__data__.set(n, Xn), this;
        }
        function zl(n) {
          return this.__data__.has(n);
        }
        lt.prototype.add = lt.prototype.push = Nl, lt.prototype.has = zl;
        function In(n) {
          var t = this.__data__ = new Nn(n);
          this.size = t.size;
        }
        function Gl() {
          this.__data__ = new Nn(), this.size = 0;
        }
        function $l(n) {
          var t = this.__data__, e = t.delete(n);
          return this.size = t.size, e;
        }
        function ql(n) {
          return this.__data__.get(n);
        }
        function Kl(n) {
          return this.__data__.has(n);
        }
        function kl(n, t) {
          var e = this.__data__;
          if (e instanceof Nn) {
            var r = e.__data__;
            if (!Yt || r.length < b - 1)
              return r.push([n, t]), this.size = ++e.size, this;
            e = this.__data__ = new zn(r);
          }
          return e.set(n, t), this.size = e.size, this;
        }
        In.prototype.clear = Gl, In.prototype.delete = $l, In.prototype.get = ql, In.prototype.has = Kl, In.prototype.set = kl;
        function Eu(n, t) {
          var e = L(n), r = !e && pt(n), i = !e && !r && it(n), f = !e && !r && !i && Dt(n), s = e || r || i || f, l = s ? Mr(n.length, el) : [], c = l.length;
          for (var p in n)
            (t || P.call(n, p)) && !(s && // Safari 9 has enumerable `arguments.length` in strict mode.
            (p == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
            i && (p == "offset" || p == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
            f && (p == "buffer" || p == "byteLength" || p == "byteOffset") || // Skip index properties.
            Kn(p, c))) && l.push(p);
          return l;
        }
        function Lu(n) {
          var t = n.length;
          return t ? n[Yr(0, t - 1)] : o;
        }
        function Zl(n, t) {
          return Xe(un(n), at(t, 0, n.length));
        }
        function Yl(n) {
          return Xe(un(n));
        }
        function Fr(n, t, e) {
          (e !== o && !Rn(n[t], e) || e === o && !(t in n)) && Gn(n, t, e);
        }
        function Qt(n, t, e) {
          var r = n[t];
          (!(P.call(n, t) && Rn(r, e)) || e === o && !(t in n)) && Gn(n, t, e);
        }
        function Ue(n, t) {
          for (var e = n.length; e--; )
            if (Rn(n[e][0], t))
              return e;
          return -1;
        }
        function Xl(n, t, e, r) {
          return nt(n, function(i, f, s) {
            t(r, i, e(i), s);
          }), r;
        }
        function Iu(n, t) {
          return n && Dn(t, J(t), n);
        }
        function Jl(n, t) {
          return n && Dn(t, on(t), n);
        }
        function Gn(n, t, e) {
          t == "__proto__" && Oe ? Oe(n, t, {
            configurable: !0,
            enumerable: !0,
            value: e,
            writable: !0
          }) : n[t] = e;
        }
        function Hr(n, t) {
          for (var e = -1, r = t.length, i = h(r), f = n == null; ++e < r; )
            i[e] = f ? o : xi(n, t[e]);
          return i;
        }
        function at(n, t, e) {
          return n === n && (e !== o && (n = n <= e ? n : e), t !== o && (n = n >= t ? n : t)), n;
        }
        function mn(n, t, e, r, i, f) {
          var s, l = t & Mn, c = t & se, p = t & _t;
          if (e && (s = i ? e(n, r, i, f) : e(n)), s !== o)
            return s;
          if (!q(n))
            return n;
          var d = L(n);
          if (d) {
            if (s = Fa(n), !l)
              return un(n, s);
          } else {
            var _ = nn(n), w = _ == pe || _ == Oi;
            if (it(n))
              return Qu(n, l);
            if (_ == Hn || _ == mt || w && !i) {
              if (s = c || w ? {} : wf(n), !l)
                return c ? La(n, Jl(s, n)) : Ea(n, Iu(s, n));
            } else {
              if (!H[_])
                return i ? n : {};
              s = Ha(n, _, l);
            }
          }
          f || (f = new In());
          var A = f.get(n);
          if (A)
            return A;
          f.set(n, s), Zf(n) ? n.forEach(function(C) {
            s.add(mn(C, t, e, C, n, f));
          }) : Kf(n) && n.forEach(function(C, M) {
            s.set(M, mn(C, t, e, M, n, f));
          });
          var y = p ? c ? ui : ii : c ? on : J, R = d ? o : y(n);
          return vn(R || n, function(C, M) {
            R && (M = C, C = n[M]), Qt(s, M, mn(C, t, e, M, n, f));
          }), s;
        }
        function Vl(n) {
          var t = J(n);
          return function(e) {
            return Ru(e, n, t);
          };
        }
        function Ru(n, t, e) {
          var r = e.length;
          if (n == null)
            return !r;
          for (n = F(n); r--; ) {
            var i = e[r], f = t[i], s = n[i];
            if (s === o && !(i in n) || !f(s))
              return !1;
          }
          return !0;
        }
        function Ou(n, t, e) {
          if (typeof n != "function")
            throw new wn(T);
          return ue(function() {
            n.apply(o, e);
          }, t);
        }
        function jt(n, t, e, r) {
          var i = -1, f = xe, s = !0, l = n.length, c = [], p = t.length;
          if (!l)
            return c;
          e && (t = $(t, an(e))), r ? (f = Tr, s = !1) : t.length >= b && (f = kt, s = !1, t = new lt(t));
          n:
            for (; ++i < l; ) {
              var d = n[i], _ = e == null ? d : e(d);
              if (d = r || d !== 0 ? d : 0, s && _ === _) {
                for (var w = p; w--; )
                  if (t[w] === _)
                    continue n;
                c.push(d);
              } else f(t, _, r) || c.push(d);
            }
          return c;
        }
        var nt = rf(Wn), Mu = rf(zr, !0);
        function Ql(n, t) {
          var e = !0;
          return nt(n, function(r, i, f) {
            return e = !!t(r, i, f), e;
          }), e;
        }
        function Pe(n, t, e) {
          for (var r = -1, i = n.length; ++r < i; ) {
            var f = n[r], s = t(f);
            if (s != null && (l === o ? s === s && !hn(s) : e(s, l)))
              var l = s, c = f;
          }
          return c;
        }
        function jl(n, t, e, r) {
          var i = n.length;
          for (e = I(e), e < 0 && (e = -e > i ? 0 : i + e), r = r === o || r > i ? i : I(r), r < 0 && (r += i), r = e > r ? 0 : Xf(r); e < r; )
            n[e++] = t;
          return n;
        }
        function bu(n, t) {
          var e = [];
          return nt(n, function(r, i, f) {
            t(r, i, f) && e.push(r);
          }), e;
        }
        function Q(n, t, e, r, i) {
          var f = -1, s = n.length;
          for (e || (e = za), i || (i = []); ++f < s; ) {
            var l = n[f];
            t > 0 && e(l) ? t > 1 ? Q(l, t - 1, e, r, i) : Vn(i, l) : r || (i[i.length] = l);
          }
          return i;
        }
        var Nr = uf(), Wu = uf(!0);
        function Wn(n, t) {
          return n && Nr(n, t, J);
        }
        function zr(n, t) {
          return n && Wu(n, t, J);
        }
        function Fe(n, t) {
          return Jn(t, function(e) {
            return kn(n[e]);
          });
        }
        function ct(n, t) {
          t = et(t, n);
          for (var e = 0, r = t.length; n != null && e < r; )
            n = n[Bn(t[e++])];
          return e && e == r ? n : o;
        }
        function Du(n, t, e) {
          var r = t(n);
          return L(n) ? r : Vn(r, e(n));
        }
        function tn(n) {
          return n == null ? n === o ? Io : Eo : ot && ot in F(n) ? Ba(n) : Ya(n);
        }
        function Gr(n, t) {
          return n > t;
        }
        function na(n, t) {
          return n != null && P.call(n, t);
        }
        function ta(n, t) {
          return n != null && t in F(n);
        }
        function ea(n, t, e) {
          return n >= j(t, e) && n < X(t, e);
        }
        function $r(n, t, e) {
          for (var r = e ? Tr : xe, i = n[0].length, f = n.length, s = f, l = h(f), c = 1 / 0, p = []; s--; ) {
            var d = n[s];
            s && t && (d = $(d, an(t))), c = j(d.length, c), l[s] = !e && (t || i >= 120 && d.length >= 120) ? new lt(s && d) : o;
          }
          d = n[0];
          var _ = -1, w = l[0];
          n:
            for (; ++_ < i && p.length < c; ) {
              var A = d[_], y = t ? t(A) : A;
              if (A = e || A !== 0 ? A : 0, !(w ? kt(w, y) : r(p, y, e))) {
                for (s = f; --s; ) {
                  var R = l[s];
                  if (!(R ? kt(R, y) : r(n[s], y, e)))
                    continue n;
                }
                w && w.push(y), p.push(A);
              }
            }
          return p;
        }
        function ra(n, t, e, r) {
          return Wn(n, function(i, f, s) {
            t(r, e(i), f, s);
          }), r;
        }
        function ne(n, t, e) {
          t = et(t, n), n = Sf(n, t);
          var r = n == null ? n : n[Bn(Sn(t))];
          return r == null ? o : ln(r, n, e);
        }
        function Bu(n) {
          return K(n) && tn(n) == mt;
        }
        function ia(n) {
          return K(n) && tn(n) == Kt;
        }
        function ua(n) {
          return K(n) && tn(n) == Nt;
        }
        function te(n, t, e, r, i) {
          return n === t ? !0 : n == null || t == null || !K(n) && !K(t) ? n !== n && t !== t : fa(n, t, e, r, te, i);
        }
        function fa(n, t, e, r, i, f) {
          var s = L(n), l = L(t), c = s ? he : nn(n), p = l ? he : nn(t);
          c = c == mt ? Hn : c, p = p == mt ? Hn : p;
          var d = c == Hn, _ = p == Hn, w = c == p;
          if (w && it(n)) {
            if (!it(t))
              return !1;
            s = !0, d = !1;
          }
          if (w && !d)
            return f || (f = new In()), s || Dt(n) ? df(n, t, e, r, i, f) : Wa(n, t, c, e, r, i, f);
          if (!(e & vt)) {
            var A = d && P.call(n, "__wrapped__"), y = _ && P.call(t, "__wrapped__");
            if (A || y) {
              var R = A ? n.value() : n, C = y ? t.value() : t;
              return f || (f = new In()), i(R, C, e, r, f);
            }
          }
          return w ? (f || (f = new In()), Da(n, t, e, r, i, f)) : !1;
        }
        function oa(n) {
          return K(n) && nn(n) == Tn;
        }
        function qr(n, t, e, r) {
          var i = e.length, f = i, s = !r;
          if (n == null)
            return !f;
          for (n = F(n); i--; ) {
            var l = e[i];
            if (s && l[2] ? l[1] !== n[l[0]] : !(l[0] in n))
              return !1;
          }
          for (; ++i < f; ) {
            l = e[i];
            var c = l[0], p = n[c], d = l[1];
            if (s && l[2]) {
              if (p === o && !(c in n))
                return !1;
            } else {
              var _ = new In();
              if (r)
                var w = r(p, d, c, n, t, _);
              if (!(w === o ? te(d, p, vt | le, r, _) : w))
                return !1;
            }
          }
          return !0;
        }
        function Uu(n) {
          if (!q(n) || $a(n))
            return !1;
          var t = kn(n) ? ol : Vo;
          return t.test(gt(n));
        }
        function sa(n) {
          return K(n) && tn(n) == Gt;
        }
        function la(n) {
          return K(n) && nn(n) == En;
        }
        function aa(n) {
          return K(n) && tr(n.length) && !!N[tn(n)];
        }
        function Pu(n) {
          return typeof n == "function" ? n : n == null ? sn : typeof n == "object" ? L(n) ? Nu(n[0], n[1]) : Hu(n) : fo(n);
        }
        function Kr(n) {
          if (!ie(n))
            return gl(n);
          var t = [];
          for (var e in F(n))
            P.call(n, e) && e != "constructor" && t.push(e);
          return t;
        }
        function ca(n) {
          if (!q(n))
            return Za(n);
          var t = ie(n), e = [];
          for (var r in n)
            r == "constructor" && (t || !P.call(n, r)) || e.push(r);
          return e;
        }
        function kr(n, t) {
          return n < t;
        }
        function Fu(n, t) {
          var e = -1, r = fn(n) ? h(n.length) : [];
          return nt(n, function(i, f, s) {
            r[++e] = t(i, f, s);
          }), r;
        }
        function Hu(n) {
          var t = oi(n);
          return t.length == 1 && t[0][2] ? mf(t[0][0], t[0][1]) : function(e) {
            return e === n || qr(e, n, t);
          };
        }
        function Nu(n, t) {
          return li(n) && xf(t) ? mf(Bn(n), t) : function(e) {
            var r = xi(e, n);
            return r === o && r === t ? mi(e, n) : te(t, r, vt | le);
          };
        }
        function He(n, t, e, r, i) {
          n !== t && Nr(t, function(f, s) {
            if (i || (i = new In()), q(f))
              ha(n, t, s, e, He, r, i);
            else {
              var l = r ? r(ci(n, s), f, s + "", n, t, i) : o;
              l === o && (l = f), Fr(n, s, l);
            }
          }, on);
        }
        function ha(n, t, e, r, i, f, s) {
          var l = ci(n, e), c = ci(t, e), p = s.get(c);
          if (p) {
            Fr(n, e, p);
            return;
          }
          var d = f ? f(l, c, e + "", n, t, s) : o, _ = d === o;
          if (_) {
            var w = L(c), A = !w && it(c), y = !w && !A && Dt(c);
            d = c, w || A || y ? L(l) ? d = l : k(l) ? d = un(l) : A ? (_ = !1, d = Qu(c, !0)) : y ? (_ = !1, d = ju(c, !0)) : d = [] : fe(c) || pt(c) ? (d = l, pt(l) ? d = Jf(l) : (!q(l) || kn(l)) && (d = wf(c))) : _ = !1;
          }
          _ && (s.set(c, d), i(d, c, r, f, s), s.delete(c)), Fr(n, e, d);
        }
        function zu(n, t) {
          var e = n.length;
          if (e)
            return t += t < 0 ? e : 0, Kn(t, e) ? n[t] : o;
        }
        function Gu(n, t, e) {
          t.length ? t = $(t, function(f) {
            return L(f) ? function(s) {
              return ct(s, f.length === 1 ? f[0] : f);
            } : f;
          }) : t = [sn];
          var r = -1;
          t = $(t, an(S()));
          var i = Fu(n, function(f, s, l) {
            var c = $(t, function(p) {
              return p(f);
            });
            return { criteria: c, index: ++r, value: f };
          });
          return Hs(i, function(f, s) {
            return Ta(f, s, e);
          });
        }
        function ga(n, t) {
          return $u(n, t, function(e, r) {
            return mi(n, r);
          });
        }
        function $u(n, t, e) {
          for (var r = -1, i = t.length, f = {}; ++r < i; ) {
            var s = t[r], l = ct(n, s);
            e(l, s) && ee(f, et(s, n), l);
          }
          return f;
        }
        function pa(n) {
          return function(t) {
            return ct(t, n);
          };
        }
        function Zr(n, t, e, r) {
          var i = r ? Fs : yt, f = -1, s = t.length, l = n;
          for (n === t && (t = un(t)), e && (l = $(n, an(e))); ++f < s; )
            for (var c = 0, p = t[f], d = e ? e(p) : p; (c = i(l, d, c, r)) > -1; )
              l !== n && Re.call(l, c, 1), Re.call(n, c, 1);
          return n;
        }
        function qu(n, t) {
          for (var e = n ? t.length : 0, r = e - 1; e--; ) {
            var i = t[e];
            if (e == r || i !== f) {
              var f = i;
              Kn(i) ? Re.call(n, i, 1) : Vr(n, i);
            }
          }
          return n;
        }
        function Yr(n, t) {
          return n + be(Cu() * (t - n + 1));
        }
        function da(n, t, e, r) {
          for (var i = -1, f = X(Me((t - n) / (e || 1)), 0), s = h(f); f--; )
            s[r ? f : ++i] = n, n += e;
          return s;
        }
        function Xr(n, t) {
          var e = "";
          if (!n || t < 1 || t > xt)
            return e;
          do
            t % 2 && (e += n), t = be(t / 2), t && (n += n);
          while (t);
          return e;
        }
        function O(n, t) {
          return hi(Af(n, t, sn), n + "");
        }
        function _a(n) {
          return Lu(Bt(n));
        }
        function va(n, t) {
          var e = Bt(n);
          return Xe(e, at(t, 0, e.length));
        }
        function ee(n, t, e, r) {
          if (!q(n))
            return n;
          t = et(t, n);
          for (var i = -1, f = t.length, s = f - 1, l = n; l != null && ++i < f; ) {
            var c = Bn(t[i]), p = e;
            if (c === "__proto__" || c === "constructor" || c === "prototype")
              return n;
            if (i != s) {
              var d = l[c];
              p = r ? r(d, c, l) : o, p === o && (p = q(d) ? d : Kn(t[i + 1]) ? [] : {});
            }
            Qt(l, c, p), l = l[c];
          }
          return n;
        }
        var Ku = We ? function(n, t) {
          return We.set(n, t), n;
        } : sn, wa = Oe ? function(n, t) {
          return Oe(n, "toString", {
            configurable: !0,
            enumerable: !1,
            value: Si(t),
            writable: !0
          });
        } : sn;
        function xa(n) {
          return Xe(Bt(n));
        }
        function An(n, t, e) {
          var r = -1, i = n.length;
          t < 0 && (t = -t > i ? 0 : i + t), e = e > i ? i : e, e < 0 && (e += i), i = t > e ? 0 : e - t >>> 0, t >>>= 0;
          for (var f = h(i); ++r < i; )
            f[r] = n[r + t];
          return f;
        }
        function ma(n, t) {
          var e;
          return nt(n, function(r, i, f) {
            return e = t(r, i, f), !e;
          }), !!e;
        }
        function Ne(n, t, e) {
          var r = 0, i = n == null ? r : n.length;
          if (typeof t == "number" && t === t && i <= So) {
            for (; r < i; ) {
              var f = r + i >>> 1, s = n[f];
              s !== null && !hn(s) && (e ? s <= t : s < t) ? r = f + 1 : i = f;
            }
            return i;
          }
          return Jr(n, t, sn, e);
        }
        function Jr(n, t, e, r) {
          var i = 0, f = n == null ? 0 : n.length;
          if (f === 0)
            return 0;
          t = e(t);
          for (var s = t !== t, l = t === null, c = hn(t), p = t === o; i < f; ) {
            var d = be((i + f) / 2), _ = e(n[d]), w = _ !== o, A = _ === null, y = _ === _, R = hn(_);
            if (s)
              var C = r || y;
            else p ? C = y && (r || w) : l ? C = y && w && (r || !A) : c ? C = y && w && !A && (r || !R) : A || R ? C = !1 : C = r ? _ <= t : _ < t;
            C ? i = d + 1 : f = d;
          }
          return j(f, Ao);
        }
        function ku(n, t) {
          for (var e = -1, r = n.length, i = 0, f = []; ++e < r; ) {
            var s = n[e], l = t ? t(s) : s;
            if (!e || !Rn(l, c)) {
              var c = l;
              f[i++] = s === 0 ? 0 : s;
            }
          }
          return f;
        }
        function Zu(n) {
          return typeof n == "number" ? n : hn(n) ? ce : +n;
        }
        function cn(n) {
          if (typeof n == "string")
            return n;
          if (L(n))
            return $(n, cn) + "";
          if (hn(n))
            return Tu ? Tu.call(n) : "";
          var t = n + "";
          return t == "0" && 1 / n == -1 / 0 ? "-0" : t;
        }
        function tt(n, t, e) {
          var r = -1, i = xe, f = n.length, s = !0, l = [], c = l;
          if (e)
            s = !1, i = Tr;
          else if (f >= b) {
            var p = t ? null : Ma(n);
            if (p)
              return Ae(p);
            s = !1, i = kt, c = new lt();
          } else
            c = t ? [] : l;
          n:
            for (; ++r < f; ) {
              var d = n[r], _ = t ? t(d) : d;
              if (d = e || d !== 0 ? d : 0, s && _ === _) {
                for (var w = c.length; w--; )
                  if (c[w] === _)
                    continue n;
                t && c.push(_), l.push(d);
              } else i(c, _, e) || (c !== l && c.push(_), l.push(d));
            }
          return l;
        }
        function Vr(n, t) {
          return t = et(t, n), n = Sf(n, t), n == null || delete n[Bn(Sn(t))];
        }
        function Yu(n, t, e, r) {
          return ee(n, t, e(ct(n, t)), r);
        }
        function ze(n, t, e, r) {
          for (var i = n.length, f = r ? i : -1; (r ? f-- : ++f < i) && t(n[f], f, n); )
            ;
          return e ? An(n, r ? 0 : f, r ? f + 1 : i) : An(n, r ? f + 1 : 0, r ? i : f);
        }
        function Xu(n, t) {
          var e = n;
          return e instanceof W && (e = e.value()), Er(t, function(r, i) {
            return i.func.apply(i.thisArg, Vn([r], i.args));
          }, e);
        }
        function Qr(n, t, e) {
          var r = n.length;
          if (r < 2)
            return r ? tt(n[0]) : [];
          for (var i = -1, f = h(r); ++i < r; )
            for (var s = n[i], l = -1; ++l < r; )
              l != i && (f[i] = jt(f[i] || s, n[l], t, e));
          return tt(Q(f, 1), t, e);
        }
        function Ju(n, t, e) {
          for (var r = -1, i = n.length, f = t.length, s = {}; ++r < i; ) {
            var l = r < f ? t[r] : o;
            e(s, n[r], l);
          }
          return s;
        }
        function jr(n) {
          return k(n) ? n : [];
        }
        function ni(n) {
          return typeof n == "function" ? n : sn;
        }
        function et(n, t) {
          return L(n) ? n : li(n, t) ? [n] : Ef(U(n));
        }
        var Aa = O;
        function rt(n, t, e) {
          var r = n.length;
          return e = e === o ? r : e, !t && e >= r ? n : An(n, t, e);
        }
        var Vu = sl || function(n) {
          return V.clearTimeout(n);
        };
        function Qu(n, t) {
          if (t)
            return n.slice();
          var e = n.length, r = xu ? xu(e) : new n.constructor(e);
          return n.copy(r), r;
        }
        function ti(n) {
          var t = new n.constructor(n.byteLength);
          return new Le(t).set(new Le(n)), t;
        }
        function Sa(n, t) {
          var e = t ? ti(n.buffer) : n.buffer;
          return new n.constructor(e, n.byteOffset, n.byteLength);
        }
        function ya(n) {
          var t = new n.constructor(n.source, Bi.exec(n));
          return t.lastIndex = n.lastIndex, t;
        }
        function Ca(n) {
          return Vt ? F(Vt.call(n)) : {};
        }
        function ju(n, t) {
          var e = t ? ti(n.buffer) : n.buffer;
          return new n.constructor(e, n.byteOffset, n.length);
        }
        function nf(n, t) {
          if (n !== t) {
            var e = n !== o, r = n === null, i = n === n, f = hn(n), s = t !== o, l = t === null, c = t === t, p = hn(t);
            if (!l && !p && !f && n > t || f && s && c && !l && !p || r && s && c || !e && c || !i)
              return 1;
            if (!r && !f && !p && n < t || p && e && i && !r && !f || l && e && i || !s && i || !c)
              return -1;
          }
          return 0;
        }
        function Ta(n, t, e) {
          for (var r = -1, i = n.criteria, f = t.criteria, s = i.length, l = e.length; ++r < s; ) {
            var c = nf(i[r], f[r]);
            if (c) {
              if (r >= l)
                return c;
              var p = e[r];
              return c * (p == "desc" ? -1 : 1);
            }
          }
          return n.index - t.index;
        }
        function tf(n, t, e, r) {
          for (var i = -1, f = n.length, s = e.length, l = -1, c = t.length, p = X(f - s, 0), d = h(c + p), _ = !r; ++l < c; )
            d[l] = t[l];
          for (; ++i < s; )
            (_ || i < f) && (d[e[i]] = n[i]);
          for (; p--; )
            d[l++] = n[i++];
          return d;
        }
        function ef(n, t, e, r) {
          for (var i = -1, f = n.length, s = -1, l = e.length, c = -1, p = t.length, d = X(f - l, 0), _ = h(d + p), w = !r; ++i < d; )
            _[i] = n[i];
          for (var A = i; ++c < p; )
            _[A + c] = t[c];
          for (; ++s < l; )
            (w || i < f) && (_[A + e[s]] = n[i++]);
          return _;
        }
        function un(n, t) {
          var e = -1, r = n.length;
          for (t || (t = h(r)); ++e < r; )
            t[e] = n[e];
          return t;
        }
        function Dn(n, t, e, r) {
          var i = !e;
          e || (e = {});
          for (var f = -1, s = t.length; ++f < s; ) {
            var l = t[f], c = r ? r(e[l], n[l], l, e, n) : o;
            c === o && (c = n[l]), i ? Gn(e, l, c) : Qt(e, l, c);
          }
          return e;
        }
        function Ea(n, t) {
          return Dn(n, si(n), t);
        }
        function La(n, t) {
          return Dn(n, _f(n), t);
        }
        function Ge(n, t) {
          return function(e, r) {
            var i = L(e) ? bs : Xl, f = t ? t() : {};
            return i(e, n, S(r, 2), f);
          };
        }
        function Mt(n) {
          return O(function(t, e) {
            var r = -1, i = e.length, f = i > 1 ? e[i - 1] : o, s = i > 2 ? e[2] : o;
            for (f = n.length > 3 && typeof f == "function" ? (i--, f) : o, s && en(e[0], e[1], s) && (f = i < 3 ? o : f, i = 1), t = F(t); ++r < i; ) {
              var l = e[r];
              l && n(t, l, r, f);
            }
            return t;
          });
        }
        function rf(n, t) {
          return function(e, r) {
            if (e == null)
              return e;
            if (!fn(e))
              return n(e, r);
            for (var i = e.length, f = t ? i : -1, s = F(e); (t ? f-- : ++f < i) && r(s[f], f, s) !== !1; )
              ;
            return e;
          };
        }
        function uf(n) {
          return function(t, e, r) {
            for (var i = -1, f = F(t), s = r(t), l = s.length; l--; ) {
              var c = s[n ? l : ++i];
              if (e(f[c], c, f) === !1)
                break;
            }
            return t;
          };
        }
        function Ia(n, t, e) {
          var r = t & Cn, i = re(n);
          function f() {
            var s = this && this !== V && this instanceof f ? i : n;
            return s.apply(r ? e : this, arguments);
          }
          return f;
        }
        function ff(n) {
          return function(t) {
            t = U(t);
            var e = Ct(t) ? Ln(t) : o, r = e ? e[0] : t.charAt(0), i = e ? rt(e, 1).join("") : t.slice(1);
            return r[n]() + i;
          };
        }
        function bt(n) {
          return function(t) {
            return Er(io(ro(t).replace(ws, "")), n, "");
          };
        }
        function re(n) {
          return function() {
            var t = arguments;
            switch (t.length) {
              case 0:
                return new n();
              case 1:
                return new n(t[0]);
              case 2:
                return new n(t[0], t[1]);
              case 3:
                return new n(t[0], t[1], t[2]);
              case 4:
                return new n(t[0], t[1], t[2], t[3]);
              case 5:
                return new n(t[0], t[1], t[2], t[3], t[4]);
              case 6:
                return new n(t[0], t[1], t[2], t[3], t[4], t[5]);
              case 7:
                return new n(t[0], t[1], t[2], t[3], t[4], t[5], t[6]);
            }
            var e = Ot(n.prototype), r = n.apply(e, t);
            return q(r) ? r : e;
          };
        }
        function Ra(n, t, e) {
          var r = re(n);
          function i() {
            for (var f = arguments.length, s = h(f), l = f, c = Wt(i); l--; )
              s[l] = arguments[l];
            var p = f < 3 && s[0] !== c && s[f - 1] !== c ? [] : Qn(s, c);
            if (f -= p.length, f < e)
              return cf(
                n,
                t,
                $e,
                i.placeholder,
                o,
                s,
                p,
                o,
                o,
                e - f
              );
            var d = this && this !== V && this instanceof i ? r : n;
            return ln(d, this, s);
          }
          return i;
        }
        function of(n) {
          return function(t, e, r) {
            var i = F(t);
            if (!fn(t)) {
              var f = S(e, 3);
              t = J(t), e = function(l) {
                return f(i[l], l, i);
              };
            }
            var s = n(t, e, r);
            return s > -1 ? i[f ? t[s] : s] : o;
          };
        }
        function sf(n) {
          return qn(function(t) {
            var e = t.length, r = e, i = xn.prototype.thru;
            for (n && t.reverse(); r--; ) {
              var f = t[r];
              if (typeof f != "function")
                throw new wn(T);
              if (i && !s && Ze(f) == "wrapper")
                var s = new xn([], !0);
            }
            for (r = s ? r : e; ++r < e; ) {
              f = t[r];
              var l = Ze(f), c = l == "wrapper" ? fi(f) : o;
              c && ai(c[0]) && c[1] == (Fn | Un | Pn | Ft) && !c[4].length && c[9] == 1 ? s = s[Ze(c[0])].apply(s, c[3]) : s = f.length == 1 && ai(f) ? s[l]() : s.thru(f);
            }
            return function() {
              var p = arguments, d = p[0];
              if (s && p.length == 1 && L(d))
                return s.plant(d).value();
              for (var _ = 0, w = e ? t[_].apply(this, p) : d; ++_ < e; )
                w = t[_].call(this, w);
              return w;
            };
          });
        }
        function $e(n, t, e, r, i, f, s, l, c, p) {
          var d = t & Fn, _ = t & Cn, w = t & wt, A = t & (Un | Ut), y = t & fr, R = w ? o : re(n);
          function C() {
            for (var M = arguments.length, D = h(M), gn = M; gn--; )
              D[gn] = arguments[gn];
            if (A)
              var rn = Wt(C), pn = zs(D, rn);
            if (r && (D = tf(D, r, i, A)), f && (D = ef(D, f, s, A)), M -= pn, A && M < p) {
              var Z = Qn(D, rn);
              return cf(
                n,
                t,
                $e,
                C.placeholder,
                e,
                D,
                Z,
                l,
                c,
                p - M
              );
            }
            var On = _ ? e : this, Yn = w ? On[n] : n;
            return M = D.length, l ? D = Xa(D, l) : y && M > 1 && D.reverse(), d && c < M && (D.length = c), this && this !== V && this instanceof C && (Yn = R || re(Yn)), Yn.apply(On, D);
          }
          return C;
        }
        function lf(n, t) {
          return function(e, r) {
            return ra(e, n, t(r), {});
          };
        }
        function qe(n, t) {
          return function(e, r) {
            var i;
            if (e === o && r === o)
              return t;
            if (e !== o && (i = e), r !== o) {
              if (i === o)
                return r;
              typeof e == "string" || typeof r == "string" ? (e = cn(e), r = cn(r)) : (e = Zu(e), r = Zu(r)), i = n(e, r);
            }
            return i;
          };
        }
        function ei(n) {
          return qn(function(t) {
            return t = $(t, an(S())), O(function(e) {
              var r = this;
              return n(t, function(i) {
                return ln(i, r, e);
              });
            });
          });
        }
        function Ke(n, t) {
          t = t === o ? " " : cn(t);
          var e = t.length;
          if (e < 2)
            return e ? Xr(t, n) : t;
          var r = Xr(t, Me(n / Tt(t)));
          return Ct(t) ? rt(Ln(r), 0, n).join("") : r.slice(0, n);
        }
        function Oa(n, t, e, r) {
          var i = t & Cn, f = re(n);
          function s() {
            for (var l = -1, c = arguments.length, p = -1, d = r.length, _ = h(d + c), w = this && this !== V && this instanceof s ? f : n; ++p < d; )
              _[p] = r[p];
            for (; c--; )
              _[p++] = arguments[++l];
            return ln(w, i ? e : this, _);
          }
          return s;
        }
        function af(n) {
          return function(t, e, r) {
            return r && typeof r != "number" && en(t, e, r) && (e = r = o), t = Zn(t), e === o ? (e = t, t = 0) : e = Zn(e), r = r === o ? t < e ? 1 : -1 : Zn(r), da(t, e, r, n);
          };
        }
        function ke(n) {
          return function(t, e) {
            return typeof t == "string" && typeof e == "string" || (t = yn(t), e = yn(e)), n(t, e);
          };
        }
        function cf(n, t, e, r, i, f, s, l, c, p) {
          var d = t & Un, _ = d ? s : o, w = d ? o : s, A = d ? f : o, y = d ? o : f;
          t |= d ? Pn : Pt, t &= ~(d ? Pt : Pn), t & Ii || (t &= -4);
          var R = [
            n,
            t,
            i,
            A,
            _,
            y,
            w,
            l,
            c,
            p
          ], C = e.apply(o, R);
          return ai(n) && yf(C, R), C.placeholder = r, Cf(C, n, t);
        }
        function ri(n) {
          var t = Y[n];
          return function(e, r) {
            if (e = yn(e), r = r == null ? 0 : j(I(r), 292), r && yu(e)) {
              var i = (U(e) + "e").split("e"), f = t(i[0] + "e" + (+i[1] + r));
              return i = (U(f) + "e").split("e"), +(i[0] + "e" + (+i[1] - r));
            }
            return t(e);
          };
        }
        var Ma = It && 1 / Ae(new It([, -0]))[1] == ae ? function(n) {
          return new It(n);
        } : Ti;
        function hf(n) {
          return function(t) {
            var e = nn(t);
            return e == Tn ? Wr(t) : e == En ? Ys(t) : Ns(t, n(t));
          };
        }
        function $n(n, t, e, r, i, f, s, l) {
          var c = t & wt;
          if (!c && typeof n != "function")
            throw new wn(T);
          var p = r ? r.length : 0;
          if (p || (t &= -97, r = i = o), s = s === o ? s : X(I(s), 0), l = l === o ? l : I(l), p -= i ? i.length : 0, t & Pt) {
            var d = r, _ = i;
            r = i = o;
          }
          var w = c ? o : fi(n), A = [
            n,
            t,
            e,
            r,
            i,
            d,
            _,
            f,
            s,
            l
          ];
          if (w && ka(A, w), n = A[0], t = A[1], e = A[2], r = A[3], i = A[4], l = A[9] = A[9] === o ? c ? 0 : n.length : X(A[9] - p, 0), !l && t & (Un | Ut) && (t &= -25), !t || t == Cn)
            var y = Ia(n, t, e);
          else t == Un || t == Ut ? y = Ra(n, t, l) : (t == Pn || t == (Cn | Pn)) && !i.length ? y = Oa(n, t, e, r) : y = $e.apply(o, A);
          var R = w ? Ku : yf;
          return Cf(R(y, A), n, t);
        }
        function gf(n, t, e, r) {
          return n === o || Rn(n, Lt[e]) && !P.call(r, e) ? t : n;
        }
        function pf(n, t, e, r, i, f) {
          return q(n) && q(t) && (f.set(t, n), He(n, t, o, pf, f), f.delete(t)), n;
        }
        function ba(n) {
          return fe(n) ? o : n;
        }
        function df(n, t, e, r, i, f) {
          var s = e & vt, l = n.length, c = t.length;
          if (l != c && !(s && c > l))
            return !1;
          var p = f.get(n), d = f.get(t);
          if (p && d)
            return p == t && d == n;
          var _ = -1, w = !0, A = e & le ? new lt() : o;
          for (f.set(n, t), f.set(t, n); ++_ < l; ) {
            var y = n[_], R = t[_];
            if (r)
              var C = s ? r(R, y, _, t, n, f) : r(y, R, _, n, t, f);
            if (C !== o) {
              if (C)
                continue;
              w = !1;
              break;
            }
            if (A) {
              if (!Lr(t, function(M, D) {
                if (!kt(A, D) && (y === M || i(y, M, e, r, f)))
                  return A.push(D);
              })) {
                w = !1;
                break;
              }
            } else if (!(y === R || i(y, R, e, r, f))) {
              w = !1;
              break;
            }
          }
          return f.delete(n), f.delete(t), w;
        }
        function Wa(n, t, e, r, i, f, s) {
          switch (e) {
            case At:
              if (n.byteLength != t.byteLength || n.byteOffset != t.byteOffset)
                return !1;
              n = n.buffer, t = t.buffer;
            case Kt:
              return !(n.byteLength != t.byteLength || !f(new Le(n), new Le(t)));
            case Ht:
            case Nt:
            case zt:
              return Rn(+n, +t);
            case ge:
              return n.name == t.name && n.message == t.message;
            case Gt:
            case $t:
              return n == t + "";
            case Tn:
              var l = Wr;
            case En:
              var c = r & vt;
              if (l || (l = Ae), n.size != t.size && !c)
                return !1;
              var p = s.get(n);
              if (p)
                return p == t;
              r |= le, s.set(n, t);
              var d = df(l(n), l(t), r, i, f, s);
              return s.delete(n), d;
            case de:
              if (Vt)
                return Vt.call(n) == Vt.call(t);
          }
          return !1;
        }
        function Da(n, t, e, r, i, f) {
          var s = e & vt, l = ii(n), c = l.length, p = ii(t), d = p.length;
          if (c != d && !s)
            return !1;
          for (var _ = c; _--; ) {
            var w = l[_];
            if (!(s ? w in t : P.call(t, w)))
              return !1;
          }
          var A = f.get(n), y = f.get(t);
          if (A && y)
            return A == t && y == n;
          var R = !0;
          f.set(n, t), f.set(t, n);
          for (var C = s; ++_ < c; ) {
            w = l[_];
            var M = n[w], D = t[w];
            if (r)
              var gn = s ? r(D, M, w, t, n, f) : r(M, D, w, n, t, f);
            if (!(gn === o ? M === D || i(M, D, e, r, f) : gn)) {
              R = !1;
              break;
            }
            C || (C = w == "constructor");
          }
          if (R && !C) {
            var rn = n.constructor, pn = t.constructor;
            rn != pn && "constructor" in n && "constructor" in t && !(typeof rn == "function" && rn instanceof rn && typeof pn == "function" && pn instanceof pn) && (R = !1);
          }
          return f.delete(n), f.delete(t), R;
        }
        function qn(n) {
          return hi(Af(n, o, Of), n + "");
        }
        function ii(n) {
          return Du(n, J, si);
        }
        function ui(n) {
          return Du(n, on, _f);
        }
        var fi = We ? function(n) {
          return We.get(n);
        } : Ti;
        function Ze(n) {
          for (var t = n.name + "", e = Rt[t], r = P.call(Rt, t) ? e.length : 0; r--; ) {
            var i = e[r], f = i.func;
            if (f == null || f == n)
              return i.name;
          }
          return t;
        }
        function Wt(n) {
          var t = P.call(u, "placeholder") ? u : n;
          return t.placeholder;
        }
        function S() {
          var n = u.iteratee || yi;
          return n = n === yi ? Pu : n, arguments.length ? n(arguments[0], arguments[1]) : n;
        }
        function Ye(n, t) {
          var e = n.__data__;
          return Ga(t) ? e[typeof t == "string" ? "string" : "hash"] : e.map;
        }
        function oi(n) {
          for (var t = J(n), e = t.length; e--; ) {
            var r = t[e], i = n[r];
            t[e] = [r, i, xf(i)];
          }
          return t;
        }
        function ht(n, t) {
          var e = Ks(n, t);
          return Uu(e) ? e : o;
        }
        function Ba(n) {
          var t = P.call(n, ot), e = n[ot];
          try {
            n[ot] = o;
            var r = !0;
          } catch {
          }
          var i = Te.call(n);
          return r && (t ? n[ot] = e : delete n[ot]), i;
        }
        var si = Br ? function(n) {
          return n == null ? [] : (n = F(n), Jn(Br(n), function(t) {
            return Au.call(n, t);
          }));
        } : Ei, _f = Br ? function(n) {
          for (var t = []; n; )
            Vn(t, si(n)), n = Ie(n);
          return t;
        } : Ei, nn = tn;
        (Ur && nn(new Ur(new ArrayBuffer(1))) != At || Yt && nn(new Yt()) != Tn || Pr && nn(Pr.resolve()) != Mi || It && nn(new It()) != En || Xt && nn(new Xt()) != qt) && (nn = function(n) {
          var t = tn(n), e = t == Hn ? n.constructor : o, r = e ? gt(e) : "";
          if (r)
            switch (r) {
              case vl:
                return At;
              case wl:
                return Tn;
              case xl:
                return Mi;
              case ml:
                return En;
              case Al:
                return qt;
            }
          return t;
        });
        function Ua(n, t, e) {
          for (var r = -1, i = e.length; ++r < i; ) {
            var f = e[r], s = f.size;
            switch (f.type) {
              case "drop":
                n += s;
                break;
              case "dropRight":
                t -= s;
                break;
              case "take":
                t = j(t, n + s);
                break;
              case "takeRight":
                n = X(n, t - s);
                break;
            }
          }
          return { start: n, end: t };
        }
        function Pa(n) {
          var t = n.match($o);
          return t ? t[1].split(qo) : [];
        }
        function vf(n, t, e) {
          t = et(t, n);
          for (var r = -1, i = t.length, f = !1; ++r < i; ) {
            var s = Bn(t[r]);
            if (!(f = n != null && e(n, s)))
              break;
            n = n[s];
          }
          return f || ++r != i ? f : (i = n == null ? 0 : n.length, !!i && tr(i) && Kn(s, i) && (L(n) || pt(n)));
        }
        function Fa(n) {
          var t = n.length, e = new n.constructor(t);
          return t && typeof n[0] == "string" && P.call(n, "index") && (e.index = n.index, e.input = n.input), e;
        }
        function wf(n) {
          return typeof n.constructor == "function" && !ie(n) ? Ot(Ie(n)) : {};
        }
        function Ha(n, t, e) {
          var r = n.constructor;
          switch (t) {
            case Kt:
              return ti(n);
            case Ht:
            case Nt:
              return new r(+n);
            case At:
              return Sa(n, e);
            case or:
            case sr:
            case lr:
            case ar:
            case cr:
            case hr:
            case gr:
            case pr:
            case dr:
              return ju(n, e);
            case Tn:
              return new r();
            case zt:
            case $t:
              return new r(n);
            case Gt:
              return ya(n);
            case En:
              return new r();
            case de:
              return Ca(n);
          }
        }
        function Na(n, t) {
          var e = t.length;
          if (!e)
            return n;
          var r = e - 1;
          return t[r] = (e > 1 ? "& " : "") + t[r], t = t.join(e > 2 ? ", " : " "), n.replace(Go, `{
/* [wrapped with ` + t + `] */
`);
        }
        function za(n) {
          return L(n) || pt(n) || !!(Su && n && n[Su]);
        }
        function Kn(n, t) {
          var e = typeof n;
          return t = t ?? xt, !!t && (e == "number" || e != "symbol" && jo.test(n)) && n > -1 && n % 1 == 0 && n < t;
        }
        function en(n, t, e) {
          if (!q(e))
            return !1;
          var r = typeof t;
          return (r == "number" ? fn(e) && Kn(t, e.length) : r == "string" && t in e) ? Rn(e[t], n) : !1;
        }
        function li(n, t) {
          if (L(n))
            return !1;
          var e = typeof n;
          return e == "number" || e == "symbol" || e == "boolean" || n == null || hn(n) ? !0 : Fo.test(n) || !Po.test(n) || t != null && n in F(t);
        }
        function Ga(n) {
          var t = typeof n;
          return t == "string" || t == "number" || t == "symbol" || t == "boolean" ? n !== "__proto__" : n === null;
        }
        function ai(n) {
          var t = Ze(n), e = u[t];
          if (typeof e != "function" || !(t in W.prototype))
            return !1;
          if (n === e)
            return !0;
          var r = fi(e);
          return !!r && n === r[0];
        }
        function $a(n) {
          return !!wu && wu in n;
        }
        var qa = ye ? kn : Li;
        function ie(n) {
          var t = n && n.constructor, e = typeof t == "function" && t.prototype || Lt;
          return n === e;
        }
        function xf(n) {
          return n === n && !q(n);
        }
        function mf(n, t) {
          return function(e) {
            return e == null ? !1 : e[n] === t && (t !== o || n in F(e));
          };
        }
        function Ka(n) {
          var t = je(n, function(r) {
            return e.size === ut && e.clear(), r;
          }), e = t.cache;
          return t;
        }
        function ka(n, t) {
          var e = n[1], r = t[1], i = e | r, f = i < (Cn | wt | Fn), s = r == Fn && e == Un || r == Fn && e == Ft && n[7].length <= t[8] || r == (Fn | Ft) && t[7].length <= t[8] && e == Un;
          if (!(f || s))
            return n;
          r & Cn && (n[2] = t[2], i |= e & Cn ? 0 : Ii);
          var l = t[3];
          if (l) {
            var c = n[3];
            n[3] = c ? tf(c, l, t[4]) : l, n[4] = c ? Qn(n[3], dt) : t[4];
          }
          return l = t[5], l && (c = n[5], n[5] = c ? ef(c, l, t[6]) : l, n[6] = c ? Qn(n[5], dt) : t[6]), l = t[7], l && (n[7] = l), r & Fn && (n[8] = n[8] == null ? t[8] : j(n[8], t[8])), n[9] == null && (n[9] = t[9]), n[0] = t[0], n[1] = i, n;
        }
        function Za(n) {
          var t = [];
          if (n != null)
            for (var e in F(n))
              t.push(e);
          return t;
        }
        function Ya(n) {
          return Te.call(n);
        }
        function Af(n, t, e) {
          return t = X(t === o ? n.length - 1 : t, 0), function() {
            for (var r = arguments, i = -1, f = X(r.length - t, 0), s = h(f); ++i < f; )
              s[i] = r[t + i];
            i = -1;
            for (var l = h(t + 1); ++i < t; )
              l[i] = r[i];
            return l[t] = e(s), ln(n, this, l);
          };
        }
        function Sf(n, t) {
          return t.length < 2 ? n : ct(n, An(t, 0, -1));
        }
        function Xa(n, t) {
          for (var e = n.length, r = j(t.length, e), i = un(n); r--; ) {
            var f = t[r];
            n[r] = Kn(f, e) ? i[f] : o;
          }
          return n;
        }
        function ci(n, t) {
          if (!(t === "constructor" && typeof n[t] == "function") && t != "__proto__")
            return n[t];
        }
        var yf = Tf(Ku), ue = al || function(n, t) {
          return V.setTimeout(n, t);
        }, hi = Tf(wa);
        function Cf(n, t, e) {
          var r = t + "";
          return hi(n, Na(r, Ja(Pa(r), e)));
        }
        function Tf(n) {
          var t = 0, e = 0;
          return function() {
            var r = pl(), i = vo - (r - e);
            if (e = r, i > 0) {
              if (++t >= _o)
                return arguments[0];
            } else
              t = 0;
            return n.apply(o, arguments);
          };
        }
        function Xe(n, t) {
          var e = -1, r = n.length, i = r - 1;
          for (t = t === o ? r : t; ++e < t; ) {
            var f = Yr(e, i), s = n[f];
            n[f] = n[e], n[e] = s;
          }
          return n.length = t, n;
        }
        var Ef = Ka(function(n) {
          var t = [];
          return n.charCodeAt(0) === 46 && t.push(""), n.replace(Ho, function(e, r, i, f) {
            t.push(i ? f.replace(Zo, "$1") : r || e);
          }), t;
        });
        function Bn(n) {
          if (typeof n == "string" || hn(n))
            return n;
          var t = n + "";
          return t == "0" && 1 / n == -1 / 0 ? "-0" : t;
        }
        function gt(n) {
          if (n != null) {
            try {
              return Ce.call(n);
            } catch {
            }
            try {
              return n + "";
            } catch {
            }
          }
          return "";
        }
        function Ja(n, t) {
          return vn(yo, function(e) {
            var r = "_." + e[0];
            t & e[1] && !xe(n, r) && n.push(r);
          }), n.sort();
        }
        function Lf(n) {
          if (n instanceof W)
            return n.clone();
          var t = new xn(n.__wrapped__, n.__chain__);
          return t.__actions__ = un(n.__actions__), t.__index__ = n.__index__, t.__values__ = n.__values__, t;
        }
        function Va(n, t, e) {
          (e ? en(n, t, e) : t === o) ? t = 1 : t = X(I(t), 0);
          var r = n == null ? 0 : n.length;
          if (!r || t < 1)
            return [];
          for (var i = 0, f = 0, s = h(Me(r / t)); i < r; )
            s[f++] = An(n, i, i += t);
          return s;
        }
        function Qa(n) {
          for (var t = -1, e = n == null ? 0 : n.length, r = 0, i = []; ++t < e; ) {
            var f = n[t];
            f && (i[r++] = f);
          }
          return i;
        }
        function ja() {
          var n = arguments.length;
          if (!n)
            return [];
          for (var t = h(n - 1), e = arguments[0], r = n; r--; )
            t[r - 1] = arguments[r];
          return Vn(L(e) ? un(e) : [e], Q(t, 1));
        }
        var nc = O(function(n, t) {
          return k(n) ? jt(n, Q(t, 1, k, !0)) : [];
        }), tc = O(function(n, t) {
          var e = Sn(t);
          return k(e) && (e = o), k(n) ? jt(n, Q(t, 1, k, !0), S(e, 2)) : [];
        }), ec = O(function(n, t) {
          var e = Sn(t);
          return k(e) && (e = o), k(n) ? jt(n, Q(t, 1, k, !0), o, e) : [];
        });
        function rc(n, t, e) {
          var r = n == null ? 0 : n.length;
          return r ? (t = e || t === o ? 1 : I(t), An(n, t < 0 ? 0 : t, r)) : [];
        }
        function ic(n, t, e) {
          var r = n == null ? 0 : n.length;
          return r ? (t = e || t === o ? 1 : I(t), t = r - t, An(n, 0, t < 0 ? 0 : t)) : [];
        }
        function uc(n, t) {
          return n && n.length ? ze(n, S(t, 3), !0, !0) : [];
        }
        function fc(n, t) {
          return n && n.length ? ze(n, S(t, 3), !0) : [];
        }
        function oc(n, t, e, r) {
          var i = n == null ? 0 : n.length;
          return i ? (e && typeof e != "number" && en(n, t, e) && (e = 0, r = i), jl(n, t, e, r)) : [];
        }
        function If(n, t, e) {
          var r = n == null ? 0 : n.length;
          if (!r)
            return -1;
          var i = e == null ? 0 : I(e);
          return i < 0 && (i = X(r + i, 0)), me(n, S(t, 3), i);
        }
        function Rf(n, t, e) {
          var r = n == null ? 0 : n.length;
          if (!r)
            return -1;
          var i = r - 1;
          return e !== o && (i = I(e), i = e < 0 ? X(r + i, 0) : j(i, r - 1)), me(n, S(t, 3), i, !0);
        }
        function Of(n) {
          var t = n == null ? 0 : n.length;
          return t ? Q(n, 1) : [];
        }
        function sc(n) {
          var t = n == null ? 0 : n.length;
          return t ? Q(n, ae) : [];
        }
        function lc(n, t) {
          var e = n == null ? 0 : n.length;
          return e ? (t = t === o ? 1 : I(t), Q(n, t)) : [];
        }
        function ac(n) {
          for (var t = -1, e = n == null ? 0 : n.length, r = {}; ++t < e; ) {
            var i = n[t];
            r[i[0]] = i[1];
          }
          return r;
        }
        function Mf(n) {
          return n && n.length ? n[0] : o;
        }
        function cc(n, t, e) {
          var r = n == null ? 0 : n.length;
          if (!r)
            return -1;
          var i = e == null ? 0 : I(e);
          return i < 0 && (i = X(r + i, 0)), yt(n, t, i);
        }
        function hc(n) {
          var t = n == null ? 0 : n.length;
          return t ? An(n, 0, -1) : [];
        }
        var gc = O(function(n) {
          var t = $(n, jr);
          return t.length && t[0] === n[0] ? $r(t) : [];
        }), pc = O(function(n) {
          var t = Sn(n), e = $(n, jr);
          return t === Sn(e) ? t = o : e.pop(), e.length && e[0] === n[0] ? $r(e, S(t, 2)) : [];
        }), dc = O(function(n) {
          var t = Sn(n), e = $(n, jr);
          return t = typeof t == "function" ? t : o, t && e.pop(), e.length && e[0] === n[0] ? $r(e, o, t) : [];
        });
        function _c(n, t) {
          return n == null ? "" : hl.call(n, t);
        }
        function Sn(n) {
          var t = n == null ? 0 : n.length;
          return t ? n[t - 1] : o;
        }
        function vc(n, t, e) {
          var r = n == null ? 0 : n.length;
          if (!r)
            return -1;
          var i = r;
          return e !== o && (i = I(e), i = i < 0 ? X(r + i, 0) : j(i, r - 1)), t === t ? Js(n, t, i) : me(n, au, i, !0);
        }
        function wc(n, t) {
          return n && n.length ? zu(n, I(t)) : o;
        }
        var xc = O(bf);
        function bf(n, t) {
          return n && n.length && t && t.length ? Zr(n, t) : n;
        }
        function mc(n, t, e) {
          return n && n.length && t && t.length ? Zr(n, t, S(e, 2)) : n;
        }
        function Ac(n, t, e) {
          return n && n.length && t && t.length ? Zr(n, t, o, e) : n;
        }
        var Sc = qn(function(n, t) {
          var e = n == null ? 0 : n.length, r = Hr(n, t);
          return qu(n, $(t, function(i) {
            return Kn(i, e) ? +i : i;
          }).sort(nf)), r;
        });
        function yc(n, t) {
          var e = [];
          if (!(n && n.length))
            return e;
          var r = -1, i = [], f = n.length;
          for (t = S(t, 3); ++r < f; ) {
            var s = n[r];
            t(s, r, n) && (e.push(s), i.push(r));
          }
          return qu(n, i), e;
        }
        function gi(n) {
          return n == null ? n : _l.call(n);
        }
        function Cc(n, t, e) {
          var r = n == null ? 0 : n.length;
          return r ? (e && typeof e != "number" && en(n, t, e) ? (t = 0, e = r) : (t = t == null ? 0 : I(t), e = e === o ? r : I(e)), An(n, t, e)) : [];
        }
        function Tc(n, t) {
          return Ne(n, t);
        }
        function Ec(n, t, e) {
          return Jr(n, t, S(e, 2));
        }
        function Lc(n, t) {
          var e = n == null ? 0 : n.length;
          if (e) {
            var r = Ne(n, t);
            if (r < e && Rn(n[r], t))
              return r;
          }
          return -1;
        }
        function Ic(n, t) {
          return Ne(n, t, !0);
        }
        function Rc(n, t, e) {
          return Jr(n, t, S(e, 2), !0);
        }
        function Oc(n, t) {
          var e = n == null ? 0 : n.length;
          if (e) {
            var r = Ne(n, t, !0) - 1;
            if (Rn(n[r], t))
              return r;
          }
          return -1;
        }
        function Mc(n) {
          return n && n.length ? ku(n) : [];
        }
        function bc(n, t) {
          return n && n.length ? ku(n, S(t, 2)) : [];
        }
        function Wc(n) {
          var t = n == null ? 0 : n.length;
          return t ? An(n, 1, t) : [];
        }
        function Dc(n, t, e) {
          return n && n.length ? (t = e || t === o ? 1 : I(t), An(n, 0, t < 0 ? 0 : t)) : [];
        }
        function Bc(n, t, e) {
          var r = n == null ? 0 : n.length;
          return r ? (t = e || t === o ? 1 : I(t), t = r - t, An(n, t < 0 ? 0 : t, r)) : [];
        }
        function Uc(n, t) {
          return n && n.length ? ze(n, S(t, 3), !1, !0) : [];
        }
        function Pc(n, t) {
          return n && n.length ? ze(n, S(t, 3)) : [];
        }
        var Fc = O(function(n) {
          return tt(Q(n, 1, k, !0));
        }), Hc = O(function(n) {
          var t = Sn(n);
          return k(t) && (t = o), tt(Q(n, 1, k, !0), S(t, 2));
        }), Nc = O(function(n) {
          var t = Sn(n);
          return t = typeof t == "function" ? t : o, tt(Q(n, 1, k, !0), o, t);
        });
        function zc(n) {
          return n && n.length ? tt(n) : [];
        }
        function Gc(n, t) {
          return n && n.length ? tt(n, S(t, 2)) : [];
        }
        function $c(n, t) {
          return t = typeof t == "function" ? t : o, n && n.length ? tt(n, o, t) : [];
        }
        function pi(n) {
          if (!(n && n.length))
            return [];
          var t = 0;
          return n = Jn(n, function(e) {
            if (k(e))
              return t = X(e.length, t), !0;
          }), Mr(t, function(e) {
            return $(n, Ir(e));
          });
        }
        function Wf(n, t) {
          if (!(n && n.length))
            return [];
          var e = pi(n);
          return t == null ? e : $(e, function(r) {
            return ln(t, o, r);
          });
        }
        var qc = O(function(n, t) {
          return k(n) ? jt(n, t) : [];
        }), Kc = O(function(n) {
          return Qr(Jn(n, k));
        }), kc = O(function(n) {
          var t = Sn(n);
          return k(t) && (t = o), Qr(Jn(n, k), S(t, 2));
        }), Zc = O(function(n) {
          var t = Sn(n);
          return t = typeof t == "function" ? t : o, Qr(Jn(n, k), o, t);
        }), Yc = O(pi);
        function Xc(n, t) {
          return Ju(n || [], t || [], Qt);
        }
        function Jc(n, t) {
          return Ju(n || [], t || [], ee);
        }
        var Vc = O(function(n) {
          var t = n.length, e = t > 1 ? n[t - 1] : o;
          return e = typeof e == "function" ? (n.pop(), e) : o, Wf(n, e);
        });
        function Df(n) {
          var t = u(n);
          return t.__chain__ = !0, t;
        }
        function Qc(n, t) {
          return t(n), n;
        }
        function Je(n, t) {
          return t(n);
        }
        var jc = qn(function(n) {
          var t = n.length, e = t ? n[0] : 0, r = this.__wrapped__, i = function(f) {
            return Hr(f, n);
          };
          return t > 1 || this.__actions__.length || !(r instanceof W) || !Kn(e) ? this.thru(i) : (r = r.slice(e, +e + (t ? 1 : 0)), r.__actions__.push({
            func: Je,
            args: [i],
            thisArg: o
          }), new xn(r, this.__chain__).thru(function(f) {
            return t && !f.length && f.push(o), f;
          }));
        });
        function n3() {
          return Df(this);
        }
        function t3() {
          return new xn(this.value(), this.__chain__);
        }
        function e3() {
          this.__values__ === o && (this.__values__ = Yf(this.value()));
          var n = this.__index__ >= this.__values__.length, t = n ? o : this.__values__[this.__index__++];
          return { done: n, value: t };
        }
        function r3() {
          return this;
        }
        function i3(n) {
          for (var t, e = this; e instanceof Be; ) {
            var r = Lf(e);
            r.__index__ = 0, r.__values__ = o, t ? i.__wrapped__ = r : t = r;
            var i = r;
            e = e.__wrapped__;
          }
          return i.__wrapped__ = n, t;
        }
        function u3() {
          var n = this.__wrapped__;
          if (n instanceof W) {
            var t = n;
            return this.__actions__.length && (t = new W(this)), t = t.reverse(), t.__actions__.push({
              func: Je,
              args: [gi],
              thisArg: o
            }), new xn(t, this.__chain__);
          }
          return this.thru(gi);
        }
        function f3() {
          return Xu(this.__wrapped__, this.__actions__);
        }
        var o3 = Ge(function(n, t, e) {
          P.call(n, e) ? ++n[e] : Gn(n, e, 1);
        });
        function s3(n, t, e) {
          var r = L(n) ? su : Ql;
          return e && en(n, t, e) && (t = o), r(n, S(t, 3));
        }
        function l3(n, t) {
          var e = L(n) ? Jn : bu;
          return e(n, S(t, 3));
        }
        var a3 = of(If), c3 = of(Rf);
        function h3(n, t) {
          return Q(Ve(n, t), 1);
        }
        function g3(n, t) {
          return Q(Ve(n, t), ae);
        }
        function p3(n, t, e) {
          return e = e === o ? 1 : I(e), Q(Ve(n, t), e);
        }
        function Bf(n, t) {
          var e = L(n) ? vn : nt;
          return e(n, S(t, 3));
        }
        function Uf(n, t) {
          var e = L(n) ? Ws : Mu;
          return e(n, S(t, 3));
        }
        var d3 = Ge(function(n, t, e) {
          P.call(n, e) ? n[e].push(t) : Gn(n, e, [t]);
        });
        function _3(n, t, e, r) {
          n = fn(n) ? n : Bt(n), e = e && !r ? I(e) : 0;
          var i = n.length;
          return e < 0 && (e = X(i + e, 0)), er(n) ? e <= i && n.indexOf(t, e) > -1 : !!i && yt(n, t, e) > -1;
        }
        var v3 = O(function(n, t, e) {
          var r = -1, i = typeof t == "function", f = fn(n) ? h(n.length) : [];
          return nt(n, function(s) {
            f[++r] = i ? ln(t, s, e) : ne(s, t, e);
          }), f;
        }), w3 = Ge(function(n, t, e) {
          Gn(n, e, t);
        });
        function Ve(n, t) {
          var e = L(n) ? $ : Fu;
          return e(n, S(t, 3));
        }
        function x3(n, t, e, r) {
          return n == null ? [] : (L(t) || (t = t == null ? [] : [t]), e = r ? o : e, L(e) || (e = e == null ? [] : [e]), Gu(n, t, e));
        }
        var m3 = Ge(function(n, t, e) {
          n[e ? 0 : 1].push(t);
        }, function() {
          return [[], []];
        });
        function A3(n, t, e) {
          var r = L(n) ? Er : hu, i = arguments.length < 3;
          return r(n, S(t, 4), e, i, nt);
        }
        function S3(n, t, e) {
          var r = L(n) ? Ds : hu, i = arguments.length < 3;
          return r(n, S(t, 4), e, i, Mu);
        }
        function y3(n, t) {
          var e = L(n) ? Jn : bu;
          return e(n, nr(S(t, 3)));
        }
        function C3(n) {
          var t = L(n) ? Lu : _a;
          return t(n);
        }
        function T3(n, t, e) {
          (e ? en(n, t, e) : t === o) ? t = 1 : t = I(t);
          var r = L(n) ? Zl : va;
          return r(n, t);
        }
        function E3(n) {
          var t = L(n) ? Yl : xa;
          return t(n);
        }
        function L3(n) {
          if (n == null)
            return 0;
          if (fn(n))
            return er(n) ? Tt(n) : n.length;
          var t = nn(n);
          return t == Tn || t == En ? n.size : Kr(n).length;
        }
        function I3(n, t, e) {
          var r = L(n) ? Lr : ma;
          return e && en(n, t, e) && (t = o), r(n, S(t, 3));
        }
        var R3 = O(function(n, t) {
          if (n == null)
            return [];
          var e = t.length;
          return e > 1 && en(n, t[0], t[1]) ? t = [] : e > 2 && en(t[0], t[1], t[2]) && (t = [t[0]]), Gu(n, Q(t, 1), []);
        }), Qe = ll || function() {
          return V.Date.now();
        };
        function O3(n, t) {
          if (typeof t != "function")
            throw new wn(T);
          return n = I(n), function() {
            if (--n < 1)
              return t.apply(this, arguments);
          };
        }
        function Pf(n, t, e) {
          return t = e ? o : t, t = n && t == null ? n.length : t, $n(n, Fn, o, o, o, o, t);
        }
        function Ff(n, t) {
          var e;
          if (typeof t != "function")
            throw new wn(T);
          return n = I(n), function() {
            return --n > 0 && (e = t.apply(this, arguments)), n <= 1 && (t = o), e;
          };
        }
        var di = O(function(n, t, e) {
          var r = Cn;
          if (e.length) {
            var i = Qn(e, Wt(di));
            r |= Pn;
          }
          return $n(n, r, t, e, i);
        }), Hf = O(function(n, t, e) {
          var r = Cn | wt;
          if (e.length) {
            var i = Qn(e, Wt(Hf));
            r |= Pn;
          }
          return $n(t, r, n, e, i);
        });
        function Nf(n, t, e) {
          t = e ? o : t;
          var r = $n(n, Un, o, o, o, o, o, t);
          return r.placeholder = Nf.placeholder, r;
        }
        function zf(n, t, e) {
          t = e ? o : t;
          var r = $n(n, Ut, o, o, o, o, o, t);
          return r.placeholder = zf.placeholder, r;
        }
        function Gf(n, t, e) {
          var r, i, f, s, l, c, p = 0, d = !1, _ = !1, w = !0;
          if (typeof n != "function")
            throw new wn(T);
          t = yn(t) || 0, q(e) && (d = !!e.leading, _ = "maxWait" in e, f = _ ? X(yn(e.maxWait) || 0, t) : f, w = "trailing" in e ? !!e.trailing : w);
          function A(Z) {
            var On = r, Yn = i;
            return r = i = o, p = Z, s = n.apply(Yn, On), s;
          }
          function y(Z) {
            return p = Z, l = ue(M, t), d ? A(Z) : s;
          }
          function R(Z) {
            var On = Z - c, Yn = Z - p, oo = t - On;
            return _ ? j(oo, f - Yn) : oo;
          }
          function C(Z) {
            var On = Z - c, Yn = Z - p;
            return c === o || On >= t || On < 0 || _ && Yn >= f;
          }
          function M() {
            var Z = Qe();
            if (C(Z))
              return D(Z);
            l = ue(M, R(Z));
          }
          function D(Z) {
            return l = o, w && r ? A(Z) : (r = i = o, s);
          }
          function gn() {
            l !== o && Vu(l), p = 0, r = c = i = l = o;
          }
          function rn() {
            return l === o ? s : D(Qe());
          }
          function pn() {
            var Z = Qe(), On = C(Z);
            if (r = arguments, i = this, c = Z, On) {
              if (l === o)
                return y(c);
              if (_)
                return Vu(l), l = ue(M, t), A(c);
            }
            return l === o && (l = ue(M, t)), s;
          }
          return pn.cancel = gn, pn.flush = rn, pn;
        }
        var M3 = O(function(n, t) {
          return Ou(n, 1, t);
        }), b3 = O(function(n, t, e) {
          return Ou(n, yn(t) || 0, e);
        });
        function W3(n) {
          return $n(n, fr);
        }
        function je(n, t) {
          if (typeof n != "function" || t != null && typeof t != "function")
            throw new wn(T);
          var e = function() {
            var r = arguments, i = t ? t.apply(this, r) : r[0], f = e.cache;
            if (f.has(i))
              return f.get(i);
            var s = n.apply(this, r);
            return e.cache = f.set(i, s) || f, s;
          };
          return e.cache = new (je.Cache || zn)(), e;
        }
        je.Cache = zn;
        function nr(n) {
          if (typeof n != "function")
            throw new wn(T);
          return function() {
            var t = arguments;
            switch (t.length) {
              case 0:
                return !n.call(this);
              case 1:
                return !n.call(this, t[0]);
              case 2:
                return !n.call(this, t[0], t[1]);
              case 3:
                return !n.call(this, t[0], t[1], t[2]);
            }
            return !n.apply(this, t);
          };
        }
        function D3(n) {
          return Ff(2, n);
        }
        var B3 = Aa(function(n, t) {
          t = t.length == 1 && L(t[0]) ? $(t[0], an(S())) : $(Q(t, 1), an(S()));
          var e = t.length;
          return O(function(r) {
            for (var i = -1, f = j(r.length, e); ++i < f; )
              r[i] = t[i].call(this, r[i]);
            return ln(n, this, r);
          });
        }), _i = O(function(n, t) {
          var e = Qn(t, Wt(_i));
          return $n(n, Pn, o, t, e);
        }), $f = O(function(n, t) {
          var e = Qn(t, Wt($f));
          return $n(n, Pt, o, t, e);
        }), U3 = qn(function(n, t) {
          return $n(n, Ft, o, o, o, t);
        });
        function P3(n, t) {
          if (typeof n != "function")
            throw new wn(T);
          return t = t === o ? t : I(t), O(n, t);
        }
        function F3(n, t) {
          if (typeof n != "function")
            throw new wn(T);
          return t = t == null ? 0 : X(I(t), 0), O(function(e) {
            var r = e[t], i = rt(e, 0, t);
            return r && Vn(i, r), ln(n, this, i);
          });
        }
        function H3(n, t, e) {
          var r = !0, i = !0;
          if (typeof n != "function")
            throw new wn(T);
          return q(e) && (r = "leading" in e ? !!e.leading : r, i = "trailing" in e ? !!e.trailing : i), Gf(n, t, {
            leading: r,
            maxWait: t,
            trailing: i
          });
        }
        function N3(n) {
          return Pf(n, 1);
        }
        function z3(n, t) {
          return _i(ni(t), n);
        }
        function G3() {
          if (!arguments.length)
            return [];
          var n = arguments[0];
          return L(n) ? n : [n];
        }
        function $3(n) {
          return mn(n, _t);
        }
        function q3(n, t) {
          return t = typeof t == "function" ? t : o, mn(n, _t, t);
        }
        function K3(n) {
          return mn(n, Mn | _t);
        }
        function k3(n, t) {
          return t = typeof t == "function" ? t : o, mn(n, Mn | _t, t);
        }
        function Z3(n, t) {
          return t == null || Ru(n, t, J(t));
        }
        function Rn(n, t) {
          return n === t || n !== n && t !== t;
        }
        var Y3 = ke(Gr), X3 = ke(function(n, t) {
          return n >= t;
        }), pt = Bu(/* @__PURE__ */ function() {
          return arguments;
        }()) ? Bu : function(n) {
          return K(n) && P.call(n, "callee") && !Au.call(n, "callee");
        }, L = h.isArray, J3 = eu ? an(eu) : ia;
        function fn(n) {
          return n != null && tr(n.length) && !kn(n);
        }
        function k(n) {
          return K(n) && fn(n);
        }
        function V3(n) {
          return n === !0 || n === !1 || K(n) && tn(n) == Ht;
        }
        var it = cl || Li, Q3 = ru ? an(ru) : ua;
        function j3(n) {
          return K(n) && n.nodeType === 1 && !fe(n);
        }
        function nh(n) {
          if (n == null)
            return !0;
          if (fn(n) && (L(n) || typeof n == "string" || typeof n.splice == "function" || it(n) || Dt(n) || pt(n)))
            return !n.length;
          var t = nn(n);
          if (t == Tn || t == En)
            return !n.size;
          if (ie(n))
            return !Kr(n).length;
          for (var e in n)
            if (P.call(n, e))
              return !1;
          return !0;
        }
        function th(n, t) {
          return te(n, t);
        }
        function eh(n, t, e) {
          e = typeof e == "function" ? e : o;
          var r = e ? e(n, t) : o;
          return r === o ? te(n, t, o, e) : !!r;
        }
        function vi(n) {
          if (!K(n))
            return !1;
          var t = tn(n);
          return t == ge || t == To || typeof n.message == "string" && typeof n.name == "string" && !fe(n);
        }
        function rh(n) {
          return typeof n == "number" && yu(n);
        }
        function kn(n) {
          if (!q(n))
            return !1;
          var t = tn(n);
          return t == pe || t == Oi || t == Co || t == Lo;
        }
        function qf(n) {
          return typeof n == "number" && n == I(n);
        }
        function tr(n) {
          return typeof n == "number" && n > -1 && n % 1 == 0 && n <= xt;
        }
        function q(n) {
          var t = typeof n;
          return n != null && (t == "object" || t == "function");
        }
        function K(n) {
          return n != null && typeof n == "object";
        }
        var Kf = iu ? an(iu) : oa;
        function ih(n, t) {
          return n === t || qr(n, t, oi(t));
        }
        function uh(n, t, e) {
          return e = typeof e == "function" ? e : o, qr(n, t, oi(t), e);
        }
        function fh(n) {
          return kf(n) && n != +n;
        }
        function oh(n) {
          if (qa(n))
            throw new E(G);
          return Uu(n);
        }
        function sh(n) {
          return n === null;
        }
        function lh(n) {
          return n == null;
        }
        function kf(n) {
          return typeof n == "number" || K(n) && tn(n) == zt;
        }
        function fe(n) {
          if (!K(n) || tn(n) != Hn)
            return !1;
          var t = Ie(n);
          if (t === null)
            return !0;
          var e = P.call(t, "constructor") && t.constructor;
          return typeof e == "function" && e instanceof e && Ce.call(e) == ul;
        }
        var wi = uu ? an(uu) : sa;
        function ah(n) {
          return qf(n) && n >= -9007199254740991 && n <= xt;
        }
        var Zf = fu ? an(fu) : la;
        function er(n) {
          return typeof n == "string" || !L(n) && K(n) && tn(n) == $t;
        }
        function hn(n) {
          return typeof n == "symbol" || K(n) && tn(n) == de;
        }
        var Dt = ou ? an(ou) : aa;
        function ch(n) {
          return n === o;
        }
        function hh(n) {
          return K(n) && nn(n) == qt;
        }
        function gh(n) {
          return K(n) && tn(n) == Ro;
        }
        var ph = ke(kr), dh = ke(function(n, t) {
          return n <= t;
        });
        function Yf(n) {
          if (!n)
            return [];
          if (fn(n))
            return er(n) ? Ln(n) : un(n);
          if (Zt && n[Zt])
            return Zs(n[Zt]());
          var t = nn(n), e = t == Tn ? Wr : t == En ? Ae : Bt;
          return e(n);
        }
        function Zn(n) {
          if (!n)
            return n === 0 ? n : 0;
          if (n = yn(n), n === ae || n === -1 / 0) {
            var t = n < 0 ? -1 : 1;
            return t * mo;
          }
          return n === n ? n : 0;
        }
        function I(n) {
          var t = Zn(n), e = t % 1;
          return t === t ? e ? t - e : t : 0;
        }
        function Xf(n) {
          return n ? at(I(n), 0, bn) : 0;
        }
        function yn(n) {
          if (typeof n == "number")
            return n;
          if (hn(n))
            return ce;
          if (q(n)) {
            var t = typeof n.valueOf == "function" ? n.valueOf() : n;
            n = q(t) ? t + "" : t;
          }
          if (typeof n != "string")
            return n === 0 ? n : +n;
          n = gu(n);
          var e = Jo.test(n);
          return e || Qo.test(n) ? Os(n.slice(2), e ? 2 : 8) : Xo.test(n) ? ce : +n;
        }
        function Jf(n) {
          return Dn(n, on(n));
        }
        function _h(n) {
          return n ? at(I(n), -9007199254740991, xt) : n === 0 ? n : 0;
        }
        function U(n) {
          return n == null ? "" : cn(n);
        }
        var vh = Mt(function(n, t) {
          if (ie(t) || fn(t)) {
            Dn(t, J(t), n);
            return;
          }
          for (var e in t)
            P.call(t, e) && Qt(n, e, t[e]);
        }), Vf = Mt(function(n, t) {
          Dn(t, on(t), n);
        }), rr = Mt(function(n, t, e, r) {
          Dn(t, on(t), n, r);
        }), wh = Mt(function(n, t, e, r) {
          Dn(t, J(t), n, r);
        }), xh = qn(Hr);
        function mh(n, t) {
          var e = Ot(n);
          return t == null ? e : Iu(e, t);
        }
        var Ah = O(function(n, t) {
          n = F(n);
          var e = -1, r = t.length, i = r > 2 ? t[2] : o;
          for (i && en(t[0], t[1], i) && (r = 1); ++e < r; )
            for (var f = t[e], s = on(f), l = -1, c = s.length; ++l < c; ) {
              var p = s[l], d = n[p];
              (d === o || Rn(d, Lt[p]) && !P.call(n, p)) && (n[p] = f[p]);
            }
          return n;
        }), Sh = O(function(n) {
          return n.push(o, pf), ln(Qf, o, n);
        });
        function yh(n, t) {
          return lu(n, S(t, 3), Wn);
        }
        function Ch(n, t) {
          return lu(n, S(t, 3), zr);
        }
        function Th(n, t) {
          return n == null ? n : Nr(n, S(t, 3), on);
        }
        function Eh(n, t) {
          return n == null ? n : Wu(n, S(t, 3), on);
        }
        function Lh(n, t) {
          return n && Wn(n, S(t, 3));
        }
        function Ih(n, t) {
          return n && zr(n, S(t, 3));
        }
        function Rh(n) {
          return n == null ? [] : Fe(n, J(n));
        }
        function Oh(n) {
          return n == null ? [] : Fe(n, on(n));
        }
        function xi(n, t, e) {
          var r = n == null ? o : ct(n, t);
          return r === o ? e : r;
        }
        function Mh(n, t) {
          return n != null && vf(n, t, na);
        }
        function mi(n, t) {
          return n != null && vf(n, t, ta);
        }
        var bh = lf(function(n, t, e) {
          t != null && typeof t.toString != "function" && (t = Te.call(t)), n[t] = e;
        }, Si(sn)), Wh = lf(function(n, t, e) {
          t != null && typeof t.toString != "function" && (t = Te.call(t)), P.call(n, t) ? n[t].push(e) : n[t] = [e];
        }, S), Dh = O(ne);
        function J(n) {
          return fn(n) ? Eu(n) : Kr(n);
        }
        function on(n) {
          return fn(n) ? Eu(n, !0) : ca(n);
        }
        function Bh(n, t) {
          var e = {};
          return t = S(t, 3), Wn(n, function(r, i, f) {
            Gn(e, t(r, i, f), r);
          }), e;
        }
        function Uh(n, t) {
          var e = {};
          return t = S(t, 3), Wn(n, function(r, i, f) {
            Gn(e, i, t(r, i, f));
          }), e;
        }
        var Ph = Mt(function(n, t, e) {
          He(n, t, e);
        }), Qf = Mt(function(n, t, e, r) {
          He(n, t, e, r);
        }), Fh = qn(function(n, t) {
          var e = {};
          if (n == null)
            return e;
          var r = !1;
          t = $(t, function(f) {
            return f = et(f, n), r || (r = f.length > 1), f;
          }), Dn(n, ui(n), e), r && (e = mn(e, Mn | se | _t, ba));
          for (var i = t.length; i--; )
            Vr(e, t[i]);
          return e;
        });
        function Hh(n, t) {
          return jf(n, nr(S(t)));
        }
        var Nh = qn(function(n, t) {
          return n == null ? {} : ga(n, t);
        });
        function jf(n, t) {
          if (n == null)
            return {};
          var e = $(ui(n), function(r) {
            return [r];
          });
          return t = S(t), $u(n, e, function(r, i) {
            return t(r, i[0]);
          });
        }
        function zh(n, t, e) {
          t = et(t, n);
          var r = -1, i = t.length;
          for (i || (i = 1, n = o); ++r < i; ) {
            var f = n == null ? o : n[Bn(t[r])];
            f === o && (r = i, f = e), n = kn(f) ? f.call(n) : f;
          }
          return n;
        }
        function Gh(n, t, e) {
          return n == null ? n : ee(n, t, e);
        }
        function $h(n, t, e, r) {
          return r = typeof r == "function" ? r : o, n == null ? n : ee(n, t, e, r);
        }
        var no = hf(J), to = hf(on);
        function qh(n, t, e) {
          var r = L(n), i = r || it(n) || Dt(n);
          if (t = S(t, 4), e == null) {
            var f = n && n.constructor;
            i ? e = r ? new f() : [] : q(n) ? e = kn(f) ? Ot(Ie(n)) : {} : e = {};
          }
          return (i ? vn : Wn)(n, function(s, l, c) {
            return t(e, s, l, c);
          }), e;
        }
        function Kh(n, t) {
          return n == null ? !0 : Vr(n, t);
        }
        function kh(n, t, e) {
          return n == null ? n : Yu(n, t, ni(e));
        }
        function Zh(n, t, e, r) {
          return r = typeof r == "function" ? r : o, n == null ? n : Yu(n, t, ni(e), r);
        }
        function Bt(n) {
          return n == null ? [] : br(n, J(n));
        }
        function Yh(n) {
          return n == null ? [] : br(n, on(n));
        }
        function Xh(n, t, e) {
          return e === o && (e = t, t = o), e !== o && (e = yn(e), e = e === e ? e : 0), t !== o && (t = yn(t), t = t === t ? t : 0), at(yn(n), t, e);
        }
        function Jh(n, t, e) {
          return t = Zn(t), e === o ? (e = t, t = 0) : e = Zn(e), n = yn(n), ea(n, t, e);
        }
        function Vh(n, t, e) {
          if (e && typeof e != "boolean" && en(n, t, e) && (t = e = o), e === o && (typeof t == "boolean" ? (e = t, t = o) : typeof n == "boolean" && (e = n, n = o)), n === o && t === o ? (n = 0, t = 1) : (n = Zn(n), t === o ? (t = n, n = 0) : t = Zn(t)), n > t) {
            var r = n;
            n = t, t = r;
          }
          if (e || n % 1 || t % 1) {
            var i = Cu();
            return j(n + i * (t - n + Rs("1e-" + ((i + "").length - 1))), t);
          }
          return Yr(n, t);
        }
        var Qh = bt(function(n, t, e) {
          return t = t.toLowerCase(), n + (e ? eo(t) : t);
        });
        function eo(n) {
          return Ai(U(n).toLowerCase());
        }
        function ro(n) {
          return n = U(n), n && n.replace(ns, Gs).replace(xs, "");
        }
        function jh(n, t, e) {
          n = U(n), t = cn(t);
          var r = n.length;
          e = e === o ? r : at(I(e), 0, r);
          var i = e;
          return e -= t.length, e >= 0 && n.slice(e, i) == t;
        }
        function n0(n) {
          return n = U(n), n && Do.test(n) ? n.replace(Wi, $s) : n;
        }
        function t0(n) {
          return n = U(n), n && No.test(n) ? n.replace(_r, "\\$&") : n;
        }
        var e0 = bt(function(n, t, e) {
          return n + (e ? "-" : "") + t.toLowerCase();
        }), r0 = bt(function(n, t, e) {
          return n + (e ? " " : "") + t.toLowerCase();
        }), i0 = ff("toLowerCase");
        function u0(n, t, e) {
          n = U(n), t = I(t);
          var r = t ? Tt(n) : 0;
          if (!t || r >= t)
            return n;
          var i = (t - r) / 2;
          return Ke(be(i), e) + n + Ke(Me(i), e);
        }
        function f0(n, t, e) {
          n = U(n), t = I(t);
          var r = t ? Tt(n) : 0;
          return t && r < t ? n + Ke(t - r, e) : n;
        }
        function o0(n, t, e) {
          n = U(n), t = I(t);
          var r = t ? Tt(n) : 0;
          return t && r < t ? Ke(t - r, e) + n : n;
        }
        function s0(n, t, e) {
          return e || t == null ? t = 0 : t && (t = +t), dl(U(n).replace(vr, ""), t || 0);
        }
        function l0(n, t, e) {
          return (e ? en(n, t, e) : t === o) ? t = 1 : t = I(t), Xr(U(n), t);
        }
        function a0() {
          var n = arguments, t = U(n[0]);
          return n.length < 3 ? t : t.replace(n[1], n[2]);
        }
        var c0 = bt(function(n, t, e) {
          return n + (e ? "_" : "") + t.toLowerCase();
        });
        function h0(n, t, e) {
          return e && typeof e != "number" && en(n, t, e) && (t = e = o), e = e === o ? bn : e >>> 0, e ? (n = U(n), n && (typeof t == "string" || t != null && !wi(t)) && (t = cn(t), !t && Ct(n)) ? rt(Ln(n), 0, e) : n.split(t, e)) : [];
        }
        var g0 = bt(function(n, t, e) {
          return n + (e ? " " : "") + Ai(t);
        });
        function p0(n, t, e) {
          return n = U(n), e = e == null ? 0 : at(I(e), 0, n.length), t = cn(t), n.slice(e, e + t.length) == t;
        }
        function d0(n, t, e) {
          var r = u.templateSettings;
          e && en(n, t, e) && (t = o), n = U(n), t = rr({}, t, r, gf);
          var i = rr({}, t.imports, r.imports, gf), f = J(i), s = br(i, f), l, c, p = 0, d = t.interpolate || _e, _ = "__p += '", w = Dr(
            (t.escape || _e).source + "|" + d.source + "|" + (d === Di ? Yo : _e).source + "|" + (t.evaluate || _e).source + "|$",
            "g"
          ), A = "//# sourceURL=" + (P.call(t, "sourceURL") ? (t.sourceURL + "").replace(/\s/g, " ") : "lodash.templateSources[" + ++Cs + "]") + `
`;
          n.replace(w, function(C, M, D, gn, rn, pn) {
            return D || (D = gn), _ += n.slice(p, pn).replace(ts, qs), M && (l = !0, _ += `' +
__e(` + M + `) +
'`), rn && (c = !0, _ += `';
` + rn + `;
__p += '`), D && (_ += `' +
((__t = (` + D + `)) == null ? '' : __t) +
'`), p = pn + C.length, C;
          }), _ += `';
`;
          var y = P.call(t, "variable") && t.variable;
          if (!y)
            _ = `with (obj) {
` + _ + `
}
`;
          else if (ko.test(y))
            throw new E(dn);
          _ = (c ? _.replace(Oo, "") : _).replace(Mo, "$1").replace(bo, "$1;"), _ = "function(" + (y || "obj") + `) {
` + (y ? "" : `obj || (obj = {});
`) + "var __t, __p = ''" + (l ? ", __e = _.escape" : "") + (c ? `, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
` : `;
`) + _ + `return __p
}`;
          var R = uo(function() {
            return B(f, A + "return " + _).apply(o, s);
          });
          if (R.source = _, vi(R))
            throw R;
          return R;
        }
        function _0(n) {
          return U(n).toLowerCase();
        }
        function v0(n) {
          return U(n).toUpperCase();
        }
        function w0(n, t, e) {
          if (n = U(n), n && (e || t === o))
            return gu(n);
          if (!n || !(t = cn(t)))
            return n;
          var r = Ln(n), i = Ln(t), f = pu(r, i), s = du(r, i) + 1;
          return rt(r, f, s).join("");
        }
        function x0(n, t, e) {
          if (n = U(n), n && (e || t === o))
            return n.slice(0, vu(n) + 1);
          if (!n || !(t = cn(t)))
            return n;
          var r = Ln(n), i = du(r, Ln(t)) + 1;
          return rt(r, 0, i).join("");
        }
        function m0(n, t, e) {
          if (n = U(n), n && (e || t === o))
            return n.replace(vr, "");
          if (!n || !(t = cn(t)))
            return n;
          var r = Ln(n), i = pu(r, Ln(t));
          return rt(r, i).join("");
        }
        function A0(n, t) {
          var e = go, r = po;
          if (q(t)) {
            var i = "separator" in t ? t.separator : i;
            e = "length" in t ? I(t.length) : e, r = "omission" in t ? cn(t.omission) : r;
          }
          n = U(n);
          var f = n.length;
          if (Ct(n)) {
            var s = Ln(n);
            f = s.length;
          }
          if (e >= f)
            return n;
          var l = e - Tt(r);
          if (l < 1)
            return r;
          var c = s ? rt(s, 0, l).join("") : n.slice(0, l);
          if (i === o)
            return c + r;
          if (s && (l += c.length - l), wi(i)) {
            if (n.slice(l).search(i)) {
              var p, d = c;
              for (i.global || (i = Dr(i.source, U(Bi.exec(i)) + "g")), i.lastIndex = 0; p = i.exec(d); )
                var _ = p.index;
              c = c.slice(0, _ === o ? l : _);
            }
          } else if (n.indexOf(cn(i), l) != l) {
            var w = c.lastIndexOf(i);
            w > -1 && (c = c.slice(0, w));
          }
          return c + r;
        }
        function S0(n) {
          return n = U(n), n && Wo.test(n) ? n.replace(bi, Vs) : n;
        }
        var y0 = bt(function(n, t, e) {
          return n + (e ? " " : "") + t.toUpperCase();
        }), Ai = ff("toUpperCase");
        function io(n, t, e) {
          return n = U(n), t = e ? o : t, t === o ? ks(n) ? nl(n) : Ps(n) : n.match(t) || [];
        }
        var uo = O(function(n, t) {
          try {
            return ln(n, o, t);
          } catch (e) {
            return vi(e) ? e : new E(e);
          }
        }), C0 = qn(function(n, t) {
          return vn(t, function(e) {
            e = Bn(e), Gn(n, e, di(n[e], n));
          }), n;
        });
        function T0(n) {
          var t = n == null ? 0 : n.length, e = S();
          return n = t ? $(n, function(r) {
            if (typeof r[1] != "function")
              throw new wn(T);
            return [e(r[0]), r[1]];
          }) : [], O(function(r) {
            for (var i = -1; ++i < t; ) {
              var f = n[i];
              if (ln(f[0], this, r))
                return ln(f[1], this, r);
            }
          });
        }
        function E0(n) {
          return Vl(mn(n, Mn));
        }
        function Si(n) {
          return function() {
            return n;
          };
        }
        function L0(n, t) {
          return n == null || n !== n ? t : n;
        }
        var I0 = sf(), R0 = sf(!0);
        function sn(n) {
          return n;
        }
        function yi(n) {
          return Pu(typeof n == "function" ? n : mn(n, Mn));
        }
        function O0(n) {
          return Hu(mn(n, Mn));
        }
        function M0(n, t) {
          return Nu(n, mn(t, Mn));
        }
        var b0 = O(function(n, t) {
          return function(e) {
            return ne(e, n, t);
          };
        }), W0 = O(function(n, t) {
          return function(e) {
            return ne(n, e, t);
          };
        });
        function Ci(n, t, e) {
          var r = J(t), i = Fe(t, r);
          e == null && !(q(t) && (i.length || !r.length)) && (e = t, t = n, n = this, i = Fe(t, J(t)));
          var f = !(q(e) && "chain" in e) || !!e.chain, s = kn(n);
          return vn(i, function(l) {
            var c = t[l];
            n[l] = c, s && (n.prototype[l] = function() {
              var p = this.__chain__;
              if (f || p) {
                var d = n(this.__wrapped__), _ = d.__actions__ = un(this.__actions__);
                return _.push({ func: c, args: arguments, thisArg: n }), d.__chain__ = p, d;
              }
              return c.apply(n, Vn([this.value()], arguments));
            });
          }), n;
        }
        function D0() {
          return V._ === this && (V._ = fl), this;
        }
        function Ti() {
        }
        function B0(n) {
          return n = I(n), O(function(t) {
            return zu(t, n);
          });
        }
        var U0 = ei($), P0 = ei(su), F0 = ei(Lr);
        function fo(n) {
          return li(n) ? Ir(Bn(n)) : pa(n);
        }
        function H0(n) {
          return function(t) {
            return n == null ? o : ct(n, t);
          };
        }
        var N0 = af(), z0 = af(!0);
        function Ei() {
          return [];
        }
        function Li() {
          return !1;
        }
        function G0() {
          return {};
        }
        function $0() {
          return "";
        }
        function q0() {
          return !0;
        }
        function K0(n, t) {
          if (n = I(n), n < 1 || n > xt)
            return [];
          var e = bn, r = j(n, bn);
          t = S(t), n -= bn;
          for (var i = Mr(r, t); ++e < n; )
            t(e);
          return i;
        }
        function k0(n) {
          return L(n) ? $(n, Bn) : hn(n) ? [n] : un(Ef(U(n)));
        }
        function Z0(n) {
          var t = ++il;
          return U(n) + t;
        }
        var Y0 = qe(function(n, t) {
          return n + t;
        }, 0), X0 = ri("ceil"), J0 = qe(function(n, t) {
          return n / t;
        }, 1), V0 = ri("floor");
        function Q0(n) {
          return n && n.length ? Pe(n, sn, Gr) : o;
        }
        function j0(n, t) {
          return n && n.length ? Pe(n, S(t, 2), Gr) : o;
        }
        function n1(n) {
          return cu(n, sn);
        }
        function t1(n, t) {
          return cu(n, S(t, 2));
        }
        function e1(n) {
          return n && n.length ? Pe(n, sn, kr) : o;
        }
        function r1(n, t) {
          return n && n.length ? Pe(n, S(t, 2), kr) : o;
        }
        var i1 = qe(function(n, t) {
          return n * t;
        }, 1), u1 = ri("round"), f1 = qe(function(n, t) {
          return n - t;
        }, 0);
        function o1(n) {
          return n && n.length ? Or(n, sn) : 0;
        }
        function s1(n, t) {
          return n && n.length ? Or(n, S(t, 2)) : 0;
        }
        return u.after = O3, u.ary = Pf, u.assign = vh, u.assignIn = Vf, u.assignInWith = rr, u.assignWith = wh, u.at = xh, u.before = Ff, u.bind = di, u.bindAll = C0, u.bindKey = Hf, u.castArray = G3, u.chain = Df, u.chunk = Va, u.compact = Qa, u.concat = ja, u.cond = T0, u.conforms = E0, u.constant = Si, u.countBy = o3, u.create = mh, u.curry = Nf, u.curryRight = zf, u.debounce = Gf, u.defaults = Ah, u.defaultsDeep = Sh, u.defer = M3, u.delay = b3, u.difference = nc, u.differenceBy = tc, u.differenceWith = ec, u.drop = rc, u.dropRight = ic, u.dropRightWhile = uc, u.dropWhile = fc, u.fill = oc, u.filter = l3, u.flatMap = h3, u.flatMapDeep = g3, u.flatMapDepth = p3, u.flatten = Of, u.flattenDeep = sc, u.flattenDepth = lc, u.flip = W3, u.flow = I0, u.flowRight = R0, u.fromPairs = ac, u.functions = Rh, u.functionsIn = Oh, u.groupBy = d3, u.initial = hc, u.intersection = gc, u.intersectionBy = pc, u.intersectionWith = dc, u.invert = bh, u.invertBy = Wh, u.invokeMap = v3, u.iteratee = yi, u.keyBy = w3, u.keys = J, u.keysIn = on, u.map = Ve, u.mapKeys = Bh, u.mapValues = Uh, u.matches = O0, u.matchesProperty = M0, u.memoize = je, u.merge = Ph, u.mergeWith = Qf, u.method = b0, u.methodOf = W0, u.mixin = Ci, u.negate = nr, u.nthArg = B0, u.omit = Fh, u.omitBy = Hh, u.once = D3, u.orderBy = x3, u.over = U0, u.overArgs = B3, u.overEvery = P0, u.overSome = F0, u.partial = _i, u.partialRight = $f, u.partition = m3, u.pick = Nh, u.pickBy = jf, u.property = fo, u.propertyOf = H0, u.pull = xc, u.pullAll = bf, u.pullAllBy = mc, u.pullAllWith = Ac, u.pullAt = Sc, u.range = N0, u.rangeRight = z0, u.rearg = U3, u.reject = y3, u.remove = yc, u.rest = P3, u.reverse = gi, u.sampleSize = T3, u.set = Gh, u.setWith = $h, u.shuffle = E3, u.slice = Cc, u.sortBy = R3, u.sortedUniq = Mc, u.sortedUniqBy = bc, u.split = h0, u.spread = F3, u.tail = Wc, u.take = Dc, u.takeRight = Bc, u.takeRightWhile = Uc, u.takeWhile = Pc, u.tap = Qc, u.throttle = H3, u.thru = Je, u.toArray = Yf, u.toPairs = no, u.toPairsIn = to, u.toPath = k0, u.toPlainObject = Jf, u.transform = qh, u.unary = N3, u.union = Fc, u.unionBy = Hc, u.unionWith = Nc, u.uniq = zc, u.uniqBy = Gc, u.uniqWith = $c, u.unset = Kh, u.unzip = pi, u.unzipWith = Wf, u.update = kh, u.updateWith = Zh, u.values = Bt, u.valuesIn = Yh, u.without = qc, u.words = io, u.wrap = z3, u.xor = Kc, u.xorBy = kc, u.xorWith = Zc, u.zip = Yc, u.zipObject = Xc, u.zipObjectDeep = Jc, u.zipWith = Vc, u.entries = no, u.entriesIn = to, u.extend = Vf, u.extendWith = rr, Ci(u, u), u.add = Y0, u.attempt = uo, u.camelCase = Qh, u.capitalize = eo, u.ceil = X0, u.clamp = Xh, u.clone = $3, u.cloneDeep = K3, u.cloneDeepWith = k3, u.cloneWith = q3, u.conformsTo = Z3, u.deburr = ro, u.defaultTo = L0, u.divide = J0, u.endsWith = jh, u.eq = Rn, u.escape = n0, u.escapeRegExp = t0, u.every = s3, u.find = a3, u.findIndex = If, u.findKey = yh, u.findLast = c3, u.findLastIndex = Rf, u.findLastKey = Ch, u.floor = V0, u.forEach = Bf, u.forEachRight = Uf, u.forIn = Th, u.forInRight = Eh, u.forOwn = Lh, u.forOwnRight = Ih, u.get = xi, u.gt = Y3, u.gte = X3, u.has = Mh, u.hasIn = mi, u.head = Mf, u.identity = sn, u.includes = _3, u.indexOf = cc, u.inRange = Jh, u.invoke = Dh, u.isArguments = pt, u.isArray = L, u.isArrayBuffer = J3, u.isArrayLike = fn, u.isArrayLikeObject = k, u.isBoolean = V3, u.isBuffer = it, u.isDate = Q3, u.isElement = j3, u.isEmpty = nh, u.isEqual = th, u.isEqualWith = eh, u.isError = vi, u.isFinite = rh, u.isFunction = kn, u.isInteger = qf, u.isLength = tr, u.isMap = Kf, u.isMatch = ih, u.isMatchWith = uh, u.isNaN = fh, u.isNative = oh, u.isNil = lh, u.isNull = sh, u.isNumber = kf, u.isObject = q, u.isObjectLike = K, u.isPlainObject = fe, u.isRegExp = wi, u.isSafeInteger = ah, u.isSet = Zf, u.isString = er, u.isSymbol = hn, u.isTypedArray = Dt, u.isUndefined = ch, u.isWeakMap = hh, u.isWeakSet = gh, u.join = _c, u.kebabCase = e0, u.last = Sn, u.lastIndexOf = vc, u.lowerCase = r0, u.lowerFirst = i0, u.lt = ph, u.lte = dh, u.max = Q0, u.maxBy = j0, u.mean = n1, u.meanBy = t1, u.min = e1, u.minBy = r1, u.stubArray = Ei, u.stubFalse = Li, u.stubObject = G0, u.stubString = $0, u.stubTrue = q0, u.multiply = i1, u.nth = wc, u.noConflict = D0, u.noop = Ti, u.now = Qe, u.pad = u0, u.padEnd = f0, u.padStart = o0, u.parseInt = s0, u.random = Vh, u.reduce = A3, u.reduceRight = S3, u.repeat = l0, u.replace = a0, u.result = zh, u.round = u1, u.runInContext = a, u.sample = C3, u.size = L3, u.snakeCase = c0, u.some = I3, u.sortedIndex = Tc, u.sortedIndexBy = Ec, u.sortedIndexOf = Lc, u.sortedLastIndex = Ic, u.sortedLastIndexBy = Rc, u.sortedLastIndexOf = Oc, u.startCase = g0, u.startsWith = p0, u.subtract = f1, u.sum = o1, u.sumBy = s1, u.template = d0, u.times = K0, u.toFinite = Zn, u.toInteger = I, u.toLength = Xf, u.toLower = _0, u.toNumber = yn, u.toSafeInteger = _h, u.toString = U, u.toUpper = v0, u.trim = w0, u.trimEnd = x0, u.trimStart = m0, u.truncate = A0, u.unescape = S0, u.uniqueId = Z0, u.upperCase = y0, u.upperFirst = Ai, u.each = Bf, u.eachRight = Uf, u.first = Mf, Ci(u, function() {
          var n = {};
          return Wn(u, function(t, e) {
            P.call(u.prototype, e) || (n[e] = t);
          }), n;
        }(), { chain: !1 }), u.VERSION = m, vn(["bind", "bindKey", "curry", "curryRight", "partial", "partialRight"], function(n) {
          u[n].placeholder = u;
        }), vn(["drop", "take"], function(n, t) {
          W.prototype[n] = function(e) {
            e = e === o ? 1 : X(I(e), 0);
            var r = this.__filtered__ && !t ? new W(this) : this.clone();
            return r.__filtered__ ? r.__takeCount__ = j(e, r.__takeCount__) : r.__views__.push({
              size: j(e, bn),
              type: n + (r.__dir__ < 0 ? "Right" : "")
            }), r;
          }, W.prototype[n + "Right"] = function(e) {
            return this.reverse()[n](e).reverse();
          };
        }), vn(["filter", "map", "takeWhile"], function(n, t) {
          var e = t + 1, r = e == Ri || e == xo;
          W.prototype[n] = function(i) {
            var f = this.clone();
            return f.__iteratees__.push({
              iteratee: S(i, 3),
              type: e
            }), f.__filtered__ = f.__filtered__ || r, f;
          };
        }), vn(["head", "last"], function(n, t) {
          var e = "take" + (t ? "Right" : "");
          W.prototype[n] = function() {
            return this[e](1).value()[0];
          };
        }), vn(["initial", "tail"], function(n, t) {
          var e = "drop" + (t ? "" : "Right");
          W.prototype[n] = function() {
            return this.__filtered__ ? new W(this) : this[e](1);
          };
        }), W.prototype.compact = function() {
          return this.filter(sn);
        }, W.prototype.find = function(n) {
          return this.filter(n).head();
        }, W.prototype.findLast = function(n) {
          return this.reverse().find(n);
        }, W.prototype.invokeMap = O(function(n, t) {
          return typeof n == "function" ? new W(this) : this.map(function(e) {
            return ne(e, n, t);
          });
        }), W.prototype.reject = function(n) {
          return this.filter(nr(S(n)));
        }, W.prototype.slice = function(n, t) {
          n = I(n);
          var e = this;
          return e.__filtered__ && (n > 0 || t < 0) ? new W(e) : (n < 0 ? e = e.takeRight(-n) : n && (e = e.drop(n)), t !== o && (t = I(t), e = t < 0 ? e.dropRight(-t) : e.take(t - n)), e);
        }, W.prototype.takeRightWhile = function(n) {
          return this.reverse().takeWhile(n).reverse();
        }, W.prototype.toArray = function() {
          return this.take(bn);
        }, Wn(W.prototype, function(n, t) {
          var e = /^(?:filter|find|map|reject)|While$/.test(t), r = /^(?:head|last)$/.test(t), i = u[r ? "take" + (t == "last" ? "Right" : "") : t], f = r || /^find/.test(t);
          i && (u.prototype[t] = function() {
            var s = this.__wrapped__, l = r ? [1] : arguments, c = s instanceof W, p = l[0], d = c || L(s), _ = function(M) {
              var D = i.apply(u, Vn([M], l));
              return r && w ? D[0] : D;
            };
            d && e && typeof p == "function" && p.length != 1 && (c = d = !1);
            var w = this.__chain__, A = !!this.__actions__.length, y = f && !w, R = c && !A;
            if (!f && d) {
              s = R ? s : new W(this);
              var C = n.apply(s, l);
              return C.__actions__.push({ func: Je, args: [_], thisArg: o }), new xn(C, w);
            }
            return y && R ? n.apply(this, l) : (C = this.thru(_), y ? r ? C.value()[0] : C.value() : C);
          });
        }), vn(["pop", "push", "shift", "sort", "splice", "unshift"], function(n) {
          var t = Se[n], e = /^(?:push|sort|unshift)$/.test(n) ? "tap" : "thru", r = /^(?:pop|shift)$/.test(n);
          u.prototype[n] = function() {
            var i = arguments;
            if (r && !this.__chain__) {
              var f = this.value();
              return t.apply(L(f) ? f : [], i);
            }
            return this[e](function(s) {
              return t.apply(L(s) ? s : [], i);
            });
          };
        }), Wn(W.prototype, function(n, t) {
          var e = u[t];
          if (e) {
            var r = e.name + "";
            P.call(Rt, r) || (Rt[r] = []), Rt[r].push({ name: t, func: e });
          }
        }), Rt[$e(o, wt).name] = [{
          name: "wrapper",
          func: o
        }], W.prototype.clone = Sl, W.prototype.reverse = yl, W.prototype.value = Cl, u.prototype.at = jc, u.prototype.chain = n3, u.prototype.commit = t3, u.prototype.next = e3, u.prototype.plant = i3, u.prototype.reverse = u3, u.prototype.toJSON = u.prototype.valueOf = u.prototype.value = f3, u.prototype.first = u.prototype.head, Zt && (u.prototype[Zt] = r3), u;
      }, Et = tl();
      ft ? ((ft.exports = Et)._ = Et, yr._ = Et) : V._ = Et;
    }).call(m1);
  }(oe, oe.exports)), oe.exports;
}
A1();
const S1 = "正在思考...";
class y1 extends x1 {
  constructor(v, o) {
    super(), this.options = {
      audioWorkletModuleUrl: o.audioWorkletModuleUrl || "./volume-meter-processor.js",
      // voiceWakeUp: false,
      wakeUpKeywords: o.wakeUpKeywords || ["小源小源", "小源%%小源"],
      exitVoiceKeywords: o.exitVoiceKeywords || ["再见小源", "再见%%小源"],
      wakeUpMessage: o.wakeUpMessage || "你好，我是小源",
      exitMessage: o.exitMessage || "稍后再见",
      test: o.test || !1,
      voiceThreshold: o.voiceThreshold || 0.1,
      chatApi: o.chatApi || "/v1/chat/completions",
      voiceToTextApi: o.voiceToTextApi || "/rgApi/recognition",
      voiceAssetsDir: o.voiceAssetsDir || "/voiceAssets",
      voiceBroadcast: ho(o.voiceBroadcast, {
        api: "/ttsApi/tts",
        refAudioPath: "/home/<USER>/project/ai/sovits/77.mp3",
        textLang: "zh",
        promptLang: "zh",
        promptText: "在不了解的状况下喜欢上一个人，确发现那个人真实的一面比你想象的还要美好，这应该算的上幸运",
        textSplitMethod: "cut5",
        batchSize: 1,
        mediaType: "wav",
        streamingMode: !0
      })
    }, window.voiceHelperOptions = this.options, console.log("voiceHelperOptions", window.voiceHelperOptions), this.container = v, this.createUi(v), this.historyMsgChat = [], this.audioChunks = [], this.speakState = "stop", this.isShowDialogueContents = !1, this.speakGapTime = 2500, this.audioDom = document.createElement("audio"), this.pauseTimer, this.initVoiceWakeUp();
  }
  createUi(v) {
    let o = this.getHelperUiHtml(), m = l1("div", "voice-helper", o);
    m.appendChild(this.addStyle()), this.domEl = m, v.appendChild(m), this.initEvent();
  }
  updatedDialogueHtml() {
    if (!this.domEl) return;
    let v = this.domEl.querySelector(".dialogue-contents");
    v && (v.innerHTML = "", this.createDialogueItems(v, this.historyMsgChat));
  }
  createDialogueItems(v, o) {
    console.log("createDialogueItems", o);
    let m = "";
    o.forEach((b) => {
      m += `
          <div class='dialogue-item ${b.role === "assistant" ? "assistant" : ""}''>
            <div class='item-profile-icon'> </div>
            <div class='item-text'> ${b.content} </div>
          </div>
        `;
    }), v.innerHTML = m;
  }
  initEvent() {
    let v = this.domEl.querySelector(".contents-switch-btn");
    v && v.addEventListener("click", lo(this.onCilickSwitchBtn, this));
    let o = this.domEl.querySelector(".voice-btn-icon");
    o && o.addEventListener("click", lo(this.onCilickVoiceBtn, this));
  }
  async onCilickVoiceBtn() {
    ["stop"].includes(this.speakState) ? this.startSpeak() : this.exitSpeak();
  }
  async startSpeak() {
    this.isWakeUp && this.clearWakeUp(), this.speakState === "stop" && this.domEl.classList.add("active"), await this.startVoice();
  }
  exitSpeak() {
    this.domEl.classList.remove("active"), this.stopVoice(), this.playTextVoice(this.options.exitMessage);
  }
  onCilickSwitchBtn() {
    let v = this.domEl.querySelector(".contents-switch-btn");
    this.isShowDialogueContents ? (v.classList.remove("active"), this.hideDialogueContents()) : (v.classList.add("active"), this.updatedDialogueHtml(), this.showDialogueContents());
  }
  showDialogueContents() {
    let v = this.domEl.querySelector(".dialogue-contents");
    this.isShowDialogueContents = !0, v.style.display = "block";
  }
  hideDialogueContents() {
    let v = this.domEl.querySelector(".dialogue-contents");
    this.isShowDialogueContents = !1, v.style.display = "none";
  }
  getHelperUiHtml() {
    return `
      <div class='voice-btn'> 
        <div class='decorate-circle-1'></div>
        <div class='decorate-circle-2'></div>
        <div class='voice-btn-icon'> </div>
      </div>
      <div class='contents-switch-btn'></div>
      <div class='dialogue-contents' style='display: ${this.isShowDialogueContents ? "block" : "none"}'> 
      </div>
    `;
  }
  addStyle() {
    let v = document.createElement("style");
    return v.type = "text/css", v.innerHTML = `
      .voice-btn-icon {
        background-image: url(${ir("voice", "#ffffff")});
      }
      .contents-switch-btn {  
        background-image: url(${ir("arrowTop", "#ffffff")});
      }
      .item-profile-icon {
          background-image: url(${ir("voice", "#ffffff")});
      }
      .assistant .item-profile-icon {
        background-image: url(${ir("user", "#ffffff")});
      }
      `, v;
  }
  async addChat(v) {
    var dn, Xn;
    let o = {
      role: "user",
      content: v
    };
    this.historyMsgChat.push(o);
    let m = {
      role: "assistant",
      content: S1
    };
    this.historyMsgChat.push(m), this.updatedDialogueHtml();
    let b = await so.aiChat({
      messages: [{ role: "user", content: v }],
      model: "qwen2.5-instruct"
      // tools: ["device_split", "device_status", "device_part", "device_health","device_introduce", "device_design_paper", "device_general"]
    });
    console.log("aiChat-res", b);
    let G, T = (Xn = (dn = b == null ? void 0 : b.choices[0]) == null ? void 0 : dn.message) == null ? void 0 : Xn.content;
    if (T) {
      try {
        if (T = JSON.parse(T), this.options.test && T.command) {
          let ut = tmpeText[T.command];
          ut && (T.reply = ut);
        }
      } catch {
        T = {
          reply: T
        };
      }
      T.command && T.command.length > 0 && this.dispatchSpeakingEvent(T.reply, T.command, T.params), G = T.reply;
    }
    G && (m.content = G, this.updatedDialogueHtml(), await this.playTextVoice(G));
  }
  async playTextVoice(v) {
    console.log("playTextVoice", v);
    let { api: o, refAudioPath: m, textLang: b, promptLang: G, promptText: T, textSplitMethod: dn, batchSize: Xn, mediaType: ut, streamingMode: dt } = this.options.voiceBroadcast;
    const Mn = `${o}?text=${v}&text_lang=${b}&ref_audio_path=${m}&prompt_lang=${G}&prompt_text=${T}&text_split_method=${dn}&batch_size=${Xn}&media_type=${ut}&streaming_mode=${dt}`;
    this.audioDom.src = Mn, this.audioDom.play(), await new Promise((se) => {
      this.audioDom.onended = () => {
        console.log("audioDom.onended"), se(!0);
      };
    });
  }
  // async startRecording() {
  //   console.log('startRecording')
  //   try {
  //     // 获取麦克风权限并开始录音
  //     const stream = await navigator.mediaDevices.getUserMedia({
  //       audio: true,
  //     });
  //     this.mediaRecorder = new MediaRecorder(stream);
  //     // 监听录音数据
  //     this.mediaRecorder.ondataavailable = (event) => {
  //       this.audioChunks.push(event.data);
  //     };
  //     // 录音结束时处理数据
  //     this.mediaRecorder.onstop = async () => {
  //       // 创建 Blob 对象，格式为 OGG/Opus
  //       const blob = new Blob(this.audioChunks, {
  //         type: "audio/ogg; codecs=opus",
  //       });
  //       this.audioChunks = []; // 清空录音数据
  //       // 调用语音转文字接口
  //       await this.speechToText(blob);
  //     };
  //     // 开始录音
  //     this.mediaRecorder.start();
  //     this.isRecording = true;
  //   } catch (error) {
  //     console.error("录音失败：", error);
  //   }
  // }
  async voiceToText(v) {
    console.log(v, "blob");
    try {
      const m = (await so.voiceToText(v)).data;
      return console.log("语音转文字结果：", m), m;
    } catch (o) {
      console.error("语音转文字失败：", o);
    }
  }
  async initVoice() {
    try {
      const v = await async function() {
        const T = window.navigator.mediaDevices;
        if (T != null && T.getUserMedia)
          return await T.getUserMedia({ audio: !0 }).then((dn) => dn).catch((dn) => {
            console.log("拒绝", dn);
          });
        console.error("浏览器不支持 getUserMedia");
      }(), o = new MediaRecorder(v), m = new (window.AudioContext || window.webkitAudioContext)();
      await m.audioWorklet.addModule(this.options.voiceAssetsDir + "/volume-meter-processor.js");
      const b = new AudioWorkletNode(m, "volume-meter");
      m.createMediaStreamSource(v).connect(b), b.port.onmessage = (T) => this.onSpeaking(T), this.mediaStream = o, this.audioContext = m, this.AudioWorklet = b, this.initMediaStreamEvent(o), o.onerror = (T) => {
        console.log(T, "mediaStream=err");
      };
    } catch (v) {
      console.log(v, "catch===================>");
    }
  }
  initMediaStreamEvent(v) {
    v.ondataavailable = (o) => {
      v.onstop = async (m) => {
        if (console.log("mediaStream.onstop", m), this.speakState === "pause") {
          const b = new Blob([o.data], { type: "audio/ogg; codecs=opus" });
          let G = await this.voiceToText(b);
          if (G) {
            if (co(G, this.options.exitVoiceKeywords)) {
              this.exitSpeak(), this.options.wakeUpKeywords && this.initVoiceWakeUp();
              return;
            }
            await this.addChat(G);
          }
          this.startVoice();
        }
      };
    };
  }
  onSpeaking(v) {
    if (this.speakState === "pause") return;
    v.data.volume > this.options.voiceThreshold && (this.pauseTimer && clearTimeout(this.pauseTimer), this.pauseTimer = setTimeout(() => this.pauseVoice(), this.speakGapTime));
  }
  pauseVoice() {
    this.mediaStream && (this.mediaStream.stop(), console.log("mediaStream.pause", this.mediaStream)), this.audioContext && this.audioContext.suspend(), this.playTipsVoice(), this.speakState = "pause", console.log("已暂停监听麦克风");
  }
  cancelPauseVoice() {
    this.audioContext && this.audioContext.resume(), this.mediaStream && this.mediaStream.start(), this.speakState = "start";
  }
  stopVoice() {
    this.mediaStream && (this.mediaStream.stop(), this.mediaStream = null), this.AudioWorklet && (this.AudioWorklet.disconnect(), this.AudioWorklet = null), this.audioContext && (this.audioContext.close(), this.audioContext = null), this.speakState = "stop", console.log("已停止监听麦克风");
  }
  async startVoice() {
    console.log("startVoice", this.speakState), this.speakState === "stop" && this.options.wakeUpMessage && this.playTextVoice(this.options.wakeUpMessage), await this.initVoice(), this.mediaStream.start(), this.speakState = "start", console.log("已开始监听麦克风");
  }
  // initWatchSpeaking() {
  //   let scriptProcessor = this.scriptProcessor;
  //   let audioContext = this.audioContext;
  //   let mediaStream = this.mediaStream;
  //   if (this.speakState !== 'stop' ) {
  //     scriptProcessor.connect(audioContext.destination);
  //     scriptProcessor.onaudioprocess = async (e) => {
  //         const buffer = e.inputBuffer.getChannelData(0);
  //         // 获取缓冲区中最大的音量值
  //         // startWaveAnimation()
  //         const maxVal = Math.max.apply(Math, buffer);
  //         // ['aStop', 'oStart'].includes(controls) && drawWave(Math.round(maxVal * 20));
  //         if (Math.round(maxVal * 100) > 25 && mediaStream.state === 'recording') {
  //             // setControls('aStop');
  //             this.speakState === 'pause'
  //             console.log('暂停')
  //             // await new Promise((resolve) => task(() => resolve(mediaStream.stop())));
  //             // mediaStream?.start();
  //         }
  //     }
  //   } else {
  //       scriptProcessor?.disconnect();
  //   }
  // }
  initVoiceWakeUp() {
    console.log("initVoiceWakeUp"), "webkitSpeechRecognition" in window ? (this.recognition = new webkitSpeechRecognition(), this.recognition.continuous = !0, this.recognition.interimResults = !0, this.recognition.lang = "zh-CN", this.isWakeUp = !0, this.recognition.onresult = (v) => {
      const o = Array.from(v.results).map((b) => b[0].transcript).join("");
      if (console.log("transcript", o), !this.isWakeUp) return;
      co(o, this.options.wakeUpKeywords) && (console.log("检测到唤醒词：", this.options.wakeUpKeyword), this.recognition.stop(), this.speakState === "stop" && this.startSpeak());
    }, this.recognition.onerror = (v) => {
      v.error === "no-speech" ? (this.recognition.stop(), setTimeout(() => {
        this.recognition.start();
      }, 100), console.log("no-speech, 重新开始监听唤醒词")) : console.error("语音识别错误:", v.error);
    }, this.recognition.onend = () => {
      console.log("onend"), this.isWakeUp = !1;
    }, setTimeout(() => {
      this.recognition.start();
    }, 500)) : console.error("浏览器不支持语音识别");
  }
  clearWakeUp() {
    this.isWakeUp = !1, this.recognition.stop();
  }
  dispatchSpeakingEvent(v, o, m) {
    this.dispatchEvent({
      type: "chatReply",
      reply: v,
      command: o,
      params: m
    });
  }
  playTipsVoice() {
    this.audioDom.src = this.options.voiceAssetsDir + "/tips.mp3", this.audioDom.play();
  }
}
function co(z, v) {
  for (let o of v) {
    let m = o.split("%%"), b, G = -1;
    if (m.forEach((T, dn) => {
      b === void 0 && (z.indexOf(T) > -1 ? m.length === 1 ? b = !0 : m.indexOf(T) > G && (G = m.indexOf(T), m.length - 1 === dn && (b = !0)) : b = !1);
    }), b) return !0;
  }
}
export {
  y1 as VoiceHelper
};
