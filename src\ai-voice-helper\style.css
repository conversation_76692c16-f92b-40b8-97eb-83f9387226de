.voice-helper {
    position: absolute;
    bottom  : 0;
    left    : 0
}

.voice-helper .decorate-circle-1,
.voice-helper .decorate-circle-2 {
    width        : 100%;
    height       : 100%;
    position     : absolute;
    top          : 0;
    left         : 0;
    border-radius: 50%;
    border       : 2px solid rgb(224, 119, 20);
    box-sizing   : border-box;
    scale        : .9
}

.voice-helper .voice-btn {
    width : 70px;
    height: 70px
}

.voice-helper .voice-btn .voice-btn-icon {
    width              : 100%;
    height             : 100%;
    border-radius      : 50%;
    background-color   : #0b86eb;
    cursor             : pointer;
    position           : absolute;
    top                : 0;
    left               : 0;
    z-index            : 1;
    box-shadow         : 0 0 22px #0000004d;
    background-size    : 60%;
    background-repeat  : no-repeat;
    background-position: center
}

.voice-helper .contents-switch-btn {
    width              : 20px;
    height             : 20px;
    background-color   : #000;
    position           : absolute;
    bottom             : 3px;
    left               : calc(100% - 26px);
    cursor             : pointer;
    border-radius      : 50%;
    box-shadow         : 6px 6px 16px #0000001a;
    background-size    : 80%;
    background-repeat  : no-repeat;
    background-position: center;
    transform          : rotate(90deg);
    transition         : transform .2s;
    z-index            : 2;
    border             : 4px solid rgb(11, 134, 235)
}

.voice-helper .contents-switch-btn.active {
    background-color: #f5cf27;
    transform       : rotate(180deg)
}

.voice-helper .dialogue-contents {
    max-height             : 30vh;
    box-shadow             : 0 2px 12px #00000026;
    position               : absolute;
    bottom                 : 0;
    border                 : 3px solid rgb(72, 130, 255);
    border-radius          : 16px;
    padding                : 10px;
    left                   : calc(100% + 10px);
    width                  : 340px;
    font-size              : 14px;
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter        : blur(16px);
    background-color       : #0000001f;
    overflow               : auto
}

.voice-helper .dialogue-contents .dialogue-item {
    display: flex
}

.voice-helper .dialogue-contents .dialogue-item .item-profile-icon {
    width              : 20px;
    height             : 20px;
    background-color   : #2988e0;
    margin-top         : 10px;
    border-radius      : 10px;
    background-position: center;
    background-size    : 85%;
    background-repeat  : no-repeat
}

.voice-helper .dialogue-contents .dialogue-item .item-text {
    width  : calc(100% - 40px);
    padding: 10px
}

.voice-helper .dialogue-contents .dialogue-item.assistant {
    flex-direction: row-reverse
}

.voice-helper .dialogue-contents .dialogue-item.assistant .item-text {
    background   : #ffffffe1;
    width        : calc(100% - 60px);
    margin-right : 10px;
    border-radius: 6px;
    color        : #000
}

.voice-helper .dialogue-contents .dialogue-item.assistant .item-profile-icon {
    margin-right: 0
}

.voice-helper.active .voice-btn-icon {
    background-color: #fa7923
}

.voice-helper.active .decorate-circle-1 {
    animation: circleScale 1.8s infinite
}

.voice-helper.active .decorate-circle-2 {
    animation      : circleScale 1.8s infinite;
    animation-delay: .25s
}

.voice-helper.active .contents-switch-btn {
    border    : 4px solid rgb(250, 121, 35);
    box-shadow: -6px -6px 16px #0000001a
}

@keyframes waveAnimation {
    0% {
        scale  : 0;
        opacity: 1
    }

    to {
        scale  : 2;
        opacity: 0
    }
}

@keyframes circleScale {
    0% {
        transform: scale(.8);
        opacity  : 1;
        border   : 1px solid rgb(224, 119, 20)
    }

    to {
        transform: scale(1.6);
        opacity  : 0;
        border   : 3px solid rgb(224, 119, 20)
    }
}